package com.ruoyi.patroltidb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheckDetail;
import com.ruoyi.patroltidb.mapper.PatrolTunnelCheckDetailMapper;
import com.ruoyi.patroltidb.service.PatrolTunnelCheckDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 隧道巡检查子Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Service
@Slave
public class
PatrolTunnelCheckDetailServiceImpl extends ServiceImpl<PatrolTunnelCheckDetailMapper, PatrolTunnelCheckDetail> implements PatrolTunnelCheckDetailService {

//    @Autowired
//    private PatrolTunnelCheckDetailMapper patrolTunnelCheckDetailMapper;

    @Override
    public List<PatrolTunnelCheckDetail> findListByParam(Map<String, Object> params) {
        return baseMapper.findListByParam(params);
    }


}
