package com.ruoyi.patroltidb.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 机电故障上报对象 patrol_device_fault_record
 * 
 * <AUTHOR>
 * @date 2024-08-29
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="机电故障上报")
@TableName("patrol_device_fault_record")
@Data
public class PatrolDeviceFaultRecord extends PatrolDeviceBase {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *  递增序号
     */
    @TableField(exist = false)
    @Excel(name = "序号", sort = 0)
    private Integer orderNumber;

    /** 故障位置 */
    @Excel(name = "故障位置", sort = 12)
    @ApiModelProperty(value = "故障位置")
    private String faultLocation;

    /** 编号 */
    @Excel(name = "编号", type = Excel.Type.IMPORT)
    @ApiModelProperty(value = "编号")
    private String code;

    /** 故障时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "故障时间", width = 30, dateFormat = "yyyy-MM-dd", sort = 6)
    @ApiModelProperty(value = "故障时间")
    private Date reportTime;

    /** 修复时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修复时间", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.IMPORT)
    @ApiModelProperty(value = "修复时间")
    private Date repairTime;

    /** 备注 */
    @Excel(name = "备注", type = Excel.Type.IMPORT)
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 修复标记 */
    @Excel(name = "修复标记", type = Excel.Type.IMPORT)
    @ApiModelProperty(value = "修复标记")
    private Integer repairFlag;

//    @Excel(name = "年份", type = Excel.Type.EXPORT, sort = 6)
    @ApiModelProperty(value = "年份")
    @TableField(exist = false)
    private Integer year;

//    @Excel(name = "月份", type = Excel.Type.EXPORT, sort = 7)
    @ApiModelProperty(value = "月份")
    @TableField(exist = false)
    private Integer month;

//    @Excel(name = "故障数量", type = Excel.Type.EXPORT, sort = 8)
    @ApiModelProperty(value = "故障数量")
    @TableField(exist = false)
    private Integer faultNum;

//    @Excel(name = "修复数量")
    @ApiModelProperty(value = "修复数量")
    @TableField(exist = false)
    private Integer repairNum;


}
