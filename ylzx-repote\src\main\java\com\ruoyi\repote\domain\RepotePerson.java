package com.ruoyi.repote.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;

import java.io.Serial;

/**
 * 填报人员对象 repote_person
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="填报人员")
@TableName("repote_person")
@Data
public class RepotePerson extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 填报用户ID */
    @Excel(name = "填报用户ID")
    @ApiModelProperty(value = "填报用户ID")
    private Long userId;

    /** 填报人员名字 */
    @Excel(name = "填报人员名字")
    @ApiModelProperty(value = "填报人员名字")
    private String userName;

    /** 任务ID */
    @Excel(name = "任务ID")
    @ApiModelProperty(value = "任务ID")
    private String missionId;

}
