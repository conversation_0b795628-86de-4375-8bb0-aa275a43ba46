package com.ruoyi.patrol.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.patrol.domain.PatrolPartsInfo;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.mapper.PatrolPartsInfoMapper;
import com.ruoyi.patrol.service.PatrolPartsInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 检查类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
@Master
public class PatrolPartsInfoServiceImpl extends ServiceImpl<PatrolPartsInfoMapper, PatrolPartsInfo> implements PatrolPartsInfoService {
    @Resource
    private RedisService redisService;

    /**
     * 根据传入的类型查询检查部位
     * @param partsType 类型(1:桥梁日常巡查;2:桥梁经常检查;3:涵洞定期检查;4:涵洞经常检查;5:隧道日常巡查;6:隧道经常检查;)
     * @return List<PatrolPartsInfo>
     */
    @Override
    public List<PatrolPartsInfo> selectByType(InspectionType partsType) {
        String redisKey = "patrol:parts:info:" + partsType;
        
        // 尝试从Redis获取
        List<PatrolPartsInfo> result = redisService.getCacheObject(redisKey);
        if (result != null && !result.isEmpty()) {
            return result;
        }

        // Redis中不存在，从数据库查询
        QueryWrapper<PatrolPartsInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parts_type", partsType);
        result = this.list(queryWrapper);

        // 存入Redis，设置24小时过期
        redisService.setCacheObject(redisKey, result, 24L, TimeUnit.HOURS);

        return result;
    }

}
