package com.ruoyi.repote.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.repote.domain.RepoteConfigure;

/**
 * 填报配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface RepoteConfigureService extends IService<RepoteConfigure> {

    /**
     * 根据条件查询填报配置数据列表
     * @param params
     */
    List<RepoteConfigure> findListByParam(Map<String, Object> params);

}
