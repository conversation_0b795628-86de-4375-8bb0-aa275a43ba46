<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patroltidb.mapper.PatrolDeviceFaultRecordMapper">

    <resultMap type="com.ruoyi.patroltidb.domain.PatrolDeviceFaultRecord" id="PatrolDeviceFaultRecordResult">
            <result property="checkId" column="check_id"/>
            <result property="devName" column="dev_name"/>
            <result property="location" column="location"/>
            <result property="faultLocation" column="fault_location"/>
            <result property="describe" column="describe"/>
            <result property="measures" column="measures"/>
            <result property="code" column="code"/>
            <result property="reportTime" column="report_time"/>
            <result property="repairTime" column="repair_time"/>
            <result property="remark" column="remark"/>
            <result property="repairFlag" column="repair_flag"/>
    </resultMap>

    <sql id="base_column">
        check_id, dev_name, location, fault_location, describe, measures, code, report_time, repair_time, remark, repair_flag    </sql>

    <sql id="where_column">
        <if test="checkId != null and checkId != ''">
            AND check_id = #{checkId}
        </if>
        <if test="checkIdLike != null and checkIdLike != ''">
            AND check_id like CONCAT('%', #{checkIdLike}, '%')
        </if>
        <if test="devName != null and devName != ''">
            AND dev_name = #{devName}
        </if>
        <if test="location != null and location != ''">
            AND location = #{location}
        </if>
        <if test="faultLocation != null and faultLocation != ''">
            AND fault_location = #{faultLocation}
        </if>
        <if test="describe != null and describe != ''">
            AND describe = #{describe}
        </if>
        <if test="measures != null and measures != ''">
            AND measures = #{measures}
        </if>
        <if test="code != null and code != ''">
            AND code = #{code}
        </if>
        <if test="reportTime != null and reportTime != ''">
            AND report_time = #{reportTime}
        </if>
        <if test="reportTimes != null and reportTimes != ''">
            AND date_format(report_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(#{reportTimes}, '%Y-%m-%d')
        </if>
        <if test="reportTimee != null and reportTimee != ''">
            AND date_format(report_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(#{reportTimee}, '%Y-%m-%d')
        </if>
        <if test="repairTime != null and repairTime != ''">
            AND repair_time = #{repairTime}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}
        </if>
        <if test="repairFlag != null and repairFlag != ''">
            AND repair_flag = #{repairFlag}
        </if>
    </sql>

    <sql id="set_column">
            <if test="checkId != null">
                check_id = #{checkId},
            </if>
            <if test="devName != null">
                dev_name = #{devName},
            </if>
            <if test="location != null">
                location = #{location},
            </if>
            <if test="faultLocation != null">
                fault_location = #{faultLocation},
            </if>
            <if test="describe != null">
                describe = #{describe},
            </if>
            <if test="measures != null">
                measures = #{measures},
            </if>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="reportTime != null">
                report_time = #{reportTime},
            </if>
            <if test="repairTime != null">
                repair_time = #{repairTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="repairFlag != null">
                repair_flag = #{repairFlag},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="PatrolDeviceFaultRecordResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_device_fault_record
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="PatrolDeviceFaultRecordResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_device_fault_record
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

</mapper>