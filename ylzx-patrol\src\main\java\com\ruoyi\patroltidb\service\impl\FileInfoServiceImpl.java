package com.ruoyi.patroltidb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.patroltidb.domain.FileInfoEntity;
import com.ruoyi.patroltidb.mapper.FileInfoMapper;
import com.ruoyi.patroltidb.service.FileInfoService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class FileInfoServiceImpl extends ServiceImpl<FileInfoMapper, FileInfoEntity> implements FileInfoService {


    @Override
    public Long insertBatch(List<FileInfoEntity> list){
        return baseMapper.insertBatch(list);
    }


}
