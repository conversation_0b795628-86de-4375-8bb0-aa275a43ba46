package com.ruoyi.patrol.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.CommonResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.manage.api.domain.RemoteRoadDiseaseResponse;
import com.ruoyi.manage.api.service.RemoteRoadDiseaseService;
import com.ruoyi.patrol.domain.PatrolInspectionLogs;
import com.ruoyi.patrol.domain.dto.AssetStatsDTO;
import com.ruoyi.patrol.domain.vo.*;
import com.ruoyi.patrol.service.PatrolInspectionLogsService;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheck;
import com.ruoyi.patroltidb.domain.PatrolCulvertCheck;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheck;
import com.ruoyi.patroltidb.service.PatrolBridgeCheckService;
import com.ruoyi.patroltidb.service.PatrolCulvertCheckService;
import com.ruoyi.patroltidb.service.PatrolTunnelCheckService;
import com.ruoyi.system.api.RemoteMaintenanceSectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Api(tags = "巡检查专题统计接口")
@Log4j2
@RestController
@RequestMapping("/patrolCount")
public  class PatrolCountController extends BaseController {

    @Resource
    private RedisService redisService;
    @Resource
    private PatrolInspectionLogsService patrolInspectionLogsService;
    @Resource
    private RemoteRoadDiseaseService remoteRoadDiseaseService;
    @Resource
    private PatrolBridgeCheckService patrolBridgeCheckService;
    @Resource
    private PatrolTunnelCheckService patrolTunnelCheckService;
    @Resource
    private PatrolCulvertCheckService patrolCulvertCheckService;
    @Resource
    private RemoteMaintenanceSectionService maintenanceSectionService;

    /**
     * 日常巡查
     */
    @ApiOperation(value = "日常巡查统计")
    @GetMapping(value = "/dailyPatrolCount")
    public CommonResult<DailyPatrolVO> dailyPatrolCount() {
        DailyPatrolVO vo = new DailyPatrolVO();
        //查询当天巡查日志
        List<String> maintenanceIds = maintenanceSectionService.findUserMaintenanceIds(null).getData();
        List<PatrolInspectionLogs> logsList = patrolInspectionLogsService.findListByUserAndTime(maintenanceIds, null, null, 1);
        // logs根据maintenanceSectionId去重 保留最新的一条
        List<PatrolInspectionLogs> distinctLogs = logsList.stream()
                .collect(Collectors.toMap(PatrolInspectionLogs::getMaintenanceSectionId, log -> log, (log1, log2) -> log1))
                .values().stream().toList();

        BigDecimal patrolMileage = distinctLogs.stream()
                .map(PatrolInspectionLogs::getPatrolMileage)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        R<List<RemoteRoadDiseaseResponse>> listR = remoteRoadDiseaseService.getRoadDiseaseListByIdList(logsList.stream().map(PatrolInspectionLogs::getId).collect(Collectors.toList()));
        if (listR.getCode() == 200 && listR.getData() != null) {
            vo.setDiseaseNum(listR.getData().size());
        } else {
            vo.setDiseaseNum(0);
        }
        vo.setPatrolNum(logsList.size());
        vo.setPatrolMileage(patrolMileage.divide(BigDecimal.valueOf(1), 2, RoundingMode.HALF_UP));
        if (vo.getPatrolNum() > 0) {
            vo.setEffPatrolRate(BigDecimal.valueOf(vo.getDiseaseNum()).divide(BigDecimal.valueOf(vo.getPatrolNum()), 2, RoundingMode.HALF_UP));
        } else {
            vo.setEffPatrolRate(BigDecimal.ZERO);
        }
        return CommonResult.success(vo);
    }


    /**
     * 桥梁经常检查
     */
    @ApiOperation(value = "桥梁经常检查统计")
    @GetMapping(value = "/bridgeOftenCount")
    public CommonResult<AssetOftenPatrolVO> bridgeOftenCount() {
        AssetOftenPatrolVO vo = new AssetOftenPatrolVO();
        AssetStatsDTO assetStats = patrolInspectionLogsService.getAssetStats(1);
        QueryWrapper<PatrolBridgeCheck> qw = new QueryWrapper<>();
        qw.eq("type", 2);
        qw.eq("stage", 1);
        long count = patrolBridgeCheckService.count(qw);
        vo.setDiseaseNum((int) count);
        vo.setToBeCheckedNum(assetStats.getPendingCount());
        vo.setCheckedNum(assetStats.getInspectedCount());
        vo.setCheckedAccountFor(BigDecimal.valueOf(assetStats.getInspectedCount()).divide(BigDecimal.valueOf(assetStats.getTotalCount()), 6, RoundingMode.HALF_UP));
        return CommonResult.success(vo);
    }

    /**
     * 隧道经常检查统计
     */
    @ApiOperation(value = "隧道经常检查统计")
    @GetMapping(value = "/tunnelOftenCount")
    public CommonResult<AssetOftenPatrolVO> tunnelOftenCount() {
        AssetOftenPatrolVO vo = new AssetOftenPatrolVO();
        AssetStatsDTO assetStats = patrolInspectionLogsService.getAssetStats(2);
        QueryWrapper<PatrolTunnelCheck> qw = new QueryWrapper<>();
        qw.eq("type", 6);
        qw.eq("stage", 1);
        long count = patrolTunnelCheckService.count(qw);
        vo.setDiseaseNum((int) count);
        vo.setCheckedNum(assetStats.getInspectedCount());
        vo.setToBeCheckedNum(assetStats.getPendingCount());
        vo.setCheckedAccountFor(BigDecimal.valueOf(assetStats.getInspectedCount()).divide(BigDecimal.valueOf(assetStats.getTotalCount()), 6, RoundingMode.HALF_UP));
        return CommonResult.success(vo);
    }


    /**
     * 涵洞经常检查统计
     */
    @ApiOperation(value = "涵洞经常检查统计")
    @GetMapping(value = "/culvertOftenCount")
    public CommonResult<AssetOftenPatrolVO> culvertOftenCount() {
        AssetOftenPatrolVO vo = new AssetOftenPatrolVO();
        AssetStatsDTO assetStats = patrolInspectionLogsService.getAssetStats(3);
        QueryWrapper<PatrolCulvertCheck> qw = new QueryWrapper<>();
        qw.eq("type", 4);
        qw.eq("stage", 1);
        long count = patrolCulvertCheckService.count(qw);
        vo.setDiseaseNum((int) count);
        vo.setCheckedNum(assetStats.getInspectedCount());
        vo.setToBeCheckedNum(assetStats.getPendingCount());
        vo.setCheckedAccountFor(BigDecimal.valueOf(assetStats.getInspectedCount()).divide(BigDecimal.valueOf(assetStats.getTotalCount()), 6, RoundingMode.HALF_UP));
        return CommonResult.success(vo);
    }

    /**
     * 各管理处桥梁经常检查情况
     */
    @ApiOperation(value = "各管理处桥梁经常检查情况")
    @GetMapping(value = "/deptBridgeOftenCountList")
    public CommonResult<List<AssetOftenPatrolVO>> deptBridgeOftenCountList() {
        return CommonResult.success(patrolInspectionLogsService.getDeptAssetOftenCountList(1));
    }

    /**
     * 各管理处隧道经常检查情况
     */
    @ApiOperation(value = "各管理处隧道经常检查情况")
    @GetMapping(value = "/deptTunnelOftenCountList")
    public CommonResult<List<AssetOftenPatrolVO>> deptTunnelOftenCountList() {
        return CommonResult.success(patrolInspectionLogsService.getDeptAssetOftenCountList(2));
    }

    /**
     * 各管理处涵洞经常检查情况
     */
    @ApiOperation(value = "各管理处涵洞经常检查情况")
    @GetMapping(value = "/deptCulvertOftenCountList")
    public CommonResult<List<AssetOftenPatrolVO>> deptCulvertOftenCountList() {
        return CommonResult.success(patrolInspectionLogsService.getDeptAssetOftenCountList(3));
    }

    /**
     * 各管理处巡查里程统计
     */
    @ApiOperation(value = "各管理处巡查里程统计")
    @GetMapping(value = "/deptPatrolMileageCount/{type}")
    public CommonResult<List<InspectionGroupCountVO>> deptPatrolMileageCount(@PathVariable Integer type) {
        int year = LocalDate.now().getYear();
        return CommonResult.success(patrolInspectionLogsService.deptPatrolMileageCount(year, type));
    }

    /**
     * 各管理处在线巡查车数量
     */
    @ApiOperation("各管理处在线巡查车数量")
    @GetMapping("/getDeptRealCoordinateCat")
    public CommonResult<List<DeptToCarVO>> getDeptRealCoordinateCat() {
        List<RealCoordinateLogVO> list = redisService.getCacheObjectsByPattern("REAL_COORDINATE*");
        if (list != null && !list.isEmpty()) {
            // 添加三条AI巡查车假数据
//            add(list);
            List<DeptToCarVO> voList = list.stream().collect(Collectors.groupingBy(RealCoordinateLogVO::getDeptId))
                    .entrySet().stream().map(entry -> {
                        Long deptId = entry.getKey();
                        List<RealCoordinateLogVO> group = entry.getValue();
                        String deptName = group.get(0).getDeptName();
                        int carNum = group.size();
                        return new DeptToCarVO(deptId, deptName, carNum);
                    })
                    .collect(Collectors.toList());
            return CommonResult.success(voList);
        } else {
            return CommonResult.success(new ArrayList<>());
        }
    }

    /**
     * 在线巡查车列表
     */
    @ApiOperation("在线巡查车列表")
    @GetMapping("/getRealCoordinateCatList")
    public CommonResult<List<RealCoordinateLogVO>> getRealCoordinateCatList(@RequestParam(name = "deptId",required = false) Long deptId) {
        List<RealCoordinateLogVO> list = redisService.getCacheObjectsByPattern("REAL_COORDINATE*");
        // 添加三条AI巡查车假数据
//        add(list);
        if (deptId != null) {
            list = list.stream().filter(i -> i.getDeptId().equals(deptId)).collect(Collectors.toList());
        }

        return CommonResult.success(list);
    }

    private static void add(List<RealCoordinateLogVO> list) {
        list.add(RealCoordinateLogVO.builder()
                .logId("1910527255055065089")
                .deptId(70L)
                .deptName("昆明西管理处")
                .maintenanceSectionName("永武高速")
                .carNum("云A050KX")
                .startTime(Date.from(LocalDateTime.now().minusHours(1).atZone(ZoneId.systemDefault()).toInstant()))
                .status(0)
                .personnelName("王建,夏燕飞,杨涛")
                .phoneNumber("")
                .patrolMileage(new BigDecimal("10437.90"))
                .catType(2)
                .lastPoint("POINT (101.94872853939553 25.545914133810737)")
                .videoUrl("https://zhyhpt.yciccloud.com:9000/webApp/yanghu/1.mp4").build());
        list.add(RealCoordinateLogVO.builder()
                .logId("1910563770145595393")
                .deptId(94L)
                .deptName("保山管理处")
                .maintenanceSectionName("腾猴高速")
                .carNum("云AM781Z")
                .startTime(Date.from(LocalDateTime.now().minusHours(1).atZone(ZoneId.systemDefault()).toInstant()))
                .status(0)
                .personnelName("何忠意,何双权")
                .phoneNumber("")
                .patrolMileage(new BigDecimal("57738.54"))
                .catType(2)
                .lastPoint("POINT (98.215436794371 25.284335855606667)")
                .videoUrl("https://zhyhpt.yciccloud.com:9000/webApp/yanghu/2.mp4").build());
        list.add(RealCoordinateLogVO.builder()
                .logId("1910487387797086209")
                .deptId(123L)
                .deptName("普洱管理处")
                .maintenanceSectionName("墨临高速[普洱]")
                .carNum("云A5YY76")
                .startTime(Date.from(LocalDateTime.now().minusHours(1).atZone(ZoneId.systemDefault()).toInstant()))
                .status(0)
                .personnelName("黎春宏,陈云")
                .phoneNumber("")
                .patrolMileage(new BigDecimal("56641.60"))
                .catType(2)
                .lastPoint("POINT (100.9154417723892 24.12255359266547)")
                .videoUrl("https://zhyhpt.yciccloud.com:9000/webApp/yanghu/3.mp4").build());
    }


}


