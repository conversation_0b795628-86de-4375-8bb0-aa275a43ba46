<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.other.mapper.OtherTransportationMapper">

    <resultMap type="com.ruoyi.other.domain.OtherTransportation" id="OtherTransportationResult">
        <result property="id" column="id"/>
        <result property="projectName" column="project_name"/>
        <result property="vehicleCargoInfo" column="vehicle_cargo_info"/>
        <result property="submissionTime" column="submission_time"/>
        <result property="startingPoint" column="starting_point"/>
        <result property="endingPoint" column="ending_point"/>
        <result property="totalWeight" column="total_weight"/>
        <result property="axleLoad" column="axle_load"/>
        <result property="wheelBase" column="wheel_base"/>
        <result property="isPass" column="is_pass"/>
        <result property="replyNumber" column="reply_number"/>
        <result property="remarks" column="remarks"/>
        <result property="delFlag" column="delFlag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">
        id, project_name, vehicle_cargo_info, submission_time, starting_point, ending_point, total_weight, axle_load, wheel_base, is_pass, reply_number, remarks, create_time, update_time, create_by, update_by    </sql>

    <sql id="where_column">
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="statusLike != null and statusLike != ''">
            AND status like CONCAT('%', #{statusLike}, '%')
        </if>
        <if test="name != null and name != ''">
            AND name = #{name}
        </if>
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="totalWeight != null and totalWeight != ''">
            AND total_weight = #{totalWeight}
        </if>
        <if test="deptId != null and deptId != ''">
            AND dept_id = #{deptId}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name = #{deptName}
        </if>
        <if test="startTime != null and startTime != ''">
            AND update_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND update_time &lt;= #{endTime}
        </if>
    </sql>

    <sql id="set_column">
        <if test="status != null">
            status = #{status},
        </if>
        <if test="name != null">
            name = #{name},
        </if>
        <if test="userId != null">
            user_id = #{userId},
        </if>
        <if test="userName != null">
            user_name = #{userName},
        </if>
        <if test=" != null">
            dept_id = #{deptId},
        </if>
        <if test="deptName != null">
            dept_name = #{deptName},
        </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="OtherTransportationResult">
        SELECT
        <include refid="base_column"/>
        FROM other_transportation
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="OtherTransportationResult">
        SELECT
        <include refid="base_column"/>
        FROM other_transportation
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

<!--    <select id="findByTransportationIds" parameterType="java.util.List" resultType="com.ruoyi.other.domain.dto.OtherTransportationDTO">-->
<!--        SELECT-->
<!--        ot.*,-->
<!--        otd.transportation_id AS dept_transportation_id,-->
<!--        GROUP_CONCAT(DISTINCT otr.road_name) AS roadName,-->
<!--        GROUP_CONCAT(DISTINCT otd.dept_name) AS deptName,-->
<!--        otr.transportation_id AS road_transportation_id-->
<!--        FROM-->
<!--        other_transportation ot-->
<!--        LEFT JOIN other_transportation_dept otd ON ot.id = otd.transportation_id-->
<!--        LEFT JOIN other_transportation_road otr ON ot.id = otr.transportation_id-->
<!--        <where>-->
<!--            <if test="list != null and list.size() > 0">-->
<!--                AND ot.id IN-->
<!--                <foreach item="item" index="index" collection="list" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--        GROUP BY ot.id-->
<!--        ORDER BY ot.id-->
<!--        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">-->
<!--            limit #{pageNum},#{pageSize}-->
<!--        </if>-->
<!--    </select>-->
<!--    <select id="findByTransportationIds" resultType="com.ruoyi.other.domain.dto.OtherTransportationDTO">-->
<!--        SELECT-->
<!--        ot.*,-->
<!--        otd.transportation_id AS dept_transportation_id,-->
<!--        GROUP_CONCAT(DISTINCT otr.road_name) AS roadName,-->
<!--        GROUP_CONCAT(DISTINCT otd.dept_name) AS deptName,-->
<!--        otr.transportation_id AS road_transportation_id-->
<!--        FROM-->
<!--        other_transportation ot-->
<!--        LEFT JOIN-->
<!--        other_transportation_dept otd ON ot.id = otd.transportation_id-->
<!--        LEFT JOIN-->
<!--        other_transportation_road otr ON ot.id = otr.transportation_id-->
<!--        <where>-->
<!--            &lt;!&ndash; 引入其他条件 &ndash;&gt;-->
<!--            <include refid="where_column_ot"/>-->

<!--            &lt;!&ndash; userTranslationIds 判断 &ndash;&gt;-->
<!--            <if test="userTranslationIds != null and userTranslationIds.size() > 0">-->
<!--                AND ot.id IN-->
<!--                <foreach item="userTranslationId" collection="userTranslationIds" open="(" separator="," close=")">-->
<!--                    #{userTranslationId}-->
<!--                </foreach>-->
<!--            </if>-->

<!--            &lt;!&ndash; rodaSections 判断，处理数组 &ndash;&gt;-->
<!--            <if test="rodaSectionsArr != null and rodaSectionsArr.size() > 0">-->
<!--                AND otr.road_name IN-->
<!--                <foreach item="roadSection" collection="rodaSectionsArr" open="(" separator="," close=")">-->
<!--                    #{roadSection}-->
<!--                </foreach>-->
<!--            </if>-->

<!--            &lt;!&ndash; deptIds 判断 &ndash;&gt;-->
<!--            <if test="deptIds != null and deptIds.size() > 0">-->
<!--                AND otd.dept_id IN-->
<!--                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">-->
<!--                    #{deptId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--        GROUP BY ot.id-->
<!--    </select>-->
    <select id="findByTransportationIds" resultType="com.ruoyi.other.domain.dto.OtherTransportationDTO">
        SELECT
        ot.*,
        otd.transportation_id AS dept_transportation_id,
        GROUP_CONCAT(DISTINCT otr.road_name) AS roadName, <!-- 聚合所有路段 -->
        GROUP_CONCAT(DISTINCT otd.dept_name) AS deptName,
        otr.transportation_id AS road_transportation_id
        FROM
        other_transportation ot
        LEFT JOIN
        other_transportation_dept otd ON ot.id = otd.transportation_id
        LEFT JOIN
        other_transportation_road otr ON ot.id = otr.transportation_id
        <where>
            <!-- 引入其他条件 -->
            <include refid="where_column_ot"/>

            <!-- userTranslationIds 判断 -->
            <if test="userTranslationIds != null and userTranslationIds.size() > 0">
                AND ot.id IN
                <foreach item="userTranslationId" collection="userTranslationIds" open="(" separator="," close=")">
                    #{userTranslationId}
                </foreach>
            </if>

            <!-- 判断 rodaSectionsArr 是否存在，按 road_name 查询相关的 transportation_id -->
            <if test="rodaSectionsArr != null and rodaSectionsArr.size() > 0">
                AND ot.id IN (
                <!-- 先查询所有与 rodasection 相关的 transportation_id -->
                SELECT transportation_id
                FROM other_transportation_road
                WHERE road_name IN
                <foreach item="roadSection" collection="rodaSectionsArr" open="(" separator="," close=")">
                    #{roadSection}
                </foreach>
                )
            </if>

            <!-- 根据 deptIds 查询 -->
            <if test="deptIds != null and deptIds.size() > 0">
                AND otd.dept_id IN
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>
        GROUP BY ot.id

    </select>






    <select id="findAll" resultType="com.ruoyi.other.domain.dto.OtherTransportationDTO">
        SELECT
        ot.*,
        otd.transportation_id AS dept_transportation_id,
        GROUP_CONCAT(DISTINCT otr.road_name) AS roadName,
        GROUP_CONCAT(DISTINCT otd.dept_name) AS deptName,
        otr.transportation_id AS road_transportation_id
        FROM
        other_transportation ot
        LEFT JOIN other_transportation_dept otd ON ot.id = otd.transportation_id
        LEFT JOIN other_transportation_road otr ON ot.id = otr.transportation_id
        <where>
            <include refid="where_column_ot"/>
            <if test="userTranslationIds != null and list.size() > 0">
                AND ot.id IN
                <foreach item="userTranslationIds" index="index" collection="userTranslationIds" open="(" separator="," close=")">
                    #{userTranslationIds}
                </foreach>
            </if>
            <if test="rodaSections != null">
                AND otr.road_id IN(${rodaSections})
            </if>
            <if test="deptIds != null and deptIds != null">
                AND otd.dept_id IN
                <foreach collection="deptIds" item="deptIds" index="index" open="(" separator="," close=")">
                    #{deptIds}
                </foreach>
            </if>
        </where>
        GROUP BY ot.id
    </select>


    <sql id="where_column_ot">
        <if test="id != null and id != ''">
            AND ot.id = #{id}
        </if>
        <if test="deptId != null and deptId != ''">
            AND ot.dept_id = #{deptId}
        </if>
        <if test="submissionTimes != null and submissionTimes != ''">
            AND date_format(ot.submission_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(#{submissionTimes}, '%Y-%m-%d')
        </if>
        <if test="submissionTimee != null and submissionTimee != ''">
            AND date_format(ot.submission_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(#{submissionTimee}, '%Y-%m-%d')
        </if>
        <if test="totalWeight != null and totalWeight != ''">
            AND ot.total_weight = #{totalWeight}
        </if>
        <if test="axleLoad != null and axleLoad != ''">
            AND ot.axle_load = #{axleLoad}
        </if>
        <if test="wheelBase != null and wheelBase != ''">
            AND ot.wheel_base = #{wheelBase}
        </if>
        <if test="deptId != null and deptId != ''">
            AND ot.dept_id = #{deptId}
        </if>
    </sql>

    <!-- 根据传入的部门ID列表查询管理处ID -->
    <select id="getTransportationDeptByIds" resultType="java.lang.String">
        SELECT dept_id
        FROM other_transportation_dept
        WHERE dept_id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>