package com.ruoyi.supervise.service.impl;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.supervise.domain.SuperviseInspectionDetail;
import com.ruoyi.supervise.service.SuperviseInspectionDetailService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysFile;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.supervise.mapper.SuperviseInspectionRecordMapper;
import com.ruoyi.supervise.domain.SuperviseInspectionRecord;
import com.ruoyi.supervise.service.SuperviseInspectionRecordService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 督查记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
@Master
public class SuperviseInspectionRecordServiceImpl extends ServiceImpl<SuperviseInspectionRecordMapper, SuperviseInspectionRecord> implements SuperviseInspectionRecordService {

    @Autowired
    private SuperviseInspectionRecordMapper superviseInspectionRecordMapper;
    @Autowired
    private RemoteFileService remoteFileService;

    @Resource
    private SuperviseInspectionDetailService superviseInspectionDetailService;

    @Override
    public List<SuperviseInspectionRecord> findListByParam(Map<String, Object> params) {
        return superviseInspectionRecordMapper.findListByParam(params);
    }

    /**
     * 通过id获取实体（带子表）
     *
     * @param id
     * @return
     */
    @Override
    public SuperviseInspectionRecord getById(Serializable id) {

        SuperviseInspectionRecord superviseInspectionRecord = super.getById(id);
        if (superviseInspectionRecord == null)
            return null;
        List<SuperviseInspectionDetail> patrolTunnelCheckDetailList = superviseInspectionDetailService.list(new QueryWrapper<>(SuperviseInspectionDetail.class) {{
            this.orderByDesc("create_time");
            this.eq("inspection_record_id", superviseInspectionRecord.getId());
        }});

        for(SuperviseInspectionDetail detail: patrolTunnelCheckDetailList){
            R<List<SysFile>> beforeFiles = remoteFileService.findFiles(detail.getBeforeImage());
            detail.setBeforeImageList(beforeFiles.getData());
            R<List<SysFile>> afterFiles = remoteFileService.findFiles(detail.getAfterImage());
            detail.setAfterImageList(afterFiles.getData());
        }
        superviseInspectionRecord.setSuperviseInspectionDetailList(patrolTunnelCheckDetailList);
        return superviseInspectionRecord;
    }

    /**
     * 保存（带子表）
     *
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(SuperviseInspectionRecord entity) {
        if (StringUtils.isEmpty(entity.getUserId()))
            entity.setUserId(SecurityUtils.getUserId().toString());
        boolean save = super.save(entity);
        List<SuperviseInspectionDetail> detailList = entity.getSuperviseInspectionDetailList();
        if (CollectionUtil.isEmpty(detailList))
            return save;
        detailList.forEach(item -> item.setInspectionRecordId(entity.getId()));
        return superviseInspectionDetailService.saveBatch(detailList);
    }

    /**
     * 更新（带子表）
     *
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(SuperviseInspectionRecord entity) {
        boolean update = super.updateById(entity);
        List<SuperviseInspectionDetail> detailList = entity.getSuperviseInspectionDetailList();
        if (CollectionUtil.isEmpty(detailList))
            return update;
        for (SuperviseInspectionDetail detail : detailList){
            if (StringUtils.isBlank(detail.getId())){
                detail.setId(null);
                detail.setInspectionRecordId(entity.getId());
                superviseInspectionDetailService.save(detail);
            }else {
                superviseInspectionDetailService.updateById(detail);
            }

            R<List<SysFile>> beforeFiles = remoteFileService.findFiles(detail.getBeforeImage());
            detail.setBeforeImageList(beforeFiles.getData());
            R<List<SysFile>> afterFiles = remoteFileService.findFiles(detail.getAfterImage());
            detail.setAfterImageList(afterFiles.getData());
        }
//        return superviseInspectionDetailService.updateBatchById(detailList);
        return update;
    }

    /**
     * 删除（带子表）
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Serializable id) {
        boolean a = super.removeById(id);
        boolean b = superviseInspectionDetailService.remove(new QueryWrapper<>(SuperviseInspectionDetail.class) {{
            this.eq("inspection_record_id", id);
        }});
        return a;
    }
}
