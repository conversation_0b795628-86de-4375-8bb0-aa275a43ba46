package com.ruoyi.util;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Map;

/**
 * mybatis plus 分页工具类
 *
 * <AUTHOR>
 */
public final class PageUtil {
    private final static long default_page = 1L;
    private final static long default_limit = 10L;
    // 允许最大分页记录
    private final static long allow_max_limit = 1000L;

    public static <T> IPage<T> getPage() {
        return new Page<>(default_page, default_limit);
    }

    public static <T> IPage<T> getPage(Long page, Long limit) {
        if (page == null) {
            page = default_page;
        }
        if (limit == null) {
            limit = default_limit;
        } else if (limit > allow_max_limit) {
            throw new RuntimeException(String.format("limit允许最大值为%s", allow_max_limit));
        }
        return new Page<>(page, limit);
    }

    public static <T> IPage<T> getPage(Map<String, Object> params) {
        long page = default_page;
        long limit = default_limit;
        if (params != null && params.containsKey("page")) {
            page = MapUtil.getLong(params, "page");
        }
        if (params != null && params.containsKey("limit")) {
            limit = MapUtil.getLong(params, "limit");
            if (limit > allow_max_limit) {
                throw new RuntimeException(String.format("limit允许最大值为%s", allow_max_limit));
            }
        }
        return new Page<>(page, limit);
    }

    /**
     * 设置like查询参数(仅支持字符串,数字格式)
     *
     * @param qw
     * @param params
     */
    public static void setLikeQuery(QueryWrapper<?> qw, Map<String, Object> params) {
        for (String key : params.keySet()) {
            if (!key.equals("page") && !key.equals("limit") && !key.equals("order")) {
                qw.like(StringUtils.camelToUnderline(key), params.get(key));
            }
        }
    }

    /**
     * 设置排序字段 例如:order=orderNum:asc;createData:desc
     *
     * @param qw
     * @param params
     */
    public static void setOrder(QueryWrapper<?> qw, Map<String, Object> params) {
        if (params.get("order") != null && params.get("order").toString().trim().length() > 0) {
            String[] orderExps = params.get("order").toString().trim().split(";");
            for (String orders : orderExps) {
                String[] field = orders.split(":");
                if (field[1].equals("asc")) {
                    qw.orderByAsc(StringUtils.camelToUnderline(field[0]));
                }
                if (field[1].equals("desc")) {
                    qw.orderByDesc(StringUtils.camelToUnderline(field[0]));
                }
            }
        }
    }

}
