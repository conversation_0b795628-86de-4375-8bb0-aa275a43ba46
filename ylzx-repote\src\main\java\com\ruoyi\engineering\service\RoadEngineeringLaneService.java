package com.ruoyi.engineering.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.engineering.domain.RoadEngineeringLane;

import java.util.List;
import java.util.Map;

/**
 * 涉路工程Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
public interface RoadEngineeringLaneService extends IService<RoadEngineeringLane> {

    /**
     * 根据条件查询涉路工程数据列表
     * @param params
     */
    List<RoadEngineeringLane> findListByParam(Map<String, Object> params);


    /**
     * 根据条件查询涉路工程数据列表
     *
     * @param engineeringId
     */
    List<String> getByEngineeringId(String engineeringId);
}
