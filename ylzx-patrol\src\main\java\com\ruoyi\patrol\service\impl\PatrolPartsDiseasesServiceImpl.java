package com.ruoyi.patrol.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.patrol.domain.PatrolPartsDiseases;
import com.ruoyi.patrol.mapper.PatrolPartsDiseasesMapper;
import com.ruoyi.patrol.service.PatrolPartsDiseasesService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 部件-病害关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
@Master
public class PatrolPartsDiseasesServiceImpl extends ServiceImpl<PatrolPartsDiseasesMapper, PatrolPartsDiseases> implements PatrolPartsDiseasesService {

    @Resource
    private RedisService redisService;

    /**
     * 获取部件-病害的映射关系
     * <p>
     * 处理流程：
     * 1. 首先尝试从Redis缓存获取映射关系
     * 2. 如果缓存中不存在，则从数据库重新查询并生成映射
     * 3. 将新生成的映射保存到Redis中，设置24小时过期时间
     * <p>
     * 映射策略：
     * - Key: 病害ID (diseases_id)
     * - Value: 部件ID (parts_id)
     * - 当存在重复的病害ID时，保留第一个对应的部件ID
     * <p>
     * 示例：
     * {
     * "D001": "P001",  // 病害D001对应部件P001
     * "D002": "P002",  // 病害D002对应部件P002
     * }
     *
     * @return 病害ID到部件ID的映射关系
     */
    @Override
    public Map<String, String> getPartsDiseasesMap() {
        String redisKey = "patrol:parts:diseases_map";

        // 尝试从Redis获取
        Map<String, String> partsDiseasesMap = redisService.getCacheMap(redisKey);

        if (partsDiseasesMap != null && !partsDiseasesMap.isEmpty()) {
            return partsDiseasesMap;
        }

        // Redis中不存在，重新生成映射
        partsDiseasesMap = this.list()
                .stream()
                .filter(relation -> StringUtils.isNotBlank(relation.getDiseasesId())
                        && StringUtils.isNotBlank(relation.getPartsId()))
                .collect(Collectors.toMap(
                        PatrolPartsDiseases::getDiseasesId,
                        PatrolPartsDiseases::getPartsId,
                        (v1, v2) -> v1 // 如果有重复key，保留第一个值
                ));

        redisService.setCacheMap(redisKey, partsDiseasesMap);
        redisService.expire(redisKey, 24, TimeUnit.HOURS);

        return partsDiseasesMap;
    }

}
