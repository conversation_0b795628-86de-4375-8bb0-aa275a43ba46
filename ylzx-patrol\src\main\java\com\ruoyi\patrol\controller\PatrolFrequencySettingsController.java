package com.ruoyi.patrol.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.map.Paramap;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.manage.api.service.BaseBridgeStaticService;
import com.ruoyi.patrol.domain.PatrolFrequencySettings;
import com.ruoyi.patrol.service.PatrolFrequencySettingsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 巡查频率配置Controller
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
@Api(tags = "巡查频率配置" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/frequencySettings")
public class PatrolFrequencySettingsController extends BaseController {
    final private PatrolFrequencySettingsService patrolFrequencySettingsService;
    @Resource
    private BaseBridgeStaticService bridgeStaticService;

    /**
     * 查询巡查频率配置列表(分页)
     */
    @ApiOperation("查询巡查频率配置列表")
    //@RequiresPermissions("patrol:frequencySettings:list")
    @GetMapping("/list")
    public MTableDataInfo list(@RequestParam Map<String, Object> params) {
        return patrolFrequencySettingsService.getListMTableDataInfo(params);
    }


    /**
     * 查询巡查频率配置列表(不分页)
     */
    @ApiOperation("查询巡查频率配置列表(不分页)")
    //@RequiresPermissions("patrol:frequencySettings:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolFrequencySettings> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolFrequencySettings> list = patrolFrequencySettingsService.list(qw);
        return success(list);
    }

    /**
     * 修改巡查频率配置
     */
    @ApiOperation("修改巡查频率配置")
    //@RequiresPermissions("patrol:frequencySettings:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolFrequencySettings patrolFrequencySettings) {
        try {
            final List<String> assetIdList = patrolFrequencySettings.getIds();//资产ids
            //批量修改
            if (null != assetIdList && assetIdList.size() > 0) {
                List<PatrolFrequencySettings> list = patrolFrequencySettingsService.findListByParam(Paramap.create()
                        .put("assetIdList", assetIdList).put("type", patrolFrequencySettings.getType()));
                List<String> uId = new ArrayList<>();
                //修改
                for (PatrolFrequencySettings settings : list) {
                    settings.setDayFrequency(patrolFrequencySettings.getDayFrequency());
                    settings.setMonthFrequency(patrolFrequencySettings.getMonthFrequency());
                    patrolFrequencySettingsService.updateById(settings);
                    uId.add(settings.getAssetId());
                }
                Set<String> newId = assetIdList.stream()
                        .filter(item -> !uId.contains(item)).collect(Collectors.toSet());
                List<PatrolFrequencySettings> newFrequencyList = new ArrayList<>();
                //新增
                PatrolFrequencySettings newFrequency;
                for (String assetId : newId) {
                    newFrequency = new PatrolFrequencySettings();
                    newFrequency.setAssetId(assetId);
                    newFrequency.setType(patrolFrequencySettings.getType());
                    newFrequency.setDayFrequency(patrolFrequencySettings.getDayFrequency());
                    newFrequency.setMonthFrequency(patrolFrequencySettings.getMonthFrequency());
                    newFrequencyList.add(newFrequency);
                }
                if (newFrequencyList.size() > 0) {
                    patrolFrequencySettingsService.saveBatch(newFrequencyList);
                }
            } else {
                //单个修改
                patrolFrequencySettingsService.saveOrUpdatePlus(patrolFrequencySettings);
            }

        } catch (DataIntegrityViolationException e) {
            // 处理唯一约束违规的异常
            System.err.println("违反了唯一约束：" + e.getMessage());
        }
        return toAjax(true);
    }

    /**
     * 删除巡查频率配置
     */
    @ApiOperation("删除巡查频率配置")
    //@RequiresPermissions("patrol:frequencySettings:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolFrequencySettingsService.removeById(id));
    }

    /**
     * 导出巡查频率配置列表
     */
    @ApiOperation("导出巡查频率配置列表")
    //@RequiresPermissions("patrol:frequencySettings:export")
    @Log(title = "巡查频率配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolFrequencySettings> list = patrolFrequencySettingsService.list();
        ExcelUtil<PatrolFrequencySettings> util = new ExcelUtil<PatrolFrequencySettings>(PatrolFrequencySettings.class);
        util.exportExcel(response, list, "巡查频率配置数据");
    }


}
