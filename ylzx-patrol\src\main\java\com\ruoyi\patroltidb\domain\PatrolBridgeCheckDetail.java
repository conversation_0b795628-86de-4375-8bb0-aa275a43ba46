package com.ruoyi.patroltidb.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;

/**
 * (PatrolBridgeCheckDetail)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-25 09:46:30
 */

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@ApiModel(value = "桥梁巡检查详情表", description = "桥梁巡检查详情表")
@TableName("patrol_bridge_check_detail")
public class PatrolBridgeCheckDetail extends PatrolAssetCheckDetail {

    @Serial
    private static final long serialVersionUID = 1L;


}

