package com.ruoyi.patrol.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 资产病害信息对象 base_asset_diseases
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@ApiModel(value="资产病害信息")
@TableName("patrol_asset_diseases")
@Getter
@Setter
public class PatrolAssetDiseases extends BaseTableEntity {
    private static final long serialVersionUID = 1L;

    /** 资产类型 */
    @Excel(name = "资产类型")
    @ApiModelProperty(value = "资产类型")
    private String assetType;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /** 病害编码 */
    @Excel(name = "病害编码")
    @ApiModelProperty(value = "病害编码")
    private String diseaseCode;

    /** 病害名称 */
    @Excel(name = "病害名称")
    @ApiModelProperty(value = "病害名称")
    private String diseaseName;

    /** 示例图像 */
    @Excel(name = "示例图像")
    @ApiModelProperty(value = "示例图像")
    private String exampleImage;

    /** 桩号计算方式 */
    @Excel(name = "桩号计算方式")
    @ApiModelProperty(value = "桩号计算方式")
    private String pileMode;

    /** 排序 */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /** 标记颜色 */
    @Excel(name = "标记颜色")
    @ApiModelProperty(value = "标记颜色")
    private String color;

}
