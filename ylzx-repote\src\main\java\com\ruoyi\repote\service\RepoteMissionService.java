package com.ruoyi.repote.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.repote.domain.RepoteMission;

/**
 * 填报任务Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
public interface RepoteMissionService extends IService<RepoteMission> {

    /**
     * 根据条件查询填报任务数据列表
     * @param params
     */
    List<RepoteMission> findListByParam(Map<String, Object> params);


    /**
     * 根据条件查询填报任务数据列表
     * @param reportMission 填报任务
     *
     */
    Long getCountByReportMission(String reportMission);

}
