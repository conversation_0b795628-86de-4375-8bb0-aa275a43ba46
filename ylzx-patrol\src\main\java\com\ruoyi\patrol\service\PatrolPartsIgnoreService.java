package com.ruoyi.patrol.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patrol.domain.PatrolPartsIgnore;

import java.util.List;

/**
 * 检查项排除Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface PatrolPartsIgnoreService extends IService<PatrolPartsIgnore> {

    /**
     * 查询列表
     * @param patrolPartsIgnore
     * @return
     */
    List<PatrolPartsIgnore> list(PatrolPartsIgnore patrolPartsIgnore);

    /**
     * 根据资源ID列表查询检查项排除记录
     *
     * @param assetIds 资源ID列表
     * @return 检查项排除记录列表
     */
    List<PatrolPartsIgnore> selectByAssetIds(List<String> assetIds);
}
