<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.engineering.mapper.RoadEngineeringLaneMapper">

    <resultMap type="com.ruoyi.engineering.domain.RoadEngineeringLane" id="RoadEngineeringLaneResult">

            <result property="id" column="id"/>
            <result property="roadEngineeringId" column="road_engineering_id"/>
            <result property="lane" column="lane"/>

    </resultMap>

    <sql id="base_column">
        road_engineering_id,lane </sql>

    <sql id="where_column">
        <if test="roadEngineeringId != null and roadEngineeringId != ''">
            AND road_engineering_id = #{roadEngineeringId}
        </if>
        <if test="lane != null and lane != ''">
            AND lane = #{lane}
        </if>
    </sql>

    <sql id="set_column">
            <if test="roadEngineeringId != null">
                road_engineering_id = #{roadEngineeringId},
            </if>
            <if test="lane != null">
                lane = #{lane},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="RoadEngineeringLaneResult">
        SELECT
        <include refid="base_column"/>
        FROM road_engineering_lane
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="RoadEngineeringLaneResult">
        SELECT
        <include refid="base_column"/>
        FROM road_engineering_lane
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>


    <select id="getByEngineeringId" resultType="String">
        SELECT
        lane
        FROM road_engineering_lane
        WHERE road_engineering_id = #{engineeringId}
    </select>



    <delete id="deleteById">
        DELETE FROM road_engineering_lane
        WHERE road_engineering_id = #{roadEngineeringId}
    </delete>

</mapper>