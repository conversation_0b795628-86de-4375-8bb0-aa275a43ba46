package com.ruoyi.patroltidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheckDetail;
import com.ruoyi.patroltidb.domain.TestFileImg;

import java.util.List;
import java.util.Map;

/**
 * 桥梁巡检查子Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface PatrolBridgeCheckDetailService extends IService<PatrolBridgeCheckDetail> {

    /**
     * 根据条件查询桥梁巡检查子数据列表
     * @param params
     */
    List<PatrolBridgeCheckDetail> findListByParam(Map<String, Object> params);

    List<TestFileImg> findFileImgList(Map<String, Object> params);

    Long batchUpdate(Map<String, Object> params);



}
