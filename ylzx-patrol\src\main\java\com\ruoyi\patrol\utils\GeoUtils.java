package com.ruoyi.patrol.utils;

import lombok.extern.slf4j.Slf4j;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月15日 17:25
 */
@Slf4j
public class GeoUtils {
    private static final  GeometryFactory geometryFactory = JTSFactoryFinder.getGeometryFactory();

    public static double arcDistance(double lat1, double lon1, double lat2, double lon2) {
        return SloppyMath.haversinMeters(lat1, lon1, lat2, lon2);
    }


    /**
     * 传入两条WKT字符串的lineString，返回合并后的LineString
     * @param lineStringA 线段A的WKT字符串
     * @param lineStringB 线段B的WKT字符串
     * @return 合并后的LineString的WKT字符串
     */
    public static String mergeLineStrings(String lineStringA, String lineStringB) {
        LineString lineA = wktToLineString(lineStringA);
        LineString lineB = wktToLineString(lineStringB);
        LineString mergedLine = mergeLines(lineA, lineB);
        return lineStringToWkt(mergedLine);
    }

    /**
     * 检查LineString WKT字符串是否合规
     * @param wkt 待检查的WKT字符串
     * @return 是否合规
     */
    public static boolean isValidLineStringWkt(String wkt) {
        if (wkt == null || wkt.trim().isEmpty()) {
            return false;
        }

        // 检查是否以LINESTRING开头(不区分大小写)
        if (!wkt.trim().toUpperCase().startsWith("LINESTRING")) {
            return false;
        }

        try {
            // 尝试解析WKT字符串
            WKTReader reader = new WKTReader(geometryFactory);
            LineString lineString = (LineString) reader.read(wkt);

            // 检查是否为空或坐标点数量是否小于2
            if (lineString == null || lineString.getNumPoints() < 2) {
                return false;
            }

            // 检查坐标值是否合法
            Coordinate[] coordinates = lineString.getCoordinates();
            for (Coordinate coordinate : coordinates) {
                if (Double.isNaN(coordinate.x) || Double.isNaN(coordinate.y) ||
                    Double.isInfinite(coordinate.x) || Double.isInfinite(coordinate.y)) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.debug("WKT字符串解析失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从LineString WKT字符串中获取最后一个点，并返回该点的WKT表示
     * @param lineStringWkt LineString的WKT字符串
     * @return 最后一个点的WKT字符串，格式为"POINT(x y)"，如果输入无效则返回null
     */
    public static String getLastPointFromLineString(String lineStringWkt) {
        if (!isValidLineStringWkt(lineStringWkt)) {
            return null;
        }

        try {
            // 解析LineString
            LineString lineString = wktToLineString(lineStringWkt);
            if (lineString == null || lineString.getNumPoints() == 0) {
                return null;
            }

            // 获取最后一个坐标点
            Coordinate lastCoordinate = lineString.getCoordinateN(lineString.getNumPoints() - 1);
            
            // 创建点对象
            org.locationtech.jts.geom.Point point = geometryFactory.createPoint(lastCoordinate);
            
            // 将点转换为WKT字符串
            WKTWriter writer = new WKTWriter();
            return writer.write(point);
        } catch (Exception e) {
            log.error("获取LineString最后一个点时出错", e);
            return null;
        }
    }

    /**
     * 将LineString对象转换为WKT字符串
     * @param lineString LineString对象
     * @return WKT字符串
     */
    public static String lineStringToWkt(LineString lineString) {
        if (lineString == null) {
            return null;
        }
        WKTWriter writer = new WKTWriter();
        return writer.write(lineString);
    }

    /**
     * 将WKT字符串解析为LineString对象
     * @param wkt WKT字符串
     * @return LineString对象
     */
    public static LineString wktToLineString(String wkt){
        if(wkt == null || wkt.trim().isEmpty()){
            return null;
        }
        WKTReader reader = new WKTReader(geometryFactory);
        try {
            return (LineString) reader.read(wkt);
        } catch (ParseException e) {
            log.error("解析WKT字符串时出错", e);
            return null;
        }
    }

    /**
     * 合并两条线段
     * @param lineA 线段A
     * @param lineB 线段B
     * @return 合并后的线段
     */
    public static LineString mergeLines(LineString lineA, LineString lineB) {
        if (lineA == null && lineB == null) {
            return null;
        } else if (lineA == null) {
            return lineB;
        } else if (lineB == null) {
            return lineA;
        }

        GeometryFactory geometryFactory = lineA.getFactory();

        // 获取线段A的坐标数组
        Coordinate[] coordinatesA = lineA.getCoordinates();

        // 获取线段B的坐标数组
        Coordinate[] coordinatesB = lineB.getCoordinates();

        // 创建新的坐标数组，长度为两条线段坐标数组之和
        Coordinate[] mergedCoordinates = new Coordinate[coordinatesA.length + coordinatesB.length];

        // 复制线段A的坐标到新数组
        System.arraycopy(coordinatesA, 0, mergedCoordinates, 0, coordinatesA.length);

        // 复制线段B的坐标到新数组
        System.arraycopy(coordinatesB, 0, mergedCoordinates, coordinatesA.length, coordinatesB.length);

        // 创建并返回新的合并后的线段
        return geometryFactory.createLineString(mergedCoordinates);
    }

    /**
     * 计算 LineString WKT 字符串表示的路径总长度（米）
     * @param wkt LineString 的 WKT 字符串
     * @return 路径总长度（米），如果输入无效则返回 0
     */
    public static double calculateLineStringLength(String wkt) {
        if (!isValidLineStringWkt(wkt)) {
            return 0.0;
        }

        try {
            LineString lineString = wktToLineString(wkt);
            if (lineString == null) {
                return 0.0;
            }

            Coordinate[] coordinates = lineString.getCoordinates();
            double totalDistance = 0.0;

            // 遍历所有相邻的坐标点对，计算它们之间的距离并累加
            for (int i = 0; i < coordinates.length - 1; i++) {
                Coordinate coord1 = coordinates[i];
                Coordinate coord2 = coordinates[i + 1];
                
                double distance = SloppyMath.haversinMeters(
                    coord1.y,
                    coord1.x,
                    coord2.y,
                    coord2.x
                );
                
                totalDistance += distance;
            }

            // 四舍五入保留两位小数
            return Math.round(totalDistance * 100.0) / 100.0;
        } catch (Exception e) {
            log.error("计算 LineString 长度时发生错误", e);
            return 0.0;
        }
    }

    /**
     * 计算两个 LineString WKT 字符串合并后的总长度（米）
     * @param wkt1 第一个 LineString 的 WKT 字符串
     * @param wkt2 第二个 LineString 的 WKT 字符串
     * @return 合并后的总长度（米）
     */
    public static double calculateMergedLineStringsLength(String wkt1, String wkt2) {
        // 合并两个 LineString
        String mergedWkt = mergeLineStrings(wkt1, wkt2);
        // 计算合并后的总长度
        return calculateLineStringLength(mergedWkt);
    }

}
