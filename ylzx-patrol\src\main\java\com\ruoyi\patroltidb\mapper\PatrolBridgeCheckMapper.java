package com.ruoyi.patroltidb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheck;

import java.util.List;
import java.util.Map;

/**
 * 桥梁巡检查记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface PatrolBridgeCheckMapper extends BaseMapper<PatrolBridgeCheck> {

    List<PatrolBridgeCheck> findListByParam(Map<String, Object> params);


}
