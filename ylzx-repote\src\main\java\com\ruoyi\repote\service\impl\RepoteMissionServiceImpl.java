package com.ruoyi.repote.service.impl;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repote.mapper.RepoteMissionMapper;
import com.ruoyi.repote.domain.RepoteMission;
import com.ruoyi.repote.service.RepoteMissionService;

import javax.annotation.Resource;

/**
 * 填报任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Service
public class RepoteMissionServiceImpl extends ServiceImpl<RepoteMissionMapper, RepoteMission> implements RepoteMissionService {

    @Resource
    private RepoteMissionMapper repoteMissionMapper;

    @Override
    public List<RepoteMission> findListByParam(Map<String, Object> params) {
        return repoteMissionMapper.findListByParam(params);
    }

    @Override
    public Long getCountByReportMission(String reportMission) {
        return repoteMissionMapper.getCountByReportMission(reportMission);
    }


}
