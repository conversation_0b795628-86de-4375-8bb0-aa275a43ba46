package com.ruoyi.patrol.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 日常巡查统计
 * @author: sfc
 * @date: 2025年03月18日 9:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DailyPatrolVO {

    @ApiModelProperty(value = "巡查病害上报个数")
    private Integer diseaseNum;

    @ApiModelProperty(value = "巡查里程")
    private BigDecimal patrolMileage;

    @ApiModelProperty(value = "巡查次数")
    private Integer patrolNum;

//    @ApiModelProperty(value = "巡查异常情况")
//    private Integer patrolAbnormalNum;

    @ApiModelProperty(value = "有效巡查率（巡查病害上报个数/巡查次数）")
    private BigDecimal effPatrolRate;


}
