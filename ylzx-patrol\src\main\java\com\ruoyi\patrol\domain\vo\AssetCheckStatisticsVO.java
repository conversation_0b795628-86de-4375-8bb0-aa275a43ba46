package com.ruoyi.patrol.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssetCheckStatisticsVO {
    @ApiModelProperty(value = "管养单位")
    private String managementOffice;
    @ApiModelProperty(value = "管养单位id")
    private Long managementOfficeId;
    @ApiModelProperty(value = "养护段")
    private String sectionName;
    @ApiModelProperty(value = "养护段id")
    private String sectionId;
    @ApiModelProperty(value = "月份")
    private Integer month;
    @ApiModelProperty(value = "格式化时间年-月")
    private String formatTime;
    @ApiModelProperty(value = "应检数量")
    private Integer totalCount;
    @ApiModelProperty(value = "未检数量")
    private Integer inPeriodCount;
    @ApiModelProperty(value = "已检数量")
    private Integer completedCount;
}
