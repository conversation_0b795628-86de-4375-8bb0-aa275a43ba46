package com.ruoyi.patrol.service.handler.impl;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.manage.api.domain.culvert.BaseCulvertRequest;
import com.ruoyi.manage.api.domain.culvert.BaseCulvertResponse;
import com.ruoyi.manage.api.service.BaseCulvertService;
import com.ruoyi.patrol.domain.BaseCulvertResponseCache;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.service.BaseCulvertResponseCacheService;
import com.ruoyi.patrol.service.enrichment.DataEnrichmentService;
import com.ruoyi.patrol.service.handler.AssetDataHandler;
import com.ruoyi.patrol.utils.DomainToCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 涵洞数据处理器
 */
@Slf4j
@Component
public class CulvertDataHandler implements AssetDataHandler<BaseCulvertRequest, BaseCulvertResponse, BaseCulvertResponseCache> {

    @Resource
    private BaseCulvertService baseCulvertService;
    
    @Resource
    private BaseCulvertResponseCacheService cacheService;
    
    @Resource
    private DataEnrichmentService dataEnrichmentService;

    @Override
    public AssetType getAssetType() {
        return AssetType.CULVERT;
    }

    @Override
    public Class<BaseCulvertRequest> getRequestClass() {
        return BaseCulvertRequest.class;
    }

    @Override
    public Class<BaseCulvertResponseCache> getCacheClass() {
        return BaseCulvertResponseCache.class;
    }

    @Override
    public BaseCulvertRequest createRequestInstance() throws Exception {
        return BaseCulvertRequest.class.getDeclaredConstructor().newInstance();
    }

    @Override
    public MTableDataInfo<List<BaseCulvertResponse>> fetchData(BaseCulvertRequest request) {
        return baseCulvertService.getInnerListPage(request, SecurityConstants.INNER);
    }

    /**
     * 将涵洞响应对象列表转换为缓存对象列表
     * 直接使用原始类型转换，避免通过BaseDataDomain转换造成的字段丢失
     * 
     * @param responses 涵洞响应对象列表
     * @return 涵洞缓存对象列表
     */
    @Override
    public List<BaseCulvertResponseCache> convertToCacheList(List<BaseCulvertResponse> responses) {
        log.info("开始转换涵洞数据，数量：{}", responses != null ? responses.size() : 0);
        // 使用直接转换方法，保留特定字段信息
        List<BaseCulvertResponseCache> cacheList = DomainToCacheUtils.convertCulvertList(responses);
        log.info("涵洞数据转换完成，结果数量：{}", cacheList.size());
        
        if (log.isDebugEnabled() && !cacheList.isEmpty()) {
            BaseCulvertResponseCache sample = cacheList.get(0);
            log.debug("涵洞数据转换示例: ID={}, culvertCode={}, culvertSpan={}, culvertLength={}, culvertHeight={}, culvertType={}",
                    sample.getId(), sample.getCulvertCode(), sample.getCulvertSpan(), 
                    sample.getCulvertLength(), sample.getCulvertHeight(), sample.getCulvertType());
        }
        
        return cacheList;
    }

    @Override
    public void prepareRequest(AssetBaseDataRequest assetRequest, BaseCulvertRequest request, boolean dataRule) {
        BeanUtils.copyProperties(assetRequest, request);
        request.setIfDataRule(dataRule);
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T extends BaseDataCache> void saveCache(List<T> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        try {
            List<BaseCulvertResponseCache> typedList = (List<BaseCulvertResponseCache>) dataList;
            cacheService.clearTable();

            // 分批保存，每批1000条记录
            int batchSize = 1000;
            int totalSize = typedList.size();
            log.info("开始分批保存涵洞数据，总记录数: {}，批次大小: {}", totalSize, batchSize);

            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<BaseCulvertResponseCache> batch = typedList.subList(i, endIndex);

                try {
                    cacheService.insertBatch(batch);
                    log.info("涵洞数据批次保存成功，批次: {}-{}/{}", i + 1, endIndex, totalSize);
                } catch (Exception e) {
                    log.error("涵洞数据批次保存失败，批次: {}-{}", i + 1, endIndex, e);
                    throw e;
                }
            }

            log.info("涵洞数据缓存成功，共{}条记录", totalSize);
        } catch (ClassCastException e) {
            log.error("涵洞数据类型转换失败", e);
            throw new RuntimeException("涵洞数据类型转换失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("涵洞数据缓存失败", e);
            throw new RuntimeException("涵洞数据缓存失败: " + e.getMessage());
        }
    }
    
    @Override
    public <T extends BaseDataCache> void enrichData(List<T> dataList) {
        dataEnrichmentService.enrichData(dataList);
    }
} 