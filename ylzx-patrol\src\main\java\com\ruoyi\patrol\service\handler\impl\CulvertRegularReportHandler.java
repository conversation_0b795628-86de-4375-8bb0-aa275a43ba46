package com.ruoyi.patrol.service.handler.impl;

import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import lombok.extern.slf4j.Slf4j;

import java.awt.Color; // 引入 AWT Color 用于背景色
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * "涵洞经常检查记录表"生成处理器。
 * 继承自抽象处理器以重用通用的Excel生成逻辑。
 */
@Slf4j
public class CulvertRegularReportHandler extends AbstractExcelReportHandler<PatrolAssetCheck> {

    private final List<PatrolAssetCheck> reportDataList;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");

    // --- 此报表的特定样式定义 ---
    // 继承的样式：titleStyle, headerLabelStyle, headerValueStyle, itemHeaderStyle, itemCellStyle, itemFirstColStyle
    // 我们将克隆并应用边框到继承的样式，并添加页脚样式和灰色背景样式

    private CellStyle headerLabelStyleWithBorder; // 带边框的头标签样式
    private CellStyle headerValueStyleWithBorder; // 带边框的头值样式
    private CellStyle itemHeaderStyleWithBorder;  // 带边框和背景的项目头样式
    private CellStyle itemCellStyleWithBorder;    // 带边框的项目单元格样式 (居中)
    private CellStyle itemFirstColStyleWithBorder;// 带边框的项目第一列样式 (左对齐)
    private CellStyle itemDescStyleWithBorder;    // 带边框的项目描述样式 (居中，用于合并列)
    private CellStyle itemAdviceStyleWithBorder;  // 带边框的项目建议样式 (居中，用于合并列)

    private CellStyle footerLabelStyle;          // 特定页脚标签样式
    private CellStyle footerValueStyle;          // 特定页脚值样式
    private CellStyle normalBorderStyle;         // 仅用于细边框的特定样式
    private CellStyle shadedHeaderStyle;         // 用于标题行的灰色背景样式 (可选，基于图片)

    private CellStyle noBorderStyle;      // 无边框左对齐自动换行样式

    // 继承的：signUrlMap, signImageMap, failedSignIds, log, SIGNATURE_HEIGHT_POINTS, httpClient

    public CulvertRegularReportHandler(List<PatrolAssetCheck> reportDataList, Map<String, String> signUrlMap) {
        // super(); // 隐式调用超类构造函数
        this.reportDataList = reportDataList != null ? reportDataList : new ArrayList<>();
        if (signUrlMap != null) {
            this.signUrlMap.putAll(signUrlMap); // 填充继承的映射
        }
        // 预加载现在在beforeSheetCreate中调用
    }

    /**
     * 重写createStyles方法，为此报表类型添加特定样式
     * 并初始化基类中的通用样式，确保应用边框。
     */
    @Override
    protected void createStyles(Workbook workbook) {
        super.createStyles(workbook); // 首先初始化通用样式

        // --- 通用边框样式 ---
        normalBorderStyle = workbook.createCellStyle();
        setThinBorders(normalBorderStyle); // 使用继承的辅助方法

        // --- 克隆基本样式并确保它们有边框 ---

        // 无边框左对齐自动换行样式
        noBorderStyle = workbook.createCellStyle();
        noBorderStyle.setBorderTop(BorderStyle.NONE);
        noBorderStyle.setBorderBottom(BorderStyle.NONE);
        noBorderStyle.setBorderLeft(BorderStyle.NONE);
        noBorderStyle.setBorderRight(BorderStyle.NONE);
        noBorderStyle.setAlignment(HorizontalAlignment.LEFT);
        noBorderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        noBorderStyle.setWrapText(true);
        // (如果基础样式没有边框，这是必需的)

        // 标题标签样式 (确保它有来自normalBorderStyle的边框)
        headerLabelStyleWithBorder = workbook.createCellStyle();
        headerLabelStyleWithBorder.cloneStyleFrom(super.headerLabelStyle); // 从基本样式开始
        copyBorders(normalBorderStyle, headerLabelStyleWithBorder); // 确保边框

        // 标题值样式
        headerValueStyleWithBorder = workbook.createCellStyle();
        headerValueStyleWithBorder.cloneStyleFrom(super.headerValueStyle);
        copyBorders(normalBorderStyle, headerValueStyleWithBorder);

        // 项目标题样式 (带边框和灰色背景)
        itemHeaderStyleWithBorder = workbook.createCellStyle();
        itemHeaderStyleWithBorder.cloneStyleFrom(super.itemHeaderStyle); // 继承字体、对齐等
        copyBorders(normalBorderStyle, itemHeaderStyleWithBorder);       // 应用边框
        setSolidBackground(itemHeaderStyleWithBorder, new Color(217, 217, 217)); // 设置灰色背景
        itemHeaderStyleWithBorder.setWrapText(true);

        // 项目单元格样式 (居中，带边框)
        itemCellStyleWithBorder = workbook.createCellStyle();
        itemCellStyleWithBorder.cloneStyleFrom(super.itemCellStyle);
        copyBorders(normalBorderStyle, itemCellStyleWithBorder);
        itemCellStyleWithBorder.setWrapText(true);

        // 项目第一列样式 (左对齐，带边框)
        itemFirstColStyleWithBorder = workbook.createCellStyle();
        itemFirstColStyleWithBorder.cloneStyleFrom(super.itemFirstColStyle);
        copyBorders(normalBorderStyle, itemFirstColStyleWithBorder);
        itemFirstColStyleWithBorder.setWrapText(true);

        // 项目描述样式 (居中，用于合并列 BCD) - 继承居中和边框
        itemDescStyleWithBorder = workbook.createCellStyle();
        itemDescStyleWithBorder.cloneStyleFrom(itemCellStyleWithBorder);
        itemDescStyleWithBorder.setWrapText(true);

        // 项目建议样式 (居中，用于合并列 EF) - 继承居中和边框
        itemAdviceStyleWithBorder = workbook.createCellStyle();
        itemAdviceStyleWithBorder.cloneStyleFrom(itemCellStyleWithBorder);
        itemAdviceStyleWithBorder.setWrapText(true);

        // --- 新的/特定样式 ---
        // 页脚标签样式 (右对齐，带边框) - 基于headerLabelStyleWithBorder
        footerLabelStyle = workbook.createCellStyle();
        footerLabelStyle.cloneStyleFrom(headerLabelStyleWithBorder); // 继承字体、对齐方式、边框
        footerLabelStyle.setAlignment(HorizontalAlignment.CENTER); // 图片中标签居中
        setSolidBackground(footerLabelStyle, new Color(217, 217, 217));
        footerLabelStyle.setWrapText(true);

        // 页脚值样式 (左对齐，带边框) - 基于headerValueStyleWithBorder
        footerValueStyle = workbook.createCellStyle();
        footerValueStyle.cloneStyleFrom(headerValueStyleWithBorder); // 继承字体、对齐方式、边框
        footerValueStyle.setAlignment(HorizontalAlignment.LEFT);   // 签名/姓名左对齐，日期值左对齐
        footerValueStyle.setWrapText(true);

        // 可选：为图片中的灰色标题行创建样式 (如果需要)
        shadedHeaderStyle = workbook.createCellStyle();
        shadedHeaderStyle.cloneStyleFrom(headerValueStyleWithBorder); // 继承值样式
        setSolidBackground(shadedHeaderStyle, new Color(217, 217, 217)); // 设置灰色背景
        shadedHeaderStyle.setWrapText(true);
        
        // 创建一个带背景的标签样式版本
        CellStyle shadedHeaderLabelStyle = workbook.createCellStyle();
        shadedHeaderLabelStyle.cloneStyleFrom(headerLabelStyleWithBorder);
        setSolidBackground(shadedHeaderLabelStyle, new Color(217, 217, 217));
        shadedHeaderLabelStyle.setWrapText(true);

        CellStyle shadedHeaderValueStyle = workbook.createCellStyle(); // 带背景的值
        shadedHeaderValueStyle.cloneStyleFrom(headerValueStyleWithBorder);
        shadedHeaderValueStyle.setWrapText(true);
        // 在afterSheetCreate中根据需要使用 shadedHeaderStyle 和 shadedHeaderLabelStyle
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        super.beforeSheetCreate(writeWorkbookHolder, writeSheetHolder); // 通过createStyles重写创建样式
        // 在生成表格内容前预加载签名
        if (!signUrlMap.isEmpty()) {
            preloadSignImages(); // 调用继承的预加载器
        }
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        Workbook workbook = sheet.getWorkbook(); // 从sheet获取workbook实例

        // 诊断日志记录
        log.info("开始生成涵洞经常检查记录表。签名URL数量: {}, 预加载图像数量: {}",
                signUrlMap != null ? signUrlMap.size() : 0, signImageMap.size());
        if (failedSignIds != null && !failedSignIds.isEmpty()) {
            log.warn("签名下载失败数量: {}. IDs: {}", failedSignIds.size(), failedSignIds);
        }

        int currentRowIndex = 0; // 当前行索引

        // --- 设置列宽 (基于6列布局 A-F) ---
        sheet.setColumnWidth(0, 10 * 256);  // A列 (检查内容/标签)
        sheet.setColumnWidth(1, 13 * 256);  // B列 (情况描述部分1 / 签名1 / 值)
        sheet.setColumnWidth(2, 14 * 256);  // C列 (情况描述部分2 / 标签)
        sheet.setColumnWidth(3, 16 * 256);  // D列 (情况描述部分3 / 签名2 / 值)
        sheet.setColumnWidth(4, 11 * 256);  // E列 (养护建议部分1 / 标签)
        sheet.setColumnWidth(5, 20 * 256);  // F列 (养护建议部分2 / 值/日期)

        // 存储列宽信息用于行高调整
        int[] columnWidths = new int[] {10, 13, 14, 16, 11, 20};

        if (reportDataList == null || reportDataList.isEmpty()) {
            log.warn("报告数据列表为空。生成空表格。");
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "涵洞经常检查记录表 (无数据)", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并A-F
            // 确保合并区域有边框（如果标题需要）
            // applyRowBorder(titleRow, 0, 5, normalBorderStyle); // 如果标题需要边框
            return;
        }

        CellStyle shadedHeaderLabelStyle = workbook.createCellStyle(); // 带背景的标签
        shadedHeaderLabelStyle.cloneStyleFrom(headerLabelStyleWithBorder);
        setSolidBackground(shadedHeaderLabelStyle, new Color(217, 217, 217));
        shadedHeaderLabelStyle.setWrapText(true);

        CellStyle shadedHeaderValueStyle = workbook.createCellStyle(); // 带背景的值
        shadedHeaderValueStyle.cloneStyleFrom(headerValueStyleWithBorder);
        shadedHeaderValueStyle.setWrapText(true);

        for (int i = 0; i < reportDataList.size(); i++) {
            PatrolAssetCheck reportData = reportDataList.get(i);
            if (reportData == null) {
                log.warn("跳过索引 {} 处的null报告数据对象", i);
                continue;
            }

            log.debug("正在为资产编码 {} 生成涵洞报告部分", reportData.getAssetCode());

            // 1. 标题行
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "涵洞经常检查记录表", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并A-F
            currentRowIndex++;

            // 2. 管理单位行
            Row agencyRow = sheet.createRow(currentRowIndex);
            // 获取管理单位名称，优先使用propertyUnitName，否则使用maintainUnitName
            String agencyName = reportData.getPropertyUnitName() != null ? reportData.getPropertyUnitName() : reportData.getMaintainUnitName();
            createCell(agencyRow, 0, "管理单位：" + Objects.toString(agencyName, ""), headerValueStyleWithBorder); // 使用带边框的值样式
            // 为合并和边框创建空单元格
            for (int col = 1; col <= 5; col++) {
                createCell(agencyRow, col, "", headerValueStyleWithBorder);
            }
            applyRowBorder(agencyRow, 0, 5, normalBorderStyle); // 确保整行边框一致
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并A-F
            setConditionalRowHeight(agencyRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 3. 路线信息行 (带灰色背景)
            Row routeRow = sheet.createRow(currentRowIndex);
            createCell(routeRow, 0, "路线编码", shadedHeaderLabelStyle); // 使用带背景的标签样式
            createCell(routeRow, 1, Objects.toString(reportData.getRouteCode(), ""), shadedHeaderValueStyle); // 使用带背景的值样式
            createCell(routeRow, 2, "路线名称", shadedHeaderLabelStyle);
            createCell(routeRow, 3, Objects.toString(reportData.getMaintenanceSectionName(), ""), shadedHeaderValueStyle);
            createCell(routeRow, 4, "涵洞桩号", shadedHeaderLabelStyle);
            // 假设 getStakeFormat() 或 getCenterStake() 提供了桩号信息
            String stake = reportData.getStakeFormat() != null ? reportData.getStakeFormat() : formatStake(reportData.getCenterStake(), null);
            createCell(routeRow, 5, stake, shadedHeaderValueStyle);
            // 边框已通过样式应用，但可以调用applyRowBorder确保一致性
            applyRowBorder(routeRow, 0, 5, normalBorderStyle);
            setConditionalRowHeight(routeRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 4. 涵洞类型/养护单位行 (带灰色背景)
            Row typeRow = sheet.createRow(currentRowIndex);
            createCell(typeRow, 0, "涵洞类型", shadedHeaderLabelStyle);
            // 假设 getAssetTypeName() 提供涵洞类型
            createCell(typeRow, 1, Objects.toString(reportData.getAssetName(), ""), shadedHeaderValueStyle);
            createCell(typeRow, 2, "养护单位", shadedHeaderLabelStyle);
            createCell(typeRow, 3, Objects.toString(reportData.getMaintainUnitName(), ""), shadedHeaderValueStyle);
            createCell(typeRow, 4, "涵洞编码", shadedHeaderLabelStyle); // E列空
            createCell(typeRow, 5, Objects.toString(reportData.getAssetCode(), ""), shadedHeaderValueStyle); // F列空
            applyRowBorder(typeRow, 0, 5, normalBorderStyle);
            setConditionalRowHeight(typeRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 5. 项目标题行 (带灰色背景)
            Row itemHeaderRow = sheet.createRow(currentRowIndex);
            itemHeaderRow.setHeightInPoints(25);
            createCell(itemHeaderRow, 0, "检查内容", itemHeaderStyleWithBorder); // A列
            createCell(itemHeaderRow, 1, "情况描述", itemHeaderStyleWithBorder); // B列 (将合并 BCD)
            createCell(itemHeaderRow, 2, "", itemHeaderStyleWithBorder); // C列占位
            createCell(itemHeaderRow, 3, "", itemHeaderStyleWithBorder); // D列占位
            createCell(itemHeaderRow, 4, "养护建议", itemHeaderStyleWithBorder); // E列 (将合并 EF)
            createCell(itemHeaderRow, 5, "", itemHeaderStyleWithBorder); // F列占位
            // 边框已通过样式应用，无需 applyRowBorder
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 3)); // 合并 B, C, D
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 5)); // 合并 E, F
            currentRowIndex++;

            // 6. 项目详情行
            List<PatrolAssetCheckDetail> details = reportData.getPatrolCheckDetailList();
            if (details != null && !details.isEmpty()) {
                for (PatrolAssetCheckDetail item : details) {
                    if (item == null) {
                        log.warn("跳过资产编码 {} 的null详情项", reportData.getAssetCode());
                        continue;
                    }
                    Row itemRow = sheet.createRow(currentRowIndex);
                    // A列: 检查内容 - 使用左对齐带边框样式
                    createCell(itemRow, 0, Objects.toString(item.getPartsTypeName(), ""), itemFirstColStyleWithBorder);

                    // BCD列: 情况描述 - 使用居中带边框样式
                    // 假设 item.getDefect() 包含描述 ("无")
                    createCell(itemRow, 1, item.getDefect() == null || item.getDefect().trim().isEmpty() ? "未见异常" : item.getDefect(), itemDescStyleWithBorder);
                    createCell(itemRow, 2, "", itemDescStyleWithBorder); // 占位
                    createCell(itemRow, 3, "", itemDescStyleWithBorder); // 占位
                    sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 3)); // 合并 B, C, D

                    // EF列: 养护建议 - 使用居中带边框样式
                    // 假设 item.getAdvice() 包含建议 ("无")
                    createCell(itemRow, 4, item.getAdvice() == null || item.getAdvice().trim().isEmpty() ? "正常保养" : item.getAdvice(), itemAdviceStyleWithBorder);
                    createCell(itemRow, 5, "", itemAdviceStyleWithBorder); // 占位
                    sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 5)); // 合并 E, F

                    // 边框已通过样式应用，无需 applyRowBorder
                    // 使用条件高度而不是固定高度
                    setConditionalRowHeight(itemRow, 0, 5, columnWidths);
                    currentRowIndex++;
                }
            } else {
                // 无详情行
                log.info("资产编码 {} 未找到检查详情", reportData.getAssetCode());
                Row emptyRow = sheet.createRow(currentRowIndex);
                createCell(emptyRow, 0, "无检查明细", itemFirstColStyleWithBorder); // 使用左对齐样式
                // 为合并和边框创建单元格
                for(int col=1; col<=5; col++) {
                    createCell(emptyRow, col, "", itemFirstColStyleWithBorder);
                }
                sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并A-F
                applyRowBorder(emptyRow, 0, 5, normalBorderStyle); // 确保边框
                // 使用条件高度
                setConditionalRowHeight(emptyRow, 0, 5, columnWidths);
                currentRowIndex++;
            }
            // 2. 添加空白行防止签名越界
            Row blankRow1 = sheet.createRow(currentRowIndex);
            createCell(blankRow1, 1, "", noBorderStyle); // 合并创建空单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            blankRow1.setHeightInPoints(5);
            currentRowIndex++;

            // 7. 页脚行 (标签和日期)
            int footerLabelRowIndex = currentRowIndex;
            Row footerLabelRow = sheet.createRow(footerLabelRowIndex);
            footerLabelRow.setHeightInPoints(25); // 可根据需要设置

            // A列: 负责人标签 (居中)
            createCell(footerLabelRow, 0, "负责人", footerLabelStyle);
            // B列: 负责人姓名/签名占位 (左对齐)
            createCell(footerLabelRow, 1, "", footerValueStyle);
            // C列: 记录人标签 (居中)
            createCell(footerLabelRow, 2, "记录人", footerLabelStyle);
            // D列: 记录人姓名/签名占位 (左对齐)
            createCell(footerLabelRow, 3, "", footerValueStyle);
            // E列: 检查日期标签 (居中)
            createCell(footerLabelRow, 4, "检查日期", footerLabelStyle);
            // F列: 检查日期值 (左对齐)
            Date checkDate = reportData.getCheckTime();
            createCell(footerLabelRow, 5, checkDate != null ? dateFormat.format(checkDate) : "", footerValueStyle);

            // 为标签行应用边框 (样式已包含边框，但可调用确保)
            applyRowBorder(footerLabelRow, 0, 5, normalBorderStyle);

            // 8. 页脚签名/姓名区域
            int signatureStartRowIndex = currentRowIndex; // 签名/姓名从这一行开始

            List<String> kahunaSignList = reportData.getKahunaSignList();
            List<String> oprUserSignList = reportData.getOprUserSignList();

            log.debug("负责人 ({}) 签名数量: {}", reportData.getKahunaName(), kahunaSignList != null ? kahunaSignList.size() : 0);
            log.debug("记录人 ({}) 签名数量: {}", reportData.getOprUserName(), oprUserSignList != null ? oprUserSignList.size() : 0);

            // 将负责人姓名/签名添加到B列 (索引1)
            int lastRowForKahuna;
            if (kahunaSignList != null && !kahunaSignList.isEmpty() && !signImageMap.isEmpty()) {
                // 从 signatureStartRowIndex 开始添加图像
                lastRowForKahuna = addSignatureImages(sheet, signatureStartRowIndex, 1,1,
                        kahunaSignList, Objects.toString(reportData.getKahunaName(), ""), footerValueStyle, true);
                // 如果有签名图，则不在标签行显示名字
            } else {
                // 没有签名或签名图像加载失败，在标签行的B列显示名字
                createCell(footerLabelRow, 1, Objects.toString(reportData.getKahunaName(), ""), footerValueStyle);
                lastRowForKahuna = signatureStartRowIndex; // 下一行从标签行之后开始
            }

            // 将记录人姓名/签名添加到D列 (索引3)
            int lastRowForOpr;
            if (oprUserSignList != null && !oprUserSignList.isEmpty() && !signImageMap.isEmpty()) {
                // 从 signatureStartRowIndex 开始添加图像
                lastRowForOpr = addSignatureImages(sheet, signatureStartRowIndex, 3,3,
                        oprUserSignList, Objects.toString(reportData.getOprUserName(), ""), footerValueStyle, true);
                // 如果有签名图，则不在标签行显示名字
            } else {
                // 没有签名或签名图像加载失败，在标签行的D列显示名字
                createCell(footerLabelRow, 3, Objects.toString(reportData.getOprUserName(), ""), footerValueStyle);
                lastRowForOpr = signatureStartRowIndex; // 下一行从标签行之后开始
            }

            // 确定页脚部分使用的最终行索引
            // 它是标签行下面的行，或者签名图像使用的最后一行中的较大者
            // 所以实际最后使用的行是 lastRowForKahuna - 1 和 lastRowForOpr - 1
            currentRowIndex = Math.max(lastRowForKahuna, lastRowForOpr); // 更新当前行索引到页脚部分的末尾

            // === 当前报告部分完成 ===

            // 在下一个报告部分之前插入分页符（如果不是最后一个）
            if (i < reportDataList.size() - 1) {
                // 在当前部分的最后一行之后设置分页符
                sheet.setRowBreak(currentRowIndex - 1);
                log.debug("已在行 {} 之后插入分页符", currentRowIndex - 1);
            }
        }
        log.info("已完成生成所有涵洞报告部分。");
    }
}
