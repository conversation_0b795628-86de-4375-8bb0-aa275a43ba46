package com.ruoyi.patroltidb.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 隧道巡检查子对象 patrol_tunnel_check_detail
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="隧道巡检查子")
@TableName("patrol_tunnel_check_detail")
@Data
public class PatrolTunnelCheckDetail extends PatrolAssetCheckDetail {
    private static final long serialVersionUID = 1L;

}

