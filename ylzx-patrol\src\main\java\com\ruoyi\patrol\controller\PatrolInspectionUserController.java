package com.ruoyi.patrol.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.patrol.domain.PatrolInspectionUser;
import com.ruoyi.patrol.service.PatrolInspectionUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 巡查人员关联Controller
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
@Api(tags = "巡查人员关联" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/inspectionUser")
public class PatrolInspectionUserController extends BaseController {
    final private PatrolInspectionUserService patrolInspectionUserService;


    /**
     * 查询巡查人员关联列表(分页)
     */
    @ApiOperation("查询巡查人员关联列表")
    //@RequiresPermissions("patrol:inspectionUser:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<PatrolInspectionUser> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<PatrolInspectionUser> list = patrolInspectionUserService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询巡查人员关联列表(不分页)
     */
    @ApiOperation("查询巡查人员关联列表(不分页)")
    //@RequiresPermissions("patrol:inspectionUser:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolInspectionUser> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolInspectionUser> list = patrolInspectionUserService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询巡查人员关联数据
     */
    @ApiOperation("根据id查询巡查人员关联数据")
    //@RequiresPermissions("patrol:inspectionUser:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            PatrolInspectionUser patrolInspectionUser = patrolInspectionUserService.getById(id);
        if (patrolInspectionUser == null) {
            return error("未查询到【巡查人员关联】记录");
        }
        return success(patrolInspectionUser);
    }

    /**
     * 新增巡查人员关联
     */
    @ApiOperation("新增巡查人员关联")
    //@RequiresPermissions("patrol:inspectionUser:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolInspectionUser patrolInspectionUser) {
        return toAjax(patrolInspectionUserService.save(patrolInspectionUser));
    }

    /**
     * 修改巡查人员关联
     */
    @ApiOperation("修改巡查人员关联")
    //@RequiresPermissions("patrol:inspectionUser:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolInspectionUser patrolInspectionUser) {
        return toAjax(patrolInspectionUserService.updateById(patrolInspectionUser));
    }

    /**
     * 删除巡查人员关联
     */
    @ApiOperation("删除巡查人员关联")
    //@RequiresPermissions("patrol:inspectionUser:remove")
    @DeleteMapping("/delete/{patrolId}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolInspectionUserService.removeById(id));
    }

    /**
     * 导出巡查人员关联列表
     */
    @ApiOperation("导出巡查人员关联列表")
    //@RequiresPermissions("patrol:inspectionUser:export")
    @Log(title = "巡查人员关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolInspectionUser> list = patrolInspectionUserService.list();
        ExcelUtil<PatrolInspectionUser> util = new ExcelUtil<PatrolInspectionUser>(PatrolInspectionUser.class);
        util.exportExcel(response, list, "巡查人员关联数据");
    }


}
