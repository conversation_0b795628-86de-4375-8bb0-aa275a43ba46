package com.ruoyi.engineering.service.impl;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.engineering.mapper.RoadEngineeringMapper;
import com.ruoyi.engineering.domain.RoadEngineering;
import com.ruoyi.engineering.domain.dto.RoadEngineeringDTO;
import com.ruoyi.engineering.service.RoadEngineeringService;

/**
 * 涉路工程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@Service
public class RoadEngineeringServiceImpl extends ServiceImpl<RoadEngineeringMapper, RoadEngineering> implements RoadEngineeringService {

    @Autowired
    private RoadEngineeringMapper roadEngineeringMapper;

    @Override
    public List<RoadEngineering> findListByParam(Map<String, Object> params) {
        return roadEngineeringMapper.findListByParam(params);
    }
    @Override
    public List<RoadEngineeringDTO> findAll(Map<String, Object> params) {
        return roadEngineeringMapper.findAll(params);
    }
    @Override
    public List<RoadEngineering> getUserPermissions(Map<String, Object> params) {
        return roadEngineeringMapper.getUserPermissions(params);
    }

}
