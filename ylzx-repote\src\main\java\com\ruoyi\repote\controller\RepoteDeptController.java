package com.ruoyi.repote.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.repote.domain.RepoteDept;
import com.ruoyi.repote.service.RepoteDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 填报单位Controller
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Api(tags = "填报单位" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/dept")
public class RepoteDeptController extends BaseController {
    final private RepoteDeptService repoteDeptService;


    /**
     * 查询填报单位列表(分页)
     */
    @ApiOperation("查询填报单位列表")
    //@RequiresPermissions("repote:dept:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<RepoteDept> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<RepoteDept> list = repoteDeptService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询填报单位列表(不分页)
     */
    @ApiOperation("查询填报单位列表(不分页)")
    //@RequiresPermissions("repote:dept:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<RepoteDept> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<RepoteDept> list = repoteDeptService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询填报单位数据
     */
    @ApiOperation("根据id查询填报单位数据")
    //@RequiresPermissions("repote:dept:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            RepoteDept repoteDept = repoteDeptService.getById(id);
        if (repoteDept == null) {
            return error("未查询到【填报单位】记录");
        }
        return success(repoteDept);
    }

    /**
     * 新增填报单位
     */
    @ApiOperation("新增填报单位")
    //@RequiresPermissions("repote:dept:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody RepoteDept repoteDept) {
        return toAjax(repoteDeptService.save(repoteDept));
    }

    /**
     * 修改填报单位
     */
    @ApiOperation("修改填报单位")
    //@RequiresPermissions("repote:dept:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody RepoteDept repoteDept) {
        return toAjax(repoteDeptService.updateById(repoteDept));
    }

    /**
     * 删除填报单位
     */
    @ApiOperation("删除填报单位")
    //@RequiresPermissions("repote:dept:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(repoteDeptService.removeById(id));
    }

    /**
     * 导出填报单位列表
     */
    @ApiOperation("导出填报单位列表")
    //@RequiresPermissions("repote:dept:export")
    @Log(title = "填报单位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<RepoteDept> list = repoteDeptService.list();
        ExcelUtil<RepoteDept> util = new ExcelUtil<RepoteDept>(RepoteDept.class);
        util.exportExcel(response, list, "填报单位数据");
    }


}
