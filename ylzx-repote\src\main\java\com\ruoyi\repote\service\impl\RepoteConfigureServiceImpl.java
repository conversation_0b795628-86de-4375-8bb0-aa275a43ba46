package com.ruoyi.repote.service.impl;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repote.mapper.RepoteConfigureMapper;
import com.ruoyi.repote.domain.RepoteConfigure;
import com.ruoyi.repote.service.RepoteConfigureService;

/**
 * 填报配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class RepoteConfigureServiceImpl extends ServiceImpl<RepoteConfigureMapper, RepoteConfigure> implements RepoteConfigureService {

    @Autowired
    private RepoteConfigureMapper repoteConfigureMapper;

    @Override
    public List<RepoteConfigure> findListByParam(Map<String, Object> params) {
        return repoteConfigureMapper.findListByParam(params);
    }


}
