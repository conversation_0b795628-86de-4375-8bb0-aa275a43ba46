<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.engineering.mapper.RoadEngineeringMapper">

    <resultMap type="com.ruoyi.engineering.domain.RoadEngineering" id="RoadEngineeringResult">
            <result property="id" column="id"/>
            <result property="documentReceived" column="document_received"/>
            <result property="projectStatus" column="project_status"/>
            <result property="compensationSituation" column="compensation_situation"/>
            <result property="managementUnit" column="management_unit"/>
            <result property="managementUnitId" column="management_unit_id"/>
            <result property="maintenanceSection" column="maintenance_section"/>
            <result property="maintenanceSectionId" column="maintenance_section_id"/>
            <result property="roadCode" column="road_code"/>
            <result property="submissionTime" column="submission_time"/>
            <result property="reportingUnit" column="reporting_unit"/>
            <result property="startPoint" column="start_point"/>
            <result property="endPoint" column="end_point"/>
            <result property="applicationContent" column="application_content"/>
            <result property="plannedStartDate" column="planned_start_date"/>
            <result property="plannedCompletionDate" column="planned_completion_date"/>
            <result property="isTrafficControl" column="is_traffic_control"/>
            <result property="contactPerson" column="contact_person"/>
            <result property="phoneNumber" column="phone_number"/>
            <result property="remarks" column="remarks"/>
            <result property="replyDocumentCode" column="reply_document_code"/>
            <result property="replyTime" column="reply_time"/>
            <result property="replyOpinions" column="reply_opinions"/>
            <result property="agreementNumber" column="agreement_number"/>
            <result property="roadPropertyAgreementNumber" column="road_property_agreement_number"/>
            <result property="agreementAmount" column="agreement_amount"/>
            <result property="direction" column="direction"/>
            <result property="constructionUnit" column="construction_unit"/>
            <result property="designUnit" column="design_unit"/>
            <result property="constructionCompany" column="construction_company"/>
            <result property="supervisionUnit" column="supervision_unit"/>
            <result property="implementationContent" column="implementation_content"/>
    </resultMap>

    <sql id="base_column">
 document_received, project_status, compensation_situation, management_unit, management_unit_id, maintenance_section, maintenance_section_id, road_code, submission_time, reporting_unit, start_point, end_point, application_content, planned_start_date, planned_completion_date, is_traffic_control, contact_person, phone_number, remarks, reply_document_code, reply_time, reply_opinions, agreement_number, road_property_agreement_number, agreement_amount, direction, construction_unit, design_unit, construction_company, supervision_unit,implementation_content    </sql>

    <sql id="where_column">
        <if test="documentReceived != null and documentReceived != ''">
            AND document_received = #{document_received}
        </if>
        <if test="documentReceivedLike != null and documentReceivedLike != ''">
            AND document_received like CONCAT('%', #{documentReceivedLike}, '%')
        </if>
        <if test="projectStatus != null and projectStatus != ''">
            AND project_status = #{projectStatus}
        </if>
        <if test="compensationSituation != null and compensationSituation != ''">
            AND compensation_situation = #{compensationSituation}
        </if>
        <if test="managementUnit != null and managementUnit != ''">
            AND management_unit = #{managementUnit}
        </if>
        <if test="managementUnitId != null and managementUnitId != ''">
            AND management_unit_id = #{managementUnitId}
        </if>
        <if test="maintenanceSection != null and maintenanceSection != ''">
            AND maintenance_section = #{maintenanceSection}
        </if>
        <if test="maintenanceSectionId != null and maintenanceSectionId != ''">
            AND maintenance_section_id = #{maintenanceSectionId}
        </if>
        <if test="roadCode != null and roadCode != ''">
            AND road_code = #{roadCode}
        </if>
        <if test="submissionTime != null and submissionTime != ''">
            AND submission_time = #{submissionTime}
        </if>
        <if test="submissionTimes != null and submissionTimes != ''">
            AND date_format(submission_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(#{submissionTimes}, '%Y-%m-%d')
        </if>
        <if test="submissionTimee != null and submissionTimee != ''">
            AND date_format(submission_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(#{submissionTimee}, '%Y-%m-%d')
        </if>
        <if test="reportingUnit != null and reportingUnit != ''">
            AND reporting_unit = #{reportingUnit}
        </if>
        <if test="startPoint != null and startPoint != ''">
            AND start_point = #{startPoint}
        </if>
        <if test="endPoint != null and endPoint != ''">
            AND end_point = #{endPoint}
        </if>
        <if test="applicationContent != null and applicationContent != ''">
            AND application_content = #{applicationContent}
        </if>
        <if test="plannedStartDate != null and plannedStartDate != ''">
            AND planned_start_date = #{plannedStartDate}
        </if>
        <if test="plannedCompletionDate != null and plannedCompletionDate != ''">
            AND planned_completion_date = #{plannedCompletionDate}
        </if>
        <if test="isTrafficControl != null and isTrafficControl != ''">
            AND is_traffic_control = #{isTrafficControl}
        </if>
        <if test="contactPerson != null and contactPerson != ''">
            AND contact_person = #{contactPerson}
        </if>
        <if test="phoneNumber != null and phoneNumber != ''">
            AND phone_number = #{phoneNumber}
        </if>
        <if test="remarks != null and remarks != ''">
            AND remarks = #{remarks}
        </if>
        <if test="replyDocumentCode != null and replyDocumentCode != ''">
            AND reply_document_code = #{replyDocumentCode}
        </if>
        <if test="replyTime != null and replyTime != ''">
            AND reply_time = #{replyTime}
        </if>
        <if test="replyOpinions != null and replyOpinions != ''">
            AND reply_opinions = #{replyOpinions}
        </if>
        <if test="agreementNumber != null and agreementNumber != ''">
            AND agreement_number = #{agreementNumber}
        </if>
        <if test="roadPropertyAgreementNumber != null and roadPropertyAgreementNumber != ''">
            AND road_property_agreement_number = #{roadPropertyAgreementNumber}
        </if>
        <if test="agreementAmount != null and agreementAmount != ''">
            AND agreement_amount = #{agreementAmount}
        </if>
        <if test="direction != null and direction != ''">
            AND direction = #{direction}
        </if>
        <if test="constructionUnit != null and constructionUnit != ''">
            AND construction_unit = #{constructionUnit}
        </if>
        <if test="designUnit != null and designUnit != ''">
            AND design_unit = #{designUnit}
        </if>
        <if test="constructionCompany != null and constructionCompany != ''">
            AND construction_company = #{constructionCompany}
        </if>
        <if test="supervisionUnit != null and supervisionUnit != ''">
            AND supervision_unit = #{supervisionUnit}
        </if>
        <if test="supervisionUnit != null and supervisionUnit != ''">
            AND supervision_unit = #{supervisionUnit}
        </if>
    </sql>

    <sql id="set_column">
            <if test="documentReceived != null">
                document_received = #{documentReceived},
            </if>
            <if test="projectStatus != null">
                project_status = #{projectStatus},
            </if>
            <if test="compensationSituation != null">
                compensation_situation = #{compensationSituation},
            </if>
            <if test="managementUnit != null">
                management_unit = #{managementUnit},
            </if>
            <if test="managementUnitId != null">
                management_unit_id = #{managementUnitId},
            </if>
            <if test="maintenanceSection != null">
                maintenance_section = #{maintenanceSection},
            </if>
            <if test="maintenanceSectionId != null">
                maintenance_section_id = #{maintenanceSectionId},
            </if>
            <if test="roadCode != null">
                road_code = #{roadCode},
            </if>
            <if test="submissionTime != null">
                submission_time = #{submissionTime},
            </if>
            <if test="reportingUnit != null">
                reporting_unit = #{reportingUnit},
            </if>
            <if test="startPoint != null">
                start_point = #{startPoint},
            </if>
            <if test="endPoint != null">
                end_point = #{endPoint},
            </if>
            <if test="applicationContent != null">
                application_content = #{applicationContent},
            </if>
            <if test="plannedStartDate != null">
                planned_start_date = #{plannedStartDate},
            </if>
            <if test="plannedCompletionDate != null">
                planned_completion_date = #{plannedCompletionDate},
            </if>
            <if test="isTrafficControl != null">
                is_traffic_control = #{isTrafficControl},
            </if>
            <if test="contactPerson != null">
                contact_person = #{contactPerson},
            </if>
            <if test="phoneNumber != null">
                phone_number = #{phoneNumber},
            </if>
            <if test="remarks != null">
                remarks = #{remarks},
            </if>
            <if test="replyDocumentCode != null">
                reply_document_code = #{replyDocumentCode},
            </if>
            <if test="replyTime != null">
                reply_time = #{replyTime},
            </if>
            <if test="replyOpinions != null">
                reply_opinions = #{replyOpinions},
            </if>
            <if test="agreementNumber != null">
                agreement_number = #{agreementNumber},
            </if>
            <if test="roadPropertyAgreementNumber != null">
                road_property_agreement_number = #{roadPropertyAgreementNumber},
            </if>
            <if test="agreementAmount != null">
                agreement_amount = #{agreementAmount},
            </if>
            <if test="direction != null">
                direction = #{direction},
            </if>
            <if test="constructionUnit != null">
                construction_unit = #{constructionUnit},
            </if>
            <if test="designUnit != null">
                design_unit = #{designUnit},
            </if>
            <if test="constructionCompany != null">
                construction_company = #{constructionCompany},
            </if>
            <if test="implementationContent != null">
                implementation_content = #{implementationContent},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="RoadEngineeringResult">
        SELECT
        <include refid="base_column"/>
        FROM road_engineering
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
<!--    <select id="findListByParam" resultMap="RoadEngineeringResult">-->
<!--        SELECT-->
<!--        <include refid="base_column"/>-->
<!--        FROM road_engineering-->
<!--        <where>-->
<!--            <include refid="where_column"/>-->
<!--        </where>-->
<!--        <if test="order != null and order != ''">-->
<!--            order by ${order}-->
<!--        </if>-->
<!--        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">-->
<!--            limit #{pageNum},#{pageSize}-->
<!--        </if>-->
<!--    </select>-->
    <select id="findListByParam" resultMap="RoadEngineeringResult">
        SELECT
        <include refid="base_column"/>
        FROM road_engineering
        <where>
            <if test="documentReceived != null and documentReceived != ''">
                AND document_received = #{documentReceived}
            </if>
            <if test="projectStatus != null and projectStatus != ''">
                AND project_status = #{projectStatus}
            </if>
            <if test="compensationSituation != null and compensationSituation != ''">
                AND compensation_situation = #{compensationSituation}
            </if>
            <if test="managementUnitId != null">
                AND management_unit_id = #{managementUnitId}
            </if>
            <if test="maintenanceSectionId != null">
                AND maintenance_section_id = #{maintenanceSectionId}
            </if>

        </where>
        <if test="order != null and order != ''">
            ORDER BY ${order}
        </if>
    </select>


    <select id="findAll" resultMap="RoadEngineeringResult">
        SELECT
        <include refid="base_column"/>,
        rel.lane  <!-- 关联表中的列为 lane -->
        FROM road_engineering re
        LEFT JOIN road_engineering_lane rel ON re.id = rel.road_engineering_id  <!-- 连接正确的表 -->
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            ORDER BY ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            LIMIT #{pageNum},#{pageSize}
        </if>
    </select>


    <select id="getUserPermissions" resultMap="RoadEngineeringResult">
        SELECT DISTINCT re.*
        FROM road_engineering re
        LEFT JOIN road_engineering_lane rel ON re.id = rel.road_engineering_id
        <where>
            <include refid="where_column"/>

            <!-- 使用 userDeptList 和 userMaintenanceIds 进行过滤 -->
            <if test="userDeptList != null and userDeptList.size() > 0">
                AND re.management_unit_id IN
                <foreach collection="userDeptList" item="userDeptList" open="(" separator="," close=")">
                    #{userDeptList}
                </foreach>
            </if>

            <if test="userMaintenanceIds != null and userMaintenanceIds.size() > 0">
                AND re.maintenance_section_id IN
                <foreach collection="userMaintenanceIds" item="userMaintenanceIds" open="(" separator="," close=")">
                    #{userMaintenanceIds}
                </foreach>
            </if>
        </where>

        <if test="order != null and order != ''">
            ORDER BY ${order}
        </if>

    </select>


    <!--    <select id="findAll" resultMap="RoadEngineeringResult">-->
<!--        SELECT-->
<!--        <include refid="base_column"/>,-->
<!--        rel.lane  &lt;!&ndash; 关联表中的列为 lane &ndash;&gt;-->
<!--        FROM road_engineering re-->
<!--        LEFT JOIN road_engineering_lane rel ON re.id = rel.road_engineering_id  &lt;!&ndash; 连接正确的表 &ndash;&gt;-->
<!--        <where>-->
<!--            <include refid="where_column"/>-->
<!--        </where>-->
<!--        <if test="orderByField != null and orderByField != ''">-->
<!--            ORDER BY ${orderByField}-->
<!--        </if>-->
<!--    </select>-->

</mapper>