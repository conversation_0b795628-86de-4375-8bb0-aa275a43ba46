package com.ruoyi.engineering.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.engineering.domain.RoadEngineeringLane;
import com.ruoyi.engineering.mapper.RoadEngineeringLaneMapper;
import com.ruoyi.engineering.service.RoadEngineeringLaneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 涉路工程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@Service
public class RoadEngineeringLaneServiceImpl extends ServiceImpl<RoadEngineeringLaneMapper, RoadEngineeringLane> implements RoadEngineeringLaneService {

    @Autowired
    private RoadEngineeringLaneMapper roadEngineeringLaneMapper;

    @Override
    public List<RoadEngineeringLane> findListByParam(Map<String, Object> params) {
        return roadEngineeringLaneMapper.findListByParam(params);
    }

    @Override
    public List<String> getByEngineeringId(String engineeringId) {
        return roadEngineeringLaneMapper.getByEngineeringId(engineeringId);
    }


}
