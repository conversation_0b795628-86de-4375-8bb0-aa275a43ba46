package com.ruoyi.patrol.domain.dto;

import com.ruoyi.patrol.domain.PatrolInspectionGeo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月27日 16:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PatrolInspectionGeoDto extends PatrolInspectionGeo {
    @ApiModelProperty(value = "巡查里程（km）")
    private BigDecimal patrolMileage;
    @ApiModelProperty(value = "巡查状态0:正在巡查 1:巡查完成")
    private Integer status = 0;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "巡查种类 1:桥梁,2：隧道")
    private List<Integer> list;
}
