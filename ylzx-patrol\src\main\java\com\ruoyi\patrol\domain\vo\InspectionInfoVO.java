package com.ruoyi.patrol.domain.vo;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.patrol.enums.InspectionType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * @Description:
 * @author: QD
 * @date: 2024年09月12日 15:13
 */
@ApiModel(value = "App巡查信息")
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class InspectionInfoVO {
    // 巡查类型
    @EnumValue
    @ApiModelProperty(value = "巡查类型 1: 桥梁日常巡查, 2: 桥梁经常检查,3': '涵洞定期检查','4': '涵洞经常检查','5': '隧道日常巡查','6': '隧道经常检查'")
    private InspectionType inspectionType;

    // 巡查类型名称
    private String inspectionTypeName;

    // 巡查描述
    private String inspectionDesc;

    // 资产id
    private String assetId;

    // 资产名称
    private String assetName;

    // 到期时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime expireTime;

    public String getInspectionTypeName() {
        return inspectionType.getDescription();
    }

    public String getInspectionDesc() {
        StringBuilder sb = new StringBuilder();
        sb.append(inspectionType.getDescription());
        sb.append("-");
        sb.append(assetName);
        sb.append("-");
        sb.append("巡查:");
        LocalDateTime now = LocalDateTime.now();
        if (expireTime.isBefore(now)) {
            sb.append("已过期");
        } else {
            sb.append("剩余");
            sb.append(expireTime.getDayOfYear() - now.getDayOfYear());
            sb.append("天");
        }

        return sb.toString();
    }
}
