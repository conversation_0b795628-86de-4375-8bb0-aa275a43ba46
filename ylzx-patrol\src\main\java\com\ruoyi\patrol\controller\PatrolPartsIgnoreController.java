package com.ruoyi.patrol.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.patrol.domain.PatrolPartsIgnore;
import com.ruoyi.patrol.service.PatrolPartsIgnoreService;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 检查项排除Controller
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Api(tags = "检查项排除" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/partsIgnore")
public class PatrolPartsIgnoreController extends BaseController {
    final private PatrolPartsIgnoreService patrolPartsIgnoreService;


    /**
     * 查询检查项排除列表(分页)
     */
    @Operation(summary = "查询检查项排除列表")
    //@RequiresPermissions("patrol:partsIgnore:list")
    @GetMapping("/list")
    public TableDataInfo list(PatrolPartsIgnore patrolPartsIgnore) {
        startPage();
        List<PatrolPartsIgnore> list = patrolPartsIgnoreService.list(patrolPartsIgnore);
        return getDataTable(list);
    }

    /**
     * 查询检查项排除列表(不分页)
     */
    @Operation(summary = "查询检查项排除列表(不分页)")
    //@RequiresPermissions("patrol:partsIgnore:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll(PatrolPartsIgnore patrolPartsIgnore) {
        List<PatrolPartsIgnore> list = patrolPartsIgnoreService.list(patrolPartsIgnore);
        return success(list);
    }

    /**
     * 根据id查询检查项排除数据
     */
    @Operation(summary = "根据id查询检查项排除数据")
    //@RequiresPermissions("patrol:partsIgnore:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            PatrolPartsIgnore patrolPartsIgnore = patrolPartsIgnoreService.getById(id);
        if (patrolPartsIgnore == null) {
            return error("未查询到【检查项排除】记录");
        }
        return success(patrolPartsIgnore);
    }

    /**
     * 新增检查项排除
     */
    @Operation(summary = "新增检查项排除")
    //@RequiresPermissions("patrol:partsIgnore:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolPartsIgnore patrolPartsIgnore) {
        return toAjax(patrolPartsIgnoreService.save(patrolPartsIgnore));
    }


    /**
     * 批量新增检查项排除
     */
    @Operation(summary = "新增检查项排除")
    //@RequiresPermissions("patrol:partsIgnore:add")
    @PostMapping("/addBatch")
    public AjaxResult addBatch( @RequestBody  List<PatrolPartsIgnore> patrolPartsIgnoreList) {
        return toAjax(patrolPartsIgnoreService.saveBatch(patrolPartsIgnoreList));
    }

    /**
     * 修改检查项排除
     */
    @Operation(summary = "修改检查项排除")
    //@RequiresPermissions("patrol:partsIgnore:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolPartsIgnore patrolPartsIgnore) {
        return toAjax(patrolPartsIgnoreService.updateById(patrolPartsIgnore));
    }

    /**
     * 删除检查项排除
     */
    @Operation(summary = "删除检查项排除")
    //@RequiresPermissions("patrol:partsIgnore:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolPartsIgnoreService.removeById(id));
    }

    /**
     * 根据条件删除检查项排除
     */
    @Operation(summary = "删除检查项排除")
    //@RequiresPermissions("patrol:partsIgnore:remove")
    @DeleteMapping("/removeByCondition")
    public AjaxResult removeByCondition(@RequestBody PatrolPartsIgnore patrolPartsIgnore) {
        QueryWrapper<PatrolPartsIgnore> qw = new QueryWrapper<>();
        qw.setEntity(patrolPartsIgnore);
        return toAjax(patrolPartsIgnoreService.remove(qw));
    }


    /**
     * 导出检查项排除列表
     */
    @Operation(summary = "导出检查项排除列表")
    //@RequiresPermissions("patrol:partsIgnore:export")
    @Log(title = "检查项排除", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolPartsIgnore> list = patrolPartsIgnoreService.list();
        ExcelUtil<PatrolPartsIgnore> util = new ExcelUtil<PatrolPartsIgnore>(PatrolPartsIgnore.class);
        util.exportExcel(response, list, "检查项排除数据");
    }


}
