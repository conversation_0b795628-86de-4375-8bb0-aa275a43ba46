<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.PatrolInspectionLastMapper">
    <resultMap type="com.ruoyi.patrol.domain.PatrolInspectionLast" id="PatrolInspectionLastMap">
        <result property="assetId" column="asset_id" jdbcType="VARCHAR"/>
        <result property="generatedAt" column="generated_at" jdbcType="TIMESTAMP"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="assetId" useGeneratedKeys="true">
        INSERT INTO patrol_inspection_last (
        asset_id, generated_at, type
        ) values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.assetId}, #{entity.generatedAt}, #{entity.type})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        INSERT INTO patrol_inspection_last (
        asset_id, generated_at, type
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.assetId}, #{entity.generatedAt}, #{entity.type})
        </foreach>
        ON DUPLICATE KEY UPDATE
        generated_at = VALUES(generated_at),
        type = VALUES(type)
    </insert>

    <insert id="insertOrUpdateBatchByAssetIdList" parameterType="map">
        INSERT INTO patrol_inspection_last (
        asset_id
        <if test="generatedAt != null">, generated_at</if>
        <if test="generatedBy != null">, generated_by</if>
        , type
        ) VALUES
        <foreach collection="assetIds" item="assetId" separator=",">
            (#{assetId}
            <if test="generatedAt != null">, #{generatedAt}</if>
            <if test="generatedBy != null">, #{generatedBy}</if>
            , #{type})
        </foreach>
        ON DUPLICATE KEY UPDATE
        <if test="generatedAt != null">generated_at = VALUES(generated_at),</if>
        <if test="generatedBy != null">generated_by = VALUES(generated_by),</if>
        type = VALUES(type)
    </insert>

</mapper>
