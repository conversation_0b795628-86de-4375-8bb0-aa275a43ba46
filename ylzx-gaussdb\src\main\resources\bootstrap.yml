# Tomcat
server:
  port: 19209

# Spring
spring:
  application:
    # 应用名称
    name: ylzx-gaussdb
  profiles:
    # 环境配置
    active: prod
  cloud:
    inetutils:
      preferred-networks:
      - 172.18.0
      - 192.168.10
#      ignored-interfaces:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 192.168.101.121:8848
        namespace: 24aaca1b-7113-4947-ba4e-5314fdc2011f
      config:
        # 配置中心地址
        server-addr: 192.168.101.121:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - common-redis.yml
#          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        namespace: 24aaca1b-7113-4947-ba4e-5314fdc2011f

# spring配置
---
spring:
  datasource:
    driver-class-name: com.huawei.opengauss.jdbc.Driver
    url: ****************************************************************
    username: ynjt_ylzx
    password: Ynjtylzx@2024#

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  #configLocation: classpath:mybatis/mybatis-config.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    enable-sql-runner: true
