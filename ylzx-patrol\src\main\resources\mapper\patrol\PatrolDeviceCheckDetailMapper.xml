<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patroltidb.mapper.PatrolDeviceCheckDetailMapper">

    <resultMap type="com.ruoyi.patroltidb.domain.PatrolDeviceCheckDetail" id="PatrolDeviceCheckDetailResult">
        <result property="id" column="id"/>
        <result property="checkId" column="check_id"/>
        <result property="devName" column="dev_name"/>
        <result property="location" column="location"/>
        <result property="content" column="content"/>
        <result property="result" column="result"/>
        <result property="describe" column="describe"/>
        <result property="measures" column="measures"/>
        <result property="picPaths" column="pic_paths"/>
        <result property="carLicense" column="car_license"/>
        <result property="nums" column="nums"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">id,update_time,create_time,create_by,update_by,
        check_id, dev_name, location, content, result, `describe`, measures, pic_paths, car_license, nums, remark    </sql>

    <sql id="where_column">
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="ids != null and ids.size() > 0 ">
            AND id IN
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="checkId != null and checkId != ''">
            AND check_id = #{checkId}
        </if>
        <if test="checkIdLike != null and checkIdLike != ''">
            AND check_id like CONCAT('%', #{checkIdLike}, '%')
        </if>
        <if test="devName != null and devName != ''">
            AND dev_name = #{devName}
        </if>
        <if test="location != null and location != ''">
            AND location = #{location}
        </if>
        <if test="content != null and content != ''">
            AND content = #{content}
        </if>
        <if test="result != null and result != ''">
            AND result = #{result}
        </if>
        <if test="describe != null and describe != ''">
            AND describe = #{describe}
        </if>
        <if test="measures != null and measures != ''">
            AND measures = #{measures}
        </if>
        <if test="picPaths != null and picPaths != ''">
            AND pic_paths = #{picPaths}
        </if>
        <if test="carLicense != null and carLicense != ''">
            AND car_license = #{carLicense}
        </if>
        <if test="nums != null and nums != ''">
            AND nums = #{nums}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}
        </if>
    </sql>

    <sql id="set_column">
            <if test="checkId != null">
                check_id = #{checkId},
            </if>
            <if test="devName != null">
                dev_name = #{devName},
            </if>
            <if test="location != null">
                location = #{location},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="result != null">
                result = #{result},
            </if>
            <if test="describe != null">
                describe = #{describe},
            </if>
            <if test="measures != null">
                measures = #{measures},
            </if>
            <if test="picPaths != null">
                pic_paths = #{picPaths},
            </if>
            <if test="carLicense != null">
                car_license = #{carLicense},
            </if>
            <if test="nums != null">
                nums = #{nums},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="PatrolDeviceCheckDetailResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_device_check_detail
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="PatrolDeviceCheckDetailResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_device_check_detail
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
<!--        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">-->
<!--            limit #{pageNum},#{pageSize}-->
<!--        </if>-->
    </select>

</mapper>