package com.ruoyi.engineering.mapper;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.engineering.domain.RoadEngineering;
import com.ruoyi.engineering.domain.dto.RoadEngineeringDTO;
import org.apache.ibatis.annotations.Mapper;

import javax.annotation.ManagedBean;

/**
 * 涉路工程车道Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@Mapper
public interface RoadEngineeringMapper extends BaseMapper<RoadEngineering> {

    List<RoadEngineering> findListByParam(Map<String, Object> params);

    List<RoadEngineeringDTO> findAll(Map<String, Object> params);

    List<RoadEngineering> getUserPermissions(Map<String, Object> params);
}
