package com.ruoyi.patroltidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patroltidb.domain.FileInfoEntity;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Transactional(rollbackFor = Exception.class)
public interface FileInfoService extends IService<FileInfoEntity> {

    /**
     * 批量新增
     */
    Long insertBatch(List<FileInfoEntity> list);


}
