package com.ruoyi.patrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patrol.domain.PatrolPartsIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查项排除Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Mapper
public interface PatrolPartsIgnoreMapper extends BaseMapper<PatrolPartsIgnore> {

    /**
     * 根据资源ID列表查询检查项排除记录
     *
     * @param assetIds 资源ID列表
     * @return 检查项排除记录列表
     */
    List<PatrolPartsIgnore> selectByAssetIds(@Param("list") List<String> list);

}
