package com.ruoyi.patrol.service.impl;/**
 *
 */

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.patrol.domain.PatrolInspectionGeo;
import com.ruoyi.patrol.mapper.PatrolInspectionGeoMapper;
import com.ruoyi.patrol.service.PatrolInspectionGeoService;
import com.ruoyi.patrol.utils.GeoUtils;
import org.springframework.stereotype.Service;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月26日 16:11
 */
@Service
@Master
public class PatrolInspectionGeoServiceImpl extends ServiceImpl<PatrolInspectionGeoMapper, PatrolInspectionGeo>
        implements PatrolInspectionGeoService {

    /**
     * 保存巡查日志地理数据
     */
    public boolean saveTrace(PatrolInspectionGeo patrolInspectionGeo) {
        PatrolInspectionGeo entity = selectGeoById(patrolInspectionGeo.getLogId());
        if (entity != null) {
            String wktMerge = GeoUtils.mergeLineStrings(entity.getShape(), patrolInspectionGeo.getShape());
            entity.setShape(wktMerge);
            return updateById(entity);
        }else{
            return save(patrolInspectionGeo);
        }
    }

    /**
     * 根据id查询巡查日志地理数据(对地理数据进行处理)
     * @param id 巡查日志id
     * @return 结果
     */
    public PatrolInspectionGeo selectGeoById(String id){
        return baseMapper.selectGeoById(id);
    }

}
