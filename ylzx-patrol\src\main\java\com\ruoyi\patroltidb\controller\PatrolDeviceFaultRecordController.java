package com.ruoyi.patroltidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheck;
import com.ruoyi.patroltidb.domain.PatrolDeviceFaultRecord;
import com.ruoyi.patroltidb.domain.excel.DeviceMonthlyReportExport;
import com.ruoyi.patroltidb.service.PatrolDeviceCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolDeviceCheckService;
import com.ruoyi.patroltidb.service.PatrolDeviceFaultRecordService;
import com.ruoyi.system.api.RemoteDeptAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 机电故障上报Controller
 * 
 * <AUTHOR>
 * @date 2024-08-29
 */
@Api(tags = "机电故障上报" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/deviceFaultRecord")
public class PatrolDeviceFaultRecordController extends BaseController {
    final private PatrolDeviceFaultRecordService patrolDeviceFaultRecordService;
    @Resource
    private PatrolDeviceCheckService patrolDeviceCheckService;
    @Resource
    private PatrolDeviceCheckDetailService patrolDeviceCheckDetailService;
    @Resource
    private RemoteDeptAuthService deptAuthService;

    /**
     * 查询机电故障上报列表(分页)
     */
    @ApiOperation("查询机电故障上报列表")
    //@RequiresPermissions("patrol:deviceFaultRecord:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<PatrolDeviceFaultRecord> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<PatrolDeviceFaultRecord> list = patrolDeviceFaultRecordService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询机电故障上报列表(不分页)
     */
    @ApiOperation("查询机电故障上报列表(不分页)")
    //@RequiresPermissions("patrol:deviceFaultRecord:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolDeviceFaultRecord> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolDeviceFaultRecord> list = patrolDeviceFaultRecordService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询机电故障上报数据
     */
    @ApiOperation("根据id查询机电故障上报数据")
    //@RequiresPermissions("patrol:deviceFaultRecord:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        PatrolDeviceFaultRecord patrolDeviceFaultRecord = patrolDeviceFaultRecordService.getById(id);
        PatrolDeviceCheck deviceCheck = patrolDeviceCheckService.getById(patrolDeviceFaultRecord.getCheckId());
        if (deviceCheck == null) {
            return error("未查询到【隧道机电日常巡查】记录");
        }
        deviceCheck.setFaultRecordList(List.of(patrolDeviceFaultRecord));
        return success(deviceCheck);
    }

    /**
     * 删除数据
     *
     * @param idList 主键集合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public AjaxResult delete(@RequestBody List<String> idList) {
        QueryWrapper<PatrolDeviceFaultRecord> qw;
        qw = new QueryWrapper<>();
        qw.in("id", idList);
        List<PatrolDeviceFaultRecord> detailList = patrolDeviceFaultRecordService.list(qw);
        for (PatrolDeviceFaultRecord record : detailList) {
            patrolDeviceCheckService.removeById(record.getCheckId());
        }
        return success(patrolDeviceFaultRecordService.removeByIds(idList));
    }

    /**
     * 导出机电故障月报数据清单
     */
    @ApiOperation("导出机电故障月报数据清单")
    @RequiresPermissions("patrol:deviceFaultRecord:export")
    @Log(title = "导出机电故障月报数据清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestParam Map<String, Object> params, HttpServletResponse response) {
        List<DeviceMonthlyReportExport> list;
        Long userId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(userId)) {
            R<List<String>> userTunnelIds = deptAuthService.findUserTunnelIds(userId);
            if (userTunnelIds.getCode() == 200 && !userTunnelIds.getData().isEmpty()) {
                params.put("tunnelIdList", userTunnelIds.getData());
                list = patrolDeviceCheckService.listMonthlyReport(params);
            } else {
                list = new ArrayList<>();
            }
        } else {
            list = patrolDeviceCheckService.listMonthlyReport(params);
        }
        ExcelUtil<DeviceMonthlyReportExport> util = new ExcelUtil<>(DeviceMonthlyReportExport.class);
        util.exportExcel(response, list, "机电故障月报数据清单");
    }


}
