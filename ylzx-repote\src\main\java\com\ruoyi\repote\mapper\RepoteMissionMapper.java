package com.ruoyi.repote.mapper;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.repote.domain.RepoteMission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Resource;

/**
 * 填报任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Mapper
public interface RepoteMissionMapper extends BaseMapper<RepoteMission> {

    List<RepoteMission> findListByParam(Map<String, Object> params);

    Long getCountByReportMission(@Param("reportMission") String reportMission);


}
