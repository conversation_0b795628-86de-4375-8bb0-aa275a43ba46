package com.ruoyi.patrol.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description:
 * @author: sfc
 * @date: 2025年02月21日 14:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MileageCountVO {

    @ApiModelProperty("路段ID")
    private String maintenanceSectionId;

    @ApiModelProperty("路段名称")
    private String maintenanceSectionName;

    @ApiModelProperty("路面管养里程（km）")
    private BigDecimal roadbedMileage;

}
