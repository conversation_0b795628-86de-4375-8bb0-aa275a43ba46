package com.ruoyi.patrol.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 巡查类型
 * 检查类型 1: 桥梁日常巡查, 2: 桥梁经常检查,3': '涵洞定期检查','4': '涵洞经常检查',c'5': '隧道日常巡查','6': '隧道经常检查'
 */
@Getter
@RequiredArgsConstructor
public enum InspectionType implements IEnum<String> {
    BRIDGE_DAILY_INSPECTION("1", "桥梁日常巡查",AssetType.BRIDGE,true),
    BRIDGE_REGULAR_INSPECTION("2", "桥梁经常检查",AssetType.BRIDGE,false),
    CULVERT_DAILY_INSPECTION("3", "涵洞日常检查",AssetType.CULVERT,true),
    CULVERT_REGULAR_INSPECTION("4", "涵洞经常检查",AssetType.CULVERT,false),
    TUNNEL_DAILY_INSPECTION("5", "隧道日常巡查",AssetType.TUNNEL,true),
    TUNNEL_REGULAR_INSPECTION("6", "隧道经常检查",AssetType.TUNNEL,false),
    DEVICE_DAILY_INSPECTION("7", "隧道机电日常检查",AssetType.DEVICE,true),
    DEVICE_REGULAR_INSPECTION("8", "隧道机电经常检查",AssetType.DEVICE,false);

    private final String code;
    private final String description;
    private final AssetType assetType;
    private final Boolean flag;

    @Override
    public String getValue() {
        return this.code;
    }

    @JsonValue
    public String getCode() {
        return this.code;
    }

    @JsonCreator
    public static InspectionType fromCode(String code) {
        for (InspectionType type : InspectionType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }

    /**
     * 根据资产类型获取巡查类型
     * @param assetType 资产类型
     * @param flag true:日常检查  false:经常检查
     * @return 巡查类型
     */
    public static InspectionType fromAssetType(AssetType assetType,boolean flag) {
        return switch (assetType) {
            case BRIDGE -> flag ? BRIDGE_DAILY_INSPECTION : BRIDGE_REGULAR_INSPECTION;
            case TUNNEL -> flag ? TUNNEL_DAILY_INSPECTION : TUNNEL_REGULAR_INSPECTION;
            case CULVERT -> flag ? CULVERT_DAILY_INSPECTION : CULVERT_REGULAR_INSPECTION;
            case DEVICE -> flag ? DEVICE_DAILY_INSPECTION : DEVICE_REGULAR_INSPECTION;
            default -> throw new IllegalArgumentException("Unknown asset type: " + assetType);
        };
    }
}
