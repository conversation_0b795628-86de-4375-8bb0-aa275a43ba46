package com.ruoyi.patrol.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiOperation("病害详情")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DiseaseDetailsVO {
    @ApiModelProperty(value = "方向")
    private String direction;

    @ApiModelProperty(value = "桩号")
    private String stakeNo;

    @ApiModelProperty(value = "缺陷项目")
    private String defectItem;

    @ApiModelProperty(value = "事件名称")
    private String eventName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "数量")
    private String quantity;

    @ApiModelProperty(value = "说明")
    private String description;
}
