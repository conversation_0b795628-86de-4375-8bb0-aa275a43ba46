package com.ruoyi.patrol.domain.request;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.ruoyi.patrol.enums.AuditStatusType;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.service.handler.DateTimeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.AssertTrue;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
/**
 * 批量审核请求类
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchAuditRequest {

    /**
     * 检查ID列表
     */
    private Set<String> checkIds;

    /**
     * 检查类型列表
     */
    @NotEmpty(message = "检查类型列表不能为空")
    private List<InspectionType> types;

    /**
     * 检查开始时间
     * 格式：yyyy-MM-dd，会自动设置为当天 00:00:00
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = DateTimeDeserializer.StartOfDayDeserializer.class)
    private LocalDateTime checkStartTime;

    /**
     * 检查结束时间
     * 格式：yyyy-MM-dd，会自动设置为当天 23:59:59
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = DateTimeDeserializer.EndOfDayDeserializer.class)
    private LocalDateTime checkEndTime;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 审核人ID
     */
    @NotNull(message = "审核人ID不能为空")
    private Long reviewerId;

    /**
     * 精确审核时间（可选）
     * 如果不指定，将根据延迟天数随机生成
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = DateTimeDeserializer.CurrentTimeDeserializer.class)
    private LocalDateTime exactAuditTime;

    /**
     * 最小延迟天数
     * 当未指定精确审核时间时使用
     */
    @Min(value = 0, message = "最小延迟天数不能小于0")
    @Builder.Default
    private Integer minDelayDays = 1;

    /**
     * 最大延迟天数
     * 当未指定精确审核时间时使用
     */
    @Min(value = 0, message = "最大延迟天数不能小于0")
    @Max(value = 30, message = "最大延迟天数不能大于30")
    @Builder.Default
    private Integer maxDelayDays = 7;

    /**
     * 设置审核状态
     */
    @EnumValue
    @Builder.Default
    private AuditStatusType status = AuditStatusType.APPROVED;

    /**
     * 状态列表
     * 默认为空，表示不限制状态
     */
    @Builder.Default
    private List<Integer> statusList = List.of(AuditStatusType.PENDING.getCode());
    
    /**
     * 验证结束时间不早于开始时间
     */
    @AssertTrue(message = "检查结束时间不能小于检查开始时间")
    public boolean isCheckEndTimeValid() {
        return checkStartTime == null || checkEndTime == null || 
               !checkEndTime.isBefore(checkStartTime);
    }
    
    /**
     * 验证最大延迟天数不小于最小延迟天数
     */
    @AssertTrue(message = "最大延迟天数不能小于最小延迟天数")
    public boolean isDelayDaysValid() {
        return minDelayDays == null || maxDelayDays == null || 
               maxDelayDays >= minDelayDays;
    }
    
    /**
     * 验证检查条件：
     * 如果checkIds为null或者空，则checkStartTime，checkEndTime，deptId必须有值
     * 如果checkIds不为空，则checkStartTime，checkEndTime，deptId可以为空
     */
    @AssertTrue(message = "当未指定检查ID时，检查开始时间、结束时间和部门ID不能为空")
    public boolean isSearchParametersValid() {
        if (CollectionUtils.isEmpty(checkIds)) {
            return checkStartTime != null && checkEndTime != null && deptId != null;
        }
        return true;
    }
} 