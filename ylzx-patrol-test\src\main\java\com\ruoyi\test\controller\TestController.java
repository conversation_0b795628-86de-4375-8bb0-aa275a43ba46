package com.ruoyi.test.controller;

import cn.hutool.core.util.IdUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.test.domain.Test;
import com.ruoyi.test.service.TestService;
import com.ruoyi.util.PageUtil;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Api(tags = "test")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("test")
public class TestController extends BaseController {
    final TestService testService;
    int i = 1;

    @GetMapping("/list")
    public Object list(@RequestParam Map<String, Object> params) {

        IPage<Test> iPage = PageUtil.getPage(params);
        QueryWrapper<Test> qw = new QueryWrapper<>();
        IPage<Test> finalIPage = iPage;
        CompletableFuture<IPage<Test>> future = CompletableFuture.supplyAsync(() -> {
                    DynamicDataSourceContextHolder.push("slave");
                    var x = testService.page(finalIPage, qw);
                    DynamicDataSourceContextHolder.clear();
                    return x;
                }

        );

        try {
            iPage = future.get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

//        DynamicDataSourceContextHolder.push("slave");
//        iPage = testService.page(iPage, qw);
//        DynamicDataSourceContextHolder.clear();

        log.info("i:{}", i);
        i++;
        return iPage;
    }

    @GetMapping("save")
    public Object save(@RequestParam Map<String, Object> params) {
        Test test = new Test();
        test.setId(IdUtil.getSnowflakeNextIdStr());
//        test



        return null;
    }

    @GetMapping("reset")
    public Object reset(@RequestParam Map<String, Object> params) {

        this.i = 1;

        return null;
    }

    static Lock lock1 = new ReentrantLock();
    static Lock lock2 = new ReentrantLock();

    @GetMapping("lock")
    public Object lock(@RequestParam Map<String, Object> params) {
        Thread t1 = new Thread() {
            @Override
            public void run() {
                try {
                    lock1.lock();
                    System.out.println(Thread.currentThread().getName() + " get the lock1");
                    Thread.sleep(1000);
                    lock2.lock();
                    System.out.println(Thread.currentThread().getName() + " get the lock2");
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        };
        Thread t2 = new Thread() {
            @Override
            public void run() {
                try {
                    lock2.lock();
                    System.out.println(Thread.currentThread().getName() + " get the lock2");
                    Thread.sleep(1000);
                    lock1.lock();
                    System.out.println(Thread.currentThread().getName() + " get the lock1");
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        };
        //设置线程名字，方便分析堆栈信息
        t1.setName("mythread-A");
        t2.setName("mythread-B");
        t1.start();
        t2.start();

        return null;
    }


}
