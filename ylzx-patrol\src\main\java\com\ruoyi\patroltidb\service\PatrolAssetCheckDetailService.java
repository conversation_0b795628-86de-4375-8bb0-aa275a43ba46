package com.ruoyi.patroltidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import com.ruoyi.patrol.domain.dto.PatrolDiseaseDTO;
import com.ruoyi.patrol.enums.InspectionType;

import java.util.List;

/**
 * 资产寻检查子表Service接口
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
public interface PatrolAssetCheckDetailService extends IService<PatrolAssetCheckDetail> {

    /**
     * 查询列表
     * @param patrolAssetCheckDetail PatrolAssetCheckDetail
     * @return List<PatrolAssetCheckDetail>
     */
    List<PatrolAssetCheckDetail> list(PatrolAssetCheckDetail patrolAssetCheckDetail);


    /**
     * 根据需要的List PatrolPartsInfo创建一个新的list PatrolAssetCheckDetail,用于经常检查
     * @param inspectionType InspectionType
     * @return List<PatrolAssetCheckDetail>
     */
    List<PatrolAssetCheckDetail> generatePatrolAssetCheckDetailList(InspectionType inspectionType,String checkId);

    /**
     * 根据已经存在的List PatrolAssetCheckDetail和需要的List PatrolPartsInfo创建一个新的list PatrolAssetCheckDetail
     * @param patrolAssetCheckDetailList 已经存在的List PatrolAssetCheckDetail
     * @param inspectionType InspectionType
     * @param checkId    String
     * @return List<PatrolAssetCheckDetail>
     */
    List<PatrolAssetCheckDetail> generatePatrolAssetCheckDetailList(
            PatrolAssetCheck patrolAssetCheck, String logId,String userName);

    /**
     * 保存saveAll
     * @param patrolAssetCheckDetailList List<PatrolAssetCheckDetail>
     * @return Boolean
     */
    Boolean saveAll(List<PatrolAssetCheckDetail> patrolAssetCheckDetailList,String tableName);

    /**
     * 批量查询
     * @param checkId
     * @return List<PatrolAssetCheckDetail>
     */
    List<PatrolAssetCheckDetail> selectByCheckId(List<String> checkId,String tableName);

    /**
     * 传入patrolDetailList，返回List<PatrolDiseaseDTO>
     * @param patrolDetailList List<PatrolAssetCheckDetail>
     * @return List<PatrolDiseaseDTO>
     */
    public List<PatrolDiseaseDTO> diseaseNumber(List<PatrolAssetCheckDetail> patrolDetailList);

}
