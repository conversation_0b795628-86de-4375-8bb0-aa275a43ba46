package com.ruoyi.patrol.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description:
 * @author: sfc
 * @date: 2025年02月21日 14:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InspectionGroupCountVO {

    @ApiModelProperty("部门ID")
    private Long deptId;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("路面巡查里程（km）")
    private BigDecimal roadbedMileage;

}
