package com.ruoyi.repote.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;

import java.io.Serial;
import java.util.List;

/**
 * 填报任务对象 repote_mission
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="填报任务")
@TableName("repote_mission")
@Data
public class RepoteMission extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 任务状态（1-未下发,2-已下发） */
    @Excel(name = "任务状态", readConverterExp = "1-未下发,2-已下发")
    @ApiModelProperty(value = "任务状态（1-未下发,2-已下发）")
    private String status;

    /** 任务名称 */
    @Excel(name = "任务名称")
    @ApiModelProperty(value = "任务名称")
    private String name;

    /** 发布人ID */
    @Excel(name = "发布人ID")
    @ApiModelProperty(value = "发布人ID")
    private Long userId;

    /** 任务发布人 */
    @Excel(name = "任务发布人")
    @ApiModelProperty(value = "任务发布人")
    private String userName;

    /** 发布单位ID */
    @Excel(name = "发布单位ID")
    @ApiModelProperty(value = "发布单位ID")
    private Long deptId;

    /** 任务发布部门 */
    @Excel(name = "任务发布部门")
    @ApiModelProperty(value = "任务发布部门")
    private String deptName;

    /** 任务要求文件 */
    @ApiModelProperty(value = "任务要求文件")
    private String url;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "填报人员")
    @TableField(exist = false)
    private List<RepotePerson> personList;

    @ApiModelProperty(value = "任务已完成数量")
    @TableField(exist = false)
    private Long finishNum;

    @ApiModelProperty(value = "任务总数")
    @TableField(exist = false)
    private Long totalNum;


}
