package com.ruoyi.patrol.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.patrol.domain.PatrolInspectionLast;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.service.PatrolInspectionLastService;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
/**
 * (PatrolInspectionLast)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-17 15:01:05
 */

@RestController
@Api("")
@RequestMapping("/patrolInspectionLast")
public class PatrolInspectionLastController extends BaseController {
    @Resource
    private PatrolInspectionLastService patrolInspectionLastService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param patrolInspectionLast 查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    //@RequiresPermissions("com.ruoyi.patrol:patrolInspectionLast:selectAll")
    @RequestMapping(value = "/selectAll", method = RequestMethod.GET)
    public AjaxResult selectAll(Page<PatrolInspectionLast> page, PatrolInspectionLast patrolInspectionLast) {
        return success(this.patrolInspectionLastService.page(page, new QueryWrapper<>(patrolInspectionLast)));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    //@RequiresPermissions("com.ruoyi.patrol:patrolInspectionLast:selectOne")
    @RequestMapping(value = "/selectOne/{id}", method = RequestMethod.GET)
    public AjaxResult selectOne(@PathVariable Serializable id) {
        return success(this.patrolInspectionLastService.getById(id));
    }

    /**
     * 新增数据
     *
     * @param patrolInspectionLast 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    //@RequiresPermissions("com.ruoyi.patrol:patrolInspectionLast:insert")
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public AjaxResult insert(@RequestBody PatrolInspectionLast patrolInspectionLast) {
        return success(this.patrolInspectionLastService.save(patrolInspectionLast));
    }

    /**
     * 修改数据
     *
     * @param patrolInspectionLast 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    //@RequiresPermissions("com.ruoyi.patrol:patrolInspectionLast:update")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public AjaxResult update(@RequestBody PatrolInspectionLast patrolInspectionLast) {
        return success(this.patrolInspectionLastService.updateById(patrolInspectionLast));
    }

    /**
     * 删除数据
     *
     * @param idList 主键集合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    //@RequiresPermissions("com.ruoyi.patrol:patrolInspectionLast:delete")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public AjaxResult delete(@RequestParam("idList") List<Long> idList) {
        return success(this.patrolInspectionLastService.removeByIds(idList));
    }

    /**
     * 查询数据列表
     *
     * @param patrolInspectionLast 查询实体
     * @return 数据列表
     */
    @ApiOperation("查询数据列表")
    //@RequiresPermissions("com.ruoyi.patrol:patrolInspectionLast:getListByEntity")
    @RequestMapping(value = "/getListByEntity", method = RequestMethod.POST)
    public AjaxResult getListByEntity(@RequestBody PatrolInspectionLast patrolInspectionLast) {
        return success(this.patrolInspectionLastService.list(new QueryWrapper<>(patrolInspectionLast)));
    }

    /**
     * 传入bridgeId修改generatedAt为当前时间
     *
     * @param assetIds 资产id
     * @return 修改结果
     */
    @ApiOperation("传入bridgeId修改generatedAt为当前时间")
    //@RequiresPermissions("com.ruoyi.patrol:patrolInspectionLast:updateGeneratedAtByBridgeId")
    @RequestMapping(value = "/updateGeneratedAtByBridgeId", method = RequestMethod.PUT)
    public AjaxResult updateGeneratedAtByBridgeId(@RequestBody List<String> assetIds) {
        return success(this.patrolInspectionLastService.updateGeneratedAtByAssetIds(assetIds,
                AssetType.BRIDGE,LocalDateTime.now()));
    }

}

