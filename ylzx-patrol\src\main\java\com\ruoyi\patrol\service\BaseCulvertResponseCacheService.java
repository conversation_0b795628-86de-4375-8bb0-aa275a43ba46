package com.ruoyi.patrol.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseCulvertResponseCache;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;

import java.util.List;

/**
 * base_culvert_response(BaseCulvertResponseCache)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-21 11:33:54
 */
public interface BaseCulvertResponseCacheService extends IService<BaseCulvertResponseCache> {

        /**
         * 清空表
         */
        void clearTable();

        /**
        * 批量新增数据
        */
        void insertBatch(List<BaseCulvertResponseCache> entities);

        /**
         * 获取request条件下的总数
         * @param request 请求参数
         * @return 总数
         */
        int countAssetBaseData(AssetBaseDataRequest request);

        /**
         * 获取request条件下的数据
         * @param request 请求参数
         * @return 数据
         */
        List<BaseCulvertResponseCache> selectAssetBaseData(AssetBaseDataRequest request, Long pageNum, Long pageSize);
}

