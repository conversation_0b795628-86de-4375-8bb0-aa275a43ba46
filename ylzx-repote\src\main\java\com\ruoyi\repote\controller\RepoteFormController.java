package com.ruoyi.repote.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.google.common.net.HttpHeaders;
import com.ruoyi.common.core.utils.map.Paramap;
import com.ruoyi.repote.domain.RepoteRecord;
import com.ruoyi.repote.service.RepoteMissionService;
import com.ruoyi.repote.service.RepotePersonService;
import com.ruoyi.repote.service.RepoteRecordService;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.utils.ExcelUtilService;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.repote.domain.RepoteForm;
import com.ruoyi.repote.service.RepoteFormService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 填报格规范Controller
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "填报格规范")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/repoteForm")
public class RepoteFormController extends BaseController {
    @Resource
    private RepoteFormService repoteFormService;
    @Resource
    private RepoteRecordService recordService;


    /**
     * 查询填报格规范列表(分页)
     */
    @ApiOperation("查询填报格规范列表")
//    //@RequiresPermissions("repote:repoteForm:list")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> params) {
        params.put("order", "create_time desc");
        startPage();
        List<RepoteForm> list = repoteFormService.findListByParam(params);
        QueryWrapper<RepoteRecord> qw;
        for (RepoteForm form : list) {
            qw = new QueryWrapper<>();
            qw.eq("form_id", form.getId());
            form.setTotalNum(recordService.count(qw));
            qw.eq("status", 3);//已提交数量
            form.setFinishNum(recordService.count(qw));
        }
        return getDataTable(list);
    }

    /**
     * 查询填报格规范列表(不分页)
     */
    @ApiOperation("查询填报格规范列表(不分页)")
//    //@RequiresPermissions("repote:repoteForm:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<RepoteForm> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<RepoteForm> list = repoteFormService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询填报格规范数据
     */
    @ApiOperation("根据id查询填报格规范数据")
//    //@RequiresPermissions("repote:repoteForm:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        RepoteForm repoteForm = repoteFormService.getById(id);
        if (repoteForm == null) {
            return error("未查询到【填报格规范】记录");
        }
        return success(repoteForm);
    }

    /**
     * 新增填报格规范
     */
    @ApiOperation("新增填报格规范")
//    //@RequiresPermissions("repote:repoteForm:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody RepoteForm repoteForm) {
        repoteForm.setFormStatus(0);
        repoteFormService.save(repoteForm);
        return toAjax(true);
    }

    /**
     * 修改填报格规范
     */
    @ApiOperation("修改填报格规范")
//    //@RequiresPermissions("repote:repoteForm:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody RepoteForm repoteForm) {
        QueryWrapper<RepoteRecord> qw = new QueryWrapper<>();
        qw.eq("form_id", repoteForm.getId()).ne("status", 1);
        final long count = recordService.count(qw);
        if (count > 0L) {
            return error("已生成填报记录，不可修改！");
        }
        return toAjax(repoteFormService.updateById(repoteForm));
    }

    /**
     * 合并报表
     */
    @ApiOperation("合并报表")
    @PostMapping("/mergeExcel")
    public AjaxResult mergeExcel(@RequestParam("formId") String formId){
        repoteFormService.mergeExcel(formId);
        return success();

    }

    /**
     * 删除填报格规范
     */
    @ApiOperation("删除填报格规范")
//    //@RequiresPermissions("repote:repoteForm:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        recordService.removeByMap(Paramap.create().put("form_id", id));
        return toAjax(repoteFormService.removeById(id));
    }

    /**
     * 导出填报格规范列表
     */
    @ApiOperation("导出填报格规范列表")
//    //@RequiresPermissions("repote:repoteForm:export")
    @Log(title = "填报格规范", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<RepoteForm> list = repoteFormService.list();
        ExcelUtil<RepoteForm> util = new ExcelUtil<RepoteForm>(RepoteForm.class);
        util.exportExcel(response, list, "填报格规范数据");
    }

    /**
     * 根据url获取Excel文件的最后一个有值的行号
     * @param fileUrl Excel 文件的 URL
     */
    @ApiOperation("根据url获取Excel文件的最后一个有值的行号")
    @GetMapping("/getLastRow")
    public AjaxResult findLastRowWithValues(String fileUrl) {
        Integer lastRowWithValues = repoteFormService.findLastRowWithValues(fileUrl);
        return success(lastRowWithValues);
    }


    @PostMapping("/download")
    public void downloadAndZipFiles(@RequestParam String formId, String fileName, HttpServletResponse response) throws Exception {
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + new String(fileName.getBytes("GBK"), "ISO8859_1") + ".zip");
        try (OutputStream outputStream = response.getOutputStream()) {
            repoteFormService.downloadAndZipFiles(formId, outputStream);
        } catch (Exception e) {
            log.error("下载文件失败", e);
            e.printStackTrace();
            // 处理异常
        }
    }


}
