package com.ruoyi.engineering.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.engineering.domain.RoadEngineeringLane;
import com.ruoyi.engineering.service.RoadEngineeringLaneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 涉路工程Controller
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@Api(tags = "涉路工程车道" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/engineeringLane")
public class RoadEngineeringLaneController extends BaseController {
    final private RoadEngineeringLaneService roadEngineeringLaneService;


    /**
     * 查询涉路工程列表(分页)
     */
    @ApiOperation("查询涉路工程路段列表")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<RoadEngineeringLane> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<RoadEngineeringLane> list = roadEngineeringLaneService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询涉路工程列表(不分页)
     */
    @ApiOperation("查询涉路工程列表(不分页)")
//    @RequiresPermissions("repote:engineering:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<RoadEngineeringLane> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<RoadEngineeringLane> list = roadEngineeringLaneService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询涉路工程数据
     */
    @ApiOperation("根据id查询涉路工程数据")
//    @RequiresPermissions("repote:engineeringLane:query")
    @GetMapping(value = "/get/{engineeringId}")
    public AjaxResult get(@PathVariable String engineeringId) {
        System.out.printf("engineeringId:::" +engineeringId);
        List<String> roadEngineeringLane = roadEngineeringLaneService.getByEngineeringId(engineeringId);

        // 判断是否为空列表
        if (roadEngineeringLane == null || roadEngineeringLane.isEmpty()) {
            return error("未查询到【涉路工程车道】记录");
        }

        // 打印查询到的结果
        System.out.printf("roadEngineeringLane:::" + roadEngineeringLane);

        return success(roadEngineeringLane);
    }


    /**
     * 新增涉路工程
     */
    @ApiOperation("新增涉路工程")
//    @RequiresPermissions("repote:engineeringLane:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody RoadEngineeringLane roadEngineeringLane) {
        roadEngineeringLaneService.save(roadEngineeringLane);
        return AjaxResult.success("操作成功",roadEngineeringLane);
    }

    /**
     * 修改涉路工程
     */
    @ApiOperation("修改涉路工程")
//    @RequiresPermissions("repote:engineeringLane:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody RoadEngineeringLane roadEngineeringLane) {
        return toAjax(roadEngineeringLaneService.updateById(roadEngineeringLane));
    }

    /**
     * 删除涉路工程
     */
    @ApiOperation("删除涉路工程")
//    @RequiresPermissions("repote:engineering:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(roadEngineeringLaneService.removeById(id));
    }

    /**
     * 导出涉路工程列表
     */
    @ApiOperation("导出涉路工程列表")
    @RequiresPermissions("repote:engineering:export")
    @Log(title = "涉路工程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<RoadEngineeringLane> list = roadEngineeringLaneService.list();
        ExcelUtil<RoadEngineeringLane> util = new ExcelUtil<RoadEngineeringLane>(RoadEngineeringLane.class);
        util.exportExcel(response, list, "涉路工程数据");
    }


}
