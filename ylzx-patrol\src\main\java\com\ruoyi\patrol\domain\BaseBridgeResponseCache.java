package com.ruoyi.patrol.domain;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.io.Serializable;

/**
 * base_bridge_response_cache(BaseBridgeResponseCache)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-22 14:33:53
 */
// BaseDataDomain
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@ApiModel(value="base_bridge_response_cache", description="base_bridge_response_cache")
@TableName("base_bridge_response_cache")
public class BaseBridgeResponseCache extends BaseDataCache implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "id")
    @TableId(type = IdType.AUTO)
    private String id;


    @ApiModelProperty(value = "assetId")
    @TableField(value = "asset_id")
    private String assetId;


    @ApiModelProperty(value = "assetName")
    @TableField(value = "asset_name")
    private String assetName;


    @ApiModelProperty(value = "assetCode")
    @TableField(value = "asset_code")
    private String assetCode;


    @ApiModelProperty(value = "routeId")
    @TableField(value = "route_id")
    private String routeId;


    @ApiModelProperty(value = "routeName")
    @TableField(value = "route_name")
    private String routeName;


    @ApiModelProperty(value = "routeCode")
    @TableField(value = "route_code")
    private String routeCode;


    @ApiModelProperty(value = "maintenanceSectionName")
    @TableField(value = "maintenance_section_name")
    private String maintenanceSectionName;


    @ApiModelProperty(value = "managementMaintenanceName")
    @TableField(value = "management_maintenance_name")
    private String managementMaintenanceName;


    @ApiModelProperty(value = "managementMaintenanceBranchName")
    @TableField(value = "management_maintenance_branch_name")
    private String managementMaintenanceBranchName;


    @ApiModelProperty(value = "centerStake")
    @TableField(value = "center_stake")
    private Double centerStake;


    @ApiModelProperty(value = "constructionStake")
    @TableField(value = "construction_stake")
    private BigDecimal constructionStake;


    @ApiModelProperty(value = "unifiedMileageStake")
    @TableField(value = "unified_mileage_stake")
    private BigDecimal unifiedMileageStake;


    @ApiModelProperty(value = "nationalNetworkStake")
    @TableField(value = "national_network_stake")
    private BigDecimal nationalNetworkStake;


    @ApiModelProperty(value = "operationState")
    @TableField(value = "operation_state")
    private String operationState;


    @ApiModelProperty(value = "operationStateName")
    @TableField(value = "operation_state_name")
    private String operationStateName;


    @ApiModelProperty(value = "dailyInspectionStatus")
    @TableField(value = "daily_inspection_status")
    private String dailyInspectionStatus;


    @ApiModelProperty(value = "regularInspectionStatus")
    @TableField(value = "regular_inspection_status")
    private String regularInspectionStatus;


    @ApiModelProperty(value = "longitude")
    @TableField(value = "longitude")
    private Double longitude;


    @ApiModelProperty(value = "latitude")
    @TableField(value = "latitude")
    private Double latitude;


    @ApiModelProperty(value = "ks")
    @TableField(value = "ks")
    private String ks;


    @ApiModelProperty(value = "managementMaintenanceId")
    @TableField(value = "management_maintenance_id")
    private String managementMaintenanceId;


    @ApiModelProperty(value = "managementMaintenanceBranchId")
    @TableField(value = "management_maintenance_branch_id")
    private String managementMaintenanceBranchId;


    @ApiModelProperty(value = "maintenanceSectionId")
    @TableField(value = "maintenance_section_id")
    private String maintenanceSectionId;


    @ApiModelProperty(value = "ifDataRule")
    @TableField(value = "if_data_rule")
    private Boolean ifDataRule;


    @ApiModelProperty(value = "otherSelectWhereSql")
    @TableField(value = "other_select_where_sql")
    private String otherSelectWhereSql;


    @ApiModelProperty(value = "当前记录起始索引")
    @TableField(value = "page_num")
    private Integer pageNum;


    @ApiModelProperty(value = "每页显示记录数")
    @TableField(value = "page_size")
    private Integer pageSize;


    @ApiModelProperty(value = "排序列")
    @TableField(value = "order_by_column")
    private String orderByColumn;


    @ApiModelProperty(value = "排序的方向desc或者asc")
    @TableField(value = "is_asc")
    private String isAsc;


    @ApiModelProperty(value = "分页参数合理化")
    @TableField(value = "reasonable")
    private Boolean reasonable;


    @ApiModelProperty(value = "bridgeCode")
    @TableField(value = "bridge_code")
    private String bridgeCode;


    @ApiModelProperty(value = "bridgeName")
    @TableField(value = "bridge_name")
    private String bridgeName;


    @ApiModelProperty(value = "日常巡查频率（天/次）")
    @TableField(value = "day_frequency")
    private Integer dayFrequency;


    @ApiModelProperty(value = "经常检查频率（月/次）")
    @TableField(value = "month_frequency")
    private Integer monthFrequency;


    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @ApiModelProperty(value = "bridgeTechAssessType")
    @TableField(value = "bridge_tech_assess_type")
    private String bridgeTechAssessType;


    @ApiModelProperty(value = "totalLength")
    @TableField(value = "total_length")
    private BigDecimal totalLength;


    @ApiModelProperty(value = "spanGroups")
    @TableField(value = "span_groups")
    private String spanGroups;


    @ApiModelProperty(value = "spanTotalLength")
    @TableField(value = "span_total_length")
    private BigDecimal spanTotalLength;


    @ApiModelProperty(value = "maxSpan")
    @TableField(value = "max_span")
    private BigDecimal maxSpan;

}

