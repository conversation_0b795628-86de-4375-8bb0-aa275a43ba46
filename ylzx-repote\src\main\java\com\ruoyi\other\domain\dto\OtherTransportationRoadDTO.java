package com.ruoyi.other.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OtherTransportationRoadDTO {
    private static final long serialVersionUID = 1L;

    /** 养护路段Id */
    @Excel(name = "养护路段Id")
    @ApiModelProperty(value = "养护路段Id")
    private String roadId;

    /** 养护路段名称 */
    @Excel(name = "养护路段名称")
    @ApiModelProperty(value = "养护路段名称")
    private String roadName;
}
