package com.ruoyi.patrol.service.handler.impl;

import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheckDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.awt.Color;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * "隧道机电日常巡查记录表"生成处理器。
 * 继承自抽象处理器以重用通用的Excel生成逻辑。
 */
@Slf4j
public class DeviceDailyReportHandler extends AbstractExcelReportHandler<PatrolDeviceCheckDetail> {

    private final List<PatrolDeviceCheckDetail> reportDataList;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");

    // --- 此报表的特定样式定义 ---
    // 继承的样式：titleStyle, headerLabelStyle, headerValueStyle, itemCellStyle, itemFirstColStyle
    // 重新定义的样式（确保有边框）: itemHeaderStyle
    private CellStyle itemContentStyle;       // 检查内容样式 (居中) - 基于itemCellStyle
    private CellStyle noBorderStyle;      // 无边框左对齐自动换行样式
    private CellStyle noBorderCenterStyle;      // 无边框居中自动换行样式

    // 继承的：signUrlMap, signImageMap, failedSignIds, log, SIGNATURE_HEIGHT_POINTS

    public DeviceDailyReportHandler(List<PatrolDeviceCheckDetail> reportDataList, Map<String, String> signUrlMap) {
        // super(); // 隐式调用超类构造函数
        this.reportDataList = reportDataList != null ? reportDataList : new ArrayList<>();
        if (signUrlMap != null) {
            this.signUrlMap.putAll(signUrlMap); // 填充继承的映射
        }
        // 预加载在beforeSheetCreate中调用
    }

    /**
     * 重写createStyles方法，为此报表类型添加特定样式
     * 并初始化基类中的通用样式。
     */
    @Override
    protected void createStyles(Workbook workbook) {
        super.createStyles(workbook); // 首先初始化通用样式
        // --- 此报表的特定样式 ---

        // 检查内容样式 (基于itemCellStyle，已居中带边框)
        itemContentStyle = workbook.createCellStyle();
        itemContentStyle.cloneStyleFrom(itemCellStyle);
        itemContentStyle.setBorderTop(BorderStyle.THIN);
        itemContentStyle.setBorderBottom(BorderStyle.THIN);
        itemContentStyle.setBorderLeft(BorderStyle.THIN);
        itemContentStyle.setBorderRight(BorderStyle.THIN);
        // 设置边框颜色（如果需要）
        itemContentStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        itemContentStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        itemContentStyle.setWrapText(true);

        // 无边框左对齐自动换行样式
        noBorderStyle = workbook.createCellStyle();
        noBorderStyle.setBorderTop(BorderStyle.NONE);
        noBorderStyle.setBorderBottom(BorderStyle.NONE);
        noBorderStyle.setBorderLeft(BorderStyle.NONE);
        noBorderStyle.setBorderRight(BorderStyle.NONE);
        noBorderStyle.setAlignment(HorizontalAlignment.LEFT);
        noBorderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        noBorderStyle.setWrapText(true);

        // 无边框居中自动换行样式
        noBorderCenterStyle = workbook.createCellStyle();
        noBorderCenterStyle.setBorderTop(BorderStyle.NONE);
        noBorderCenterStyle.setBorderBottom(BorderStyle.NONE);
        noBorderCenterStyle.setBorderLeft(BorderStyle.NONE);
        noBorderCenterStyle.setBorderRight(BorderStyle.NONE);
        noBorderCenterStyle.setAlignment(HorizontalAlignment.CENTER);
        noBorderCenterStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        noBorderCenterStyle.setWrapText(true);
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        super.beforeSheetCreate(writeWorkbookHolder, writeSheetHolder); // 通过createStyles重写创建样式
        // 在生成表格内容前预加载签名
        if (!signUrlMap.isEmpty()) {
            preloadSignImages(); // 调用继承的预加载器
        }
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        // 诊断日志
        log.info("开始生成隧道机电日常巡查记录表...");
        if (signUrlMap.isEmpty()) {
            log.warn("签名URL映射为空。签名可能不会出现。");
        } else {
            log.info("签名URL映射包含 {} 条记录。", signUrlMap.size());
            log.info("预加载的签名图像缓存包含 {} 条记录。", signImageMap.size());
            if (!failedSignIds.isEmpty()) {
                log.warn("有 {} 个签名下载失败。ID列表: {}", failedSignIds.size(), failedSignIds);
            }
        }

        int currentRowIndex = 0; // 当前行索引

        // --- 设置列宽 (可以根据需要调整，这里暂时沿用Regular的宽度) ---
        sheet.setColumnWidth(0, 10 * 256);  // A列 (路线/桥梁编码 标签)
        sheet.setColumnWidth(1, 10 * 256);  // B列 (路线/桥梁编码 值)
        sheet.setColumnWidth(2, 15 * 256);  // C列 (路线/桥梁名称 标签)
        sheet.setColumnWidth(3, 10 * 256);  // D列 (路线/桥梁名称 值)
        sheet.setColumnWidth(4, 11 * 256);  // E列 (桩号/养护单位 标签)
        sheet.setColumnWidth(5, 15 * 256);  // F列 (桩号/养护单位 值)
        sheet.setColumnWidth(6, 11 * 256);  // G列 (桩号/养护单位 值)

        // 调整项目列的宽度以适应内容
        if (reportDataList == null || reportDataList.isEmpty()) {
            log.warn("报告数据列表为空。生成空表格。");
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "隧道机电设施日常巡查记录表 (无数据)", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            return; // 没有数据则停止处理
        }

        for (int i = 0; i < reportDataList.size(); i++) {
            PatrolDeviceCheckDetail reportData = reportDataList.get(i);
            if (reportData == null) {
                log.warn("跳过索引 {} 处的null报告数据对象", i);
                continue; // 跳过null数据条目
            }

            log.debug("正在为资产编码 {} 生成日常巡查报告部分", reportData.getAssetCode());

            // 1. 标题行
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "隧道机电设施日常巡查记录表", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            currentRowIndex++;

            // 2. 添加空白行
            Row blankRow = sheet.createRow(currentRowIndex);
            createCell(blankRow, 1, "", noBorderStyle); // 合并创建空单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            blankRow.setHeightInPoints(5);
            currentRowIndex++;

            // 3. 路线信息行1
            Row routeRow1 = sheet.createRow(currentRowIndex);
            createCell(routeRow1, 0, "隧道名称：" + Objects.toString(reportData.getAssetName(), ""), noBorderStyle);
            createCell(routeRow1, 4, "路线名称：" + Objects.toString(reportData.getRouteName(), ""), noBorderStyle);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 2)); // 合并A、B、C
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 6)); // 合并E、F、G
            routeRow1.setHeightInPoints(30);
            currentRowIndex++;

            // 4. 路线信息行2
            Row routeRow2 = sheet.createRow(currentRowIndex);
            createCell(routeRow2, 0, "隧道编码：" + Objects.toString(reportData.getAssetCode(), ""), noBorderStyle);
            createCell(routeRow2, 4, "路线编码：" + Objects.toString(reportData.getRouteCode(), ""), noBorderStyle);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 2)); // 合并A、B、C
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 6)); // 合并E、F、G
            routeRow2.setHeightInPoints(30);
            currentRowIndex++;

            // 5. 路线信息行3
            Row routeRow3 = sheet.createRow(currentRowIndex);
            createCell(routeRow3, 0, "养护机构：" + Objects.toString(reportData.getDeptName(), ""), noBorderStyle);
            createCell(routeRow3, 4, "日期：" + dateFormat.format(reportData.getCheckTime())+ "  天气：" + Objects.toString(reportData.getWeather(), ""), noBorderStyle);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 2)); // 合并A、B、C
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 6)); // 合并E、F、G
            routeRow3.setHeightInPoints(30);
            currentRowIndex++;

            // 6. 设备名称
            Row itemDevNameRow = sheet.createRow(currentRowIndex);
            // 使用带背景的样式
            createCell(itemDevNameRow, 0, "设备名称", itemContentStyle); // 合并A
            Cell itemDevNameCell = createCell(itemDevNameRow, 1, Objects.toString(reportData.getDevName(), ""), itemContentStyle);
            // 使用itemHeaderStyleWithBg自身的边框定义
            applyRowBorder(itemDevNameRow, 0, 6, itemContentStyle);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 6)); // 合并B、G
            setAdaptiveRowHeight(sheet, itemDevNameCell, itemDevNameRow, 60F, itemContentStyle);
            currentRowIndex++;

            // 7. 检查位置
            Row itemLocationRow = sheet.createRow(currentRowIndex);
            // 使用带背景的样式
            createCell(itemLocationRow, 0, "检查位置", itemContentStyle); // 合并A
            Cell itemLocationCell = createCell(itemLocationRow, 1, Objects.toString(reportData.getLocation(), ""), itemContentStyle);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 6)); // 合并B、G
            // 使用itemHeaderStyleWithBg自身的边框定义
            applyRowBorder(itemLocationRow, 0, 6, itemContentStyle);
            setAdaptiveRowHeight(sheet, itemLocationCell, itemLocationRow, 60F, itemContentStyle);
            currentRowIndex++;

            // 8. 检查内容
            Row contentRow = sheet.createRow(currentRowIndex);
            // 使用带背景的样式
            createCell(contentRow, 0, "检查内容", itemContentStyle); // 合并A
            Cell cell = createCell(contentRow, 1, Objects.toString(reportData.getContent(), ""), itemContentStyle);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 6)); // 合并B、G
            // --- 核心：计算自适应行高 ---
            setAdaptiveRowHeight(sheet, cell, contentRow, 60F, itemContentStyle);

            // 使用itemHeaderStyleWithBg自身的边框定义
            applyRowBorder(contentRow, 0, 6, itemContentStyle);

            System.out.println("Top Border: " + cell.getCellStyle().getBorderTop());
            currentRowIndex++;

            // 9. 检查结果
            Row resultRow = sheet.createRow(currentRowIndex);
            // 使用带背景的样式
            createCell(resultRow, 0, "检查结果", itemContentStyle); // 合并A
            createCell(resultRow, 1, "正常", itemContentStyle);      // B
            createCell(resultRow, 2, reportData.getResult()==1?"√":"", itemContentStyle);
            createCell(resultRow, 3, "异常", itemContentStyle); // 占位符C
            createCell(resultRow, 4, reportData.getResult()==2?"√":"", itemContentStyle); // D
            createCell(resultRow, 5, "异常且严重", itemContentStyle); // 合并E、F
            createCell(resultRow, 6, reportData.getResult()==3?"√":"", itemContentStyle);      // 占位符F
            // 使用itemHeaderStyleWithBg自身的边框定义
            applyRowBorder(resultRow, 0, 6, itemContentStyle);
            resultRow.setHeightInPoints(30);
            currentRowIndex++;

            // 10. 巡视车、作业车使用情况
            // 车牌号
            Row carUseRow = sheet.createRow(currentRowIndex);
            // 使用带背景的样式
            createCell(carUseRow, 0, "巡视车、作业车使用情况", itemContentStyle); // 合并A
            createCell(carUseRow, 1, "车牌号", itemContentStyle);
            createCell(carUseRow, 2, Objects.toString(reportData.getCarLicense(), ""), itemContentStyle);
            // 使用itemHeaderStyleWithBg自身的边框定义
            applyRowBorder(carUseRow, 0, 6, itemContentStyle);
            carUseRow.setHeightInPoints(30);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 2, 6));
            currentRowIndex++;

            // 台数
            Row carUseRow2 = sheet.createRow(currentRowIndex);
            // 使用带背景的样式
            createCell(carUseRow2, 0, "", itemContentStyle); // 占位
            createCell(carUseRow2, 1, "台数", itemContentStyle);
            createCell(carUseRow2, 2, Objects.toString(reportData.getNums(), ""), itemContentStyle);
            // 使用itemHeaderStyleWithBg自身的边框定义
            applyRowBorder(carUseRow2, 0, 6, itemContentStyle);
            carUseRow2.setHeightInPoints(30);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 2, 6));
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex-1, currentRowIndex, 0, 0)); // 合并本行和上一行
            currentRowIndex++;

            // 11. 注意事项
            Row describeRow = sheet.createRow(currentRowIndex);
            // 使用带背景的样式
            createCell(describeRow, 0, "注意事项", itemContentStyle); // 合并A
            Cell describeCell = createCell(describeRow, 1, Objects.toString(reportData.getDescribe(), ""), itemContentStyle);
            // 使用itemHeaderStyleWithBg自身的边框定义
            applyRowBorder(describeRow, 0, 6, itemContentStyle);
            setAdaptiveRowHeight(sheet, describeCell, describeRow, 40F, itemContentStyle);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 6)); // 合并B、G
            currentRowIndex++;

            // 2. 添加空白行防止签名越界
            Row blankRow1 = sheet.createRow(currentRowIndex);
            createCell(blankRow1, 1, "", noBorderStyle); // 合并创建空单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            blankRow1.setHeightInPoints(5);
            currentRowIndex++;

            // 12. 页脚行 (签名)
            int footerLabelRowIndex = currentRowIndex;
            Row footerLabelRow = sheet.createRow(footerLabelRowIndex);
            footerLabelRow.setHeightInPoints(35); // 标签行高度
            // 使用带背景的标签样式
            createCell(footerLabelRow, 0, "", noBorderStyle); // 标签A
            createCell(footerLabelRow, 1, "负责人", noBorderCenterStyle); // 标签B
            createCell(footerLabelRow, 2, "", noBorderStyle);       // 占位符C (值/签名区)
            createCell(footerLabelRow, 3, "", noBorderStyle); // 标签D
            createCell(footerLabelRow, 4, "记录人", noBorderCenterStyle);       // 占位符E (值/签名区)
            createCell(footerLabelRow, 5, "", noBorderStyle); // 标签F
            createCell(footerLabelRow, 6, "", noBorderStyle); // 标签G

            // 13. 页脚签名/姓名区域
            // 签名逻辑与BridgeRegularReportHandler完全相同
            int signatureStartRowIndex = currentRowIndex ; // 从标签行开始放名字，图片从下一行开始放
            List<String> kahunaSignList = reportData.getKahunaSignList();
            List<String> oprUserSignList = reportData.getOprUserSignList();

            log.debug("检查人 ({}) 签名数量: {}", reportData.getChecker(), kahunaSignList != null ? kahunaSignList.size() : 0);
            log.debug("记录人 ({}) 签名数量: {}", reportData.getRecorder(), oprUserSignList != null ? oprUserSignList.size() : 0);

            // 将负责人姓名/签名添加到B列 (索引1)
            int lastRowForKahuna = signatureStartRowIndex;
            if (kahunaSignList != null && !kahunaSignList.isEmpty()) {
                // 如果有签名图片，先在标签行写名字，图片从下一行开始
                lastRowForKahuna = addSignatureImages(sheet, signatureStartRowIndex, 2,2,
                        kahunaSignList, Objects.toString(reportData.getChecker(), ""), noBorderStyle, true); // 名字已写，图片下不需要名字
            } else {
                // 没有签名图片，直接在标签行写名字
                createCell(footerLabelRow, 2, Objects.toString(reportData.getChecker(), ""), noBorderStyle);
            }


            // 将记录人姓名/签名添加到D列（索引3）
            int lastRowForOpr = signatureStartRowIndex;
            if (oprUserSignList != null && !oprUserSignList.isEmpty()) {
                lastRowForOpr = addSignatureImages(sheet, signatureStartRowIndex, 5,5,
                        oprUserSignList, Objects.toString(reportData.getRecorder(), ""), noBorderStyle, true); // 名字已写，图片下不需要名字
            } else {
                // 没有签名图片，直接在标签行写名字
                createCell(footerLabelRow, 5, Objects.toString(reportData.getRecorder(), ""), noBorderStyle);
            }
            // 为标签行应用边框 (需要在写入名字后，避免名字单元格无边框)
//            applyRowBorder(footerLabelRow, 0, 6, normalBorderStyle);
            // 确定页脚部分使用的最终行索引
            // 它是标签行、负责人签名结束行、记录人签名结束行中的最大值
            currentRowIndex = Math.max(signatureStartRowIndex + 3, Math.max(lastRowForKahuna, lastRowForOpr));

            // --- 当前报告部分完成 ---

            // 在下一个报告部分之前插入分页符（如果不是最后一个）
            if (i < reportDataList.size() - 1) {
                // 在当前部分最后使用的行之后设置分页符
                sheet.setRowBreak(currentRowIndex - 1);
                log.debug("已在行 {} 之后插入分页符", currentRowIndex - 1);
            }
        }
        log.info("已完成生成所有隧道机电日常巡查记录表部分。");
    }
}