package com.ruoyi.patrol.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patrol.domain.PatrolInspectionGeo;


public interface PatrolInspectionGeoService extends IService<PatrolInspectionGeo> {

    /**
     * 保存巡查日志地理数据
     * @param patrolInspectionGeo 巡查日志地理数据
     * @return 结果
     */
    boolean saveTrace(PatrolInspectionGeo patrolInspectionGeo);

    /**
     * 根据id查询巡查日志地理数据(对地理数据进行处理)
     * @param id 巡查日志id
     * @return 结果
     */
    PatrolInspectionGeo selectGeoById(String id);
}
