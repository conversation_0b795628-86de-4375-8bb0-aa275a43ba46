package com.ruoyi.patroltidb.scheduler;

import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.service.BaseCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class CacheWarmUpScheduler {

    private final BaseCacheService baseCacheService;

    /**
     * 每5分钟预热桥梁基础数据缓存
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void warmUpBridgeCache() {
        log.info("定时预热桥梁基础数据缓存");
        baseCacheService.selectBaseType(AssetType.BRIDGE);
        log.info("桥梁基础数据缓存预热完成");
    }

    /**
     * 每5分钟预热隧道基础数据缓存（延迟20秒执行）
     */
    @Scheduled(cron = "20 */5 * * * ?")
    public void warmUpTunnelCache() {
        log.info("定时预热隧道基础数据缓存");
        baseCacheService.selectBaseType(AssetType.TUNNEL);
        log.info("隧道基础数据缓存预热完成");
    }

    /**
     * 每5分钟预热涵洞基础数据缓存（延迟40秒执行）
     */
    @Scheduled(cron = "40 */5 * * * ?")
    public void warmUpCulvertCache() {
        log.info("定时预热涵洞基础数据缓存");
        baseCacheService.selectBaseType(AssetType.CULVERT);
        log.info("涵洞基础数据缓存预热完成");
    }
} 