package com.ruoyi.patrol.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patrol.domain.PatrolInspectionUser;

import java.util.List;

/**
 * 巡查人员关联Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
public interface PatrolInspectionUserService extends IService<PatrolInspectionUser> {

    /**
     * 批量插入巡查人员关联
     * @param patrolInspectionUserList 巡查人员关联列表
     */
    int batchInsert(List<PatrolInspectionUser> patrolInspectionUserList);


    /**
     * 根据巡查id批量删除
     * @param ids 巡查id
     * @return int 结果
     */
    int batchDeleteByPatrolIds(List<String> ids);

}
