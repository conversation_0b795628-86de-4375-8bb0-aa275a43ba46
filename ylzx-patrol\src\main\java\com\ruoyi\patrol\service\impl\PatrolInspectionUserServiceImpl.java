package com.ruoyi.patrol.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.patrol.domain.PatrolInspectionUser;
import com.ruoyi.patrol.mapper.PatrolInspectionUserMapper;
import com.ruoyi.patrol.service.PatrolInspectionUserService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 巡查人员关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
@Master
public class PatrolInspectionUserServiceImpl extends ServiceImpl<PatrolInspectionUserMapper, PatrolInspectionUser> implements PatrolInspectionUserService {

    /**
     * 批量插入巡查人员关联
     * @param patrolInspectionUserList 巡查人员关联列表
     */
    public int batchInsert(List<PatrolInspectionUser> patrolInspectionUserList) {
        return baseMapper.batchInsert(patrolInspectionUserList);
    }

    /**
     * 根据巡查id批量删除
     * @param ids 巡查id
     * @return int 结果
     */
    @Override
    public int batchDeleteByPatrolIds(List<String> ids){
        return baseMapper.batchDeleteByPatrolIds(ids);

    }

}
