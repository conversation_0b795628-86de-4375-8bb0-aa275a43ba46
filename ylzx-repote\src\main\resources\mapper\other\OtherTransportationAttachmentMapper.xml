<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.other.mapper.OtherTransportationAttachmentMapper">

    <resultMap type="com.ruoyi.other.domain.OtherTransportationAttachment" id="OtherTransportationAttachmentResult">
        <result property="id" column="id"/>
        <result property="transportationId" column="transportation_id"/>
        <result property="name" column="name"/>
        <result property="attachmentType" column="attachment_type"/>
        <result property="url" column="url"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">
        id, transportation_id, name, attachment_type, url, create_by, create_time, update_by, update_time   </sql>

    <sql id="where_column">
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="transportation_id != null and transportation_id != ''">
            AND transportation_id = #{transportation_id}
        </if>
        <if test="name != null and name != ''">
            AND name = #{name}
        </if>
        <if test="attachmentType != null and attachmentType != ''">
            AND attachment_type = #{attachment_type}
        </if>
        <if test="url != null and url != ''">
            AND url = #{url}
        </if>
    </sql>

    <sql id="set_column">
        <if test="transportation_id != null">
            transportation_id = #{transportation_id},
        </if>
        <if test="name != null">
            name = #{name},
        </if>
        <if test="url != null">
            url = #{url}
        </if>
        <if test="create_time != create_time">
            create_time = #{create_time}
        </if>
        <if test="update_by != update_by">
            update_by = #{update_by}
        </if>
        <if test="update_time != update_time">
            update_time = #{update_time}
        </if>
        <if test="create_by != create_by">
            create_by = #{create_by}
        </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="OtherTransportationAttachmentResult">
        SELECT
        <include refid="base_column"/>
        FROM other_transportation_attachment
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="OtherTransportationAttachmentResult">
        SELECT
        <include refid="base_column"/>
        FROM other_transportation_attachment
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="transportation_id != transportation_id">
            transportation_id = #{transportation_id}
        </if>
    </select>
</mapper>