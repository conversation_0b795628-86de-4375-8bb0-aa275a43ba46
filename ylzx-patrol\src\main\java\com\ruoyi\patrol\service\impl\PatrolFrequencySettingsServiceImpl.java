package com.ruoyi.patrol.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.map.Paramap;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.manage.api.domain.bridge.BridgeStaticRequest;
import com.ruoyi.manage.api.domain.culvert.BaseCulvertRequest;
import com.ruoyi.manage.api.domain.tunnel.BaseTunnelRequest;
import com.ruoyi.manage.api.service.BaseBridgeStaticService;
import com.ruoyi.manage.api.service.BaseCulvertService;
import com.ruoyi.manage.api.service.BaseTunnelService;
import com.ruoyi.patrol.domain.PatrolFrequencySettings;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.mapper.PatrolFrequencySettingsMapper;
import com.ruoyi.patrol.service.PatrolFrequencySettingsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 巡查频率配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
@Slave
public class PatrolFrequencySettingsServiceImpl extends ServiceImpl<PatrolFrequencySettingsMapper, PatrolFrequencySettings> implements PatrolFrequencySettingsService {

    @Resource
    private PatrolFrequencySettingsMapper frequencySettingsMapper;
    @Resource
    private BaseBridgeStaticService bridgeStaticService;
    @Resource
    private BaseTunnelService tunnelService;
    @Resource
    private BaseCulvertService culvertService;

    @Override
    public Long saveOrUpdatePlus(PatrolFrequencySettings patrolFrequencySettings) {
        return frequencySettingsMapper.saveOrUpdatePlus(patrolFrequencySettings);
    }

    @Override
    public List<PatrolFrequencySettings> findListByParam(Map<String, Object> params) {
        return frequencySettingsMapper.findListByParam(params);
    }

    @Override
    public MTableDataInfo getListMTableDataInfo(Map<String, Object> params) {
        MTableDataInfo listPage = new MTableDataInfo();//1-桥梁，2-隧道，3-涵洞
        Integer type = MapUtil.getInt(params, "type");
        // 1、查询用户所管养的桥梁信息列表（分页列表，可根据管养单位、路段名称、桥梁名称、桥梁编码进行搜索）
        BaseDataDomain dataDomain = this.getBaseDataDomain(params, type);
        if (type == 1) {
            // 桥梁
            BridgeStaticRequest request = new BridgeStaticRequest();
            BeanUtils.copyProperties(dataDomain, request);
            listPage = bridgeStaticService.getListPage(request);
        } else if (type == 2 || type == 4) {
            // 隧道巡查频率
            BaseTunnelRequest request = new BaseTunnelRequest();
            BeanUtils.copyProperties(dataDomain, request);
            listPage = tunnelService.getListPage(request);
        } else if (type == 3) {
            // 涵洞巡查频率
            BaseCulvertRequest request = new BaseCulvertRequest();
            BeanUtils.copyProperties(dataDomain, request);
            listPage = culvertService.getListPage(request);
        }
        this.buildFrequency(listPage, type);
        return listPage;
    }

    /**
     * 封装请求数据
     */
    private BaseDataDomain getBaseDataDomain(Map<String, Object> params, Integer type) {
        BaseDataDomain dataDomain = new BaseDataDomain();
        dataDomain.setPageNum(MapUtil.getInt(params, "pageNum"));
        dataDomain.setPageSize(MapUtil.getInt(params, "pageSize"));
        dataDomain.setMaintenanceSectionId(MapUtil.getStr(params, "maintenanceSectionId"));//养护路段Id
        dataDomain.setManagementMaintenanceId(MapUtil.getStr(params, "deptId"));//管养单位id
        String deptIds = MapUtil.getStr(params, "deptIds");
        if (StringUtils.isNotEmpty(deptIds)) dataDomain.setManagementMaintenanceIds(Arrays.stream(deptIds.split(",")).collect(Collectors.toSet()) );
        dataDomain.setAssetName(MapUtil.getStr(params, "assetName"));//资产名称
        dataDomain.setAssetCode(MapUtil.getStr(params, "assetCode"));//资产编码
        if (null != MapUtil.getInt(params, "isSettings")) {
            List<PatrolFrequencySettings> listByMap = frequencySettingsMapper.findListByParam(Paramap.create()
                    .put("isSettings", 1).put("type", type));
            List<String> toList = listByMap.stream().map(PatrolFrequencySettings::getAssetId).toList();
            if (MapUtil.getInt(params, "isSettings") == 1) {
                dataDomain.setAssetIds(toList);//桥梁ids
            } else {
                dataDomain.setNotAssetIds(toList);//桥梁排除ids
            }
        }
        return dataDomain;
    }

    /**
     * 查询资产配置巡查频率
     */
    private void buildFrequency(MTableDataInfo listPage, Integer type) {
        List<BaseDataDomain> list = (List<BaseDataDomain>) listPage.getRows();
        if (null != list) {
            // 2、关联桥梁表配置巡查频率
            List<String> assetIdList = list.stream().map(BaseDataDomain::getAssetId).toList();
            List<PatrolFrequencySettings> list1 = frequencySettingsMapper.findListByParam(Paramap.create()
                    .put("assetIdList", assetIdList).put("type", type));
            for (BaseDataDomain bridge : list) {
                for (PatrolFrequencySettings settings : list1) {
                    if (bridge.getAssetId().equals(settings.getAssetId())) {
                        bridge.setDayFrequency(settings.getDayFrequency());
                        bridge.setMonthFrequency(settings.getMonthFrequency());
                        bridge.setUpdateTime(settings.getUpdateTime());
                    }
                }
            }
        }
    }


    /**
     * 根据type查询
     *
     * @param type 类型
     * @return List<PatrolFrequencySettings>
     */
    @Override
    public List<PatrolFrequencySettings> listByType(AssetType type) {
        QueryWrapper<PatrolFrequencySettings> queryWrapper = new QueryWrapper<>();
        if (type != null) queryWrapper.eq("type", type.getValue());
        return this.list(queryWrapper);
    }

    /**
     * 根据type查询
     *
     * @param type 类型
     * @param flag true:dayFrequency(日常检查) false:monthFrequency(经常检查)
     * @return Map<String, Integer> assetId到频率的映射
     */
    @Override
    public Map<String, Integer> assetMapByType(AssetType type, Boolean flag) {
        Map<String, Integer> assetMap;
        List<PatrolFrequencySettings> patrolFrequencySettingsList = this.listByType(type);
        if (flag) {
            assetMap = patrolFrequencySettingsList.stream()
                    .filter(settings -> settings.getDayFrequency() != null && !settings.getDayFrequency().equals(0))
                    .collect(Collectors.toMap(PatrolFrequencySettings::getAssetId, PatrolFrequencySettings::getDayFrequency));
        } else {
            assetMap = patrolFrequencySettingsList.stream()
                    .filter(settings -> settings.getMonthFrequency() != null && !settings.getMonthFrequency().equals(0))
                    .collect(Collectors.toMap(PatrolFrequencySettings::getAssetId, PatrolFrequencySettings::getMonthFrequency));
        }
        return assetMap;
    }

    /**
     * 根据实体类查询
     *
     * @param assetId 资产id
     * @param flag    true:dayFrequency(日常检查) false:monthFrequency(经常检查)
     * @return Integer 周期时间
     */
    @Override
    public Integer getFrequencyByAssetId(String assetId, Integer type, Boolean flag) {
        Integer frequency = 1;
        if(assetId == null){
            return frequency;
        }
        QueryWrapper<PatrolFrequencySettings> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("asset_id", assetId);
        queryWrapper.eq("type", type);
        DynamicDataSourceContextHolder.push("slave");
        PatrolFrequencySettings patrolFrequencySettings = baseMapper.selectOne(queryWrapper);
        DynamicDataSourceContextHolder.clear();
        if (patrolFrequencySettings == null) {
            return 0;
        }
        frequency = flag ? patrolFrequencySettings.getDayFrequency() : patrolFrequencySettings.getMonthFrequency();
        frequency = (frequency == null || frequency == 0) ? 1 : frequency;
        return frequency;
    }

    /**
     * 根据ids查询
     *
     * @param assetIds 资产ids
     * @param type     类型 1-桥梁，2-隧道，3-涵洞
     * @param flag     true:dayFrequency(日常检查) false:monthFrequency(经常检查)
     * @return Map<String, Integer> assetId到频率的映射
     */
    @Override
    public Map<String, Integer> assetMapByAssetIds(List<String> assetIds, Integer type, Boolean flag) {
        // 如果资产ID列表为空，直接返回空映射
        if (assetIds == null || assetIds.isEmpty()) {
            return new HashMap<>();
        }

        QueryWrapper<PatrolFrequencySettings> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("asset_id", assetIds);
        queryWrapper.eq("type", type);

        DynamicDataSourceContextHolder.push("slave");
        List<PatrolFrequencySettings> patrolFrequencySettingsList = baseMapper.selectList(queryWrapper);
        DynamicDataSourceContextHolder.clear();

        Map<String, Integer> assetMap = flag ?
                patrolFrequencySettingsList.stream()
                        .filter(settings -> settings.getDayFrequency() != null && !settings.getDayFrequency().equals(0))
                        .collect(Collectors.toMap(PatrolFrequencySettings::getAssetId, PatrolFrequencySettings::getDayFrequency)) :
                patrolFrequencySettingsList.stream()
                        .filter(settings -> settings.getMonthFrequency() != null && !settings.getMonthFrequency().equals(0))
                        .collect(Collectors.toMap(PatrolFrequencySettings::getAssetId, PatrolFrequencySettings::getMonthFrequency));

        // 创建新的Map包含默认值
        Map<String, Integer> resultMap = new HashMap<>(assetMap);
        assetIds.forEach(assetId -> resultMap.putIfAbsent(assetId, 1));

        return resultMap;
    }


}
