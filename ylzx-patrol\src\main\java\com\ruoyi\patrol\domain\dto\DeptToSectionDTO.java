package com.ruoyi.patrol.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeptToSectionDTO {
    Map<Long,String> deptIdNameMap = new HashMap<>();
    Map<String, Set<String>> deptNameToSectionNameMap = new HashMap<>();
}
