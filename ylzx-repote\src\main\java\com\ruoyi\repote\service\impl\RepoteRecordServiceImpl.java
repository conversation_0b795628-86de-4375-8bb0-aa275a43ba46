package com.ruoyi.repote.service.impl;

import java.util.List;
import java.util.Map;

import cn.hutool.db.PageResult;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.repote.domain.RepoteForm;
import com.ruoyi.repote.service.RepoteFormService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repote.mapper.RepoteRecordMapper;
import com.ruoyi.repote.domain.RepoteRecord;
import com.ruoyi.repote.service.RepoteRecordService;

import javax.annotation.Resource;

/**
 * 填报记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Service
public class RepoteRecordServiceImpl extends ServiceImpl<RepoteRecordMapper, RepoteRecord> implements RepoteRecordService {

    @Resource
    private RepoteRecordMapper repoteRecordMapper;


    @Override
    public List<RepoteRecord> findListByParam(Map<String, Object> params) {
        return repoteRecordMapper.findListByParam(params);
    }



}
