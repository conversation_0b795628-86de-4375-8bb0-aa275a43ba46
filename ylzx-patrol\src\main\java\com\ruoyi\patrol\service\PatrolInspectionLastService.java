package com.ruoyi.patrol.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patrol.domain.PatrolInspectionLast;
import com.ruoyi.patrol.enums.AssetType;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;
import java.util.Map;

/**
 * (PatrolInspectionLast)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-17 15:01:05
 */
public interface PatrolInspectionLastService extends IService<PatrolInspectionLast> {

    /**
     * 根据type查询
     * @param type 类型
     * @return List<PatrolInspectionLast>
     */
    List<PatrolInspectionLast> listByType(AssetType type);

    /**
     * 根据type查询
     * @param type 类型
     * @param flag true:generatedAt(日常检查) false:generatedBy(经常检查)
     * @return List<PatrolInspectionLast> 生成时间列表
     */
    Map<String, LocalDateTime> assetMapByType(AssetType type, Boolean flag);

    /**
     * @param bridgeIds 桥梁id
     * @Description: 传入bridgeId修改generatedAt为当前时间
     */
    Integer updateGeneratedAtByAssetIds(List<String> bridgeIds, AssetType type, LocalDateTime now );


    /**
     * @param bridgeIds 桥梁id
     * @Description: 传入bridgeId修改generatedAt为当前时间
     */
    Integer updateGeneratedAtByAssetIdList(List<String> bridgeIds, AssetType type, LocalDateTime generatedAt, LocalDateTime generatedBy);



    /**
     * 获取date需要自动生成的桥梁ID列表
     * @param date 传入日期，避免极端情况跨日期
     * @return 日期到桥梁ID列表的映射
     */
    Map<LocalDate, Map<LocalDate, List<String>>> generatedAtByAssetIdList(LocalDate date, AssetType assetType,
                                                          Map<String, Integer> assetIdToDayFrequencyMap);

    /**
     * 获取date需要自动生成的桥梁ID列表
     * @param date 传入日期，避免极端情况跨日期
     * @return 日期到桥梁ID列表的映射
     */
    Map<YearMonth,Map<YearMonth, List<String>>> generatedAtByAssetIdList(YearMonth date, AssetType assetType,
                                                          Map<String, Integer> assetIdToDayFrequencyMap);

}

