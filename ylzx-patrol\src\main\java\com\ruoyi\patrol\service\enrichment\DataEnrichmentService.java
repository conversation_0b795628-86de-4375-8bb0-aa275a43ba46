package com.ruoyi.patrol.service.enrichment;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteRouteService;
import com.ruoyi.system.api.domain.dto.BaseRouteDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataEnrichmentService {

    /** Feign 调用超时时间（毫秒） */
    private static final long REQUEST_TIMEOUT_MS = 2000L;

    private final RemoteDeptAuthService   remoteDeptAuthService;
    private final RemoteRouteService      remoteRouteService;

    /**
     * 注入AsyncConfig 中定义好的线程池
     */
    @Resource
    @Qualifier("taskExecutor")
    private Executor taskExecutor;

    public DataEnrichmentService(RemoteDeptAuthService remoteDeptAuthService,
                                 RemoteRouteService remoteRouteService) {
        this.remoteDeptAuthService = remoteDeptAuthService;
        this.remoteRouteService    = remoteRouteService;
    }

    public <T extends BaseDataCache> void enrichData(List<T> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        // 并行异步获取：部门映射，使用 Spring 管理的线程池
        CompletableFuture<Map<String, String>> deptFuture = CompletableFuture
            .supplyAsync(this::fetchDeptMap, taskExecutor)
            .completeOnTimeout(Collections.emptyMap(), REQUEST_TIMEOUT_MS, TimeUnit.MILLISECONDS)
            .exceptionally(ex -> {
                log.warn("fetchDeptMap 失败或超时: {}", ex.getMessage());
                return Collections.emptyMap();
            });

        // 并行异步获取：路线映射
        CompletableFuture<Map<String, BaseRouteDTO>> routeFuture = CompletableFuture
            .supplyAsync(this::fetchRouteMap, taskExecutor)
            .completeOnTimeout(Collections.emptyMap(), REQUEST_TIMEOUT_MS, TimeUnit.MILLISECONDS)
            .exceptionally(ex -> {
                log.warn("fetchRouteMap 失败或超时: {}", ex.getMessage());
                return Collections.emptyMap();
            });

        // 等两个任务一起完成
        CompletableFuture.allOf(deptFuture, routeFuture).join();

        Map<String, String>        deptMap  = deptFuture.join();
        Map<String, BaseRouteDTO>  routeMap = routeFuture.join();

        // 一次遍历完成所有字段补充
        dataList.forEach(item -> {
            // 补充部门名称
            if (StringUtils.isEmpty(item.getManagementMaintenanceName())
                    && StringUtils.isNotEmpty(item.getManagementMaintenanceId())) {
                item.setManagementMaintenanceName(deptMap.get(item.getManagementMaintenanceId()));
            }
            if (StringUtils.isEmpty(item.getManagementMaintenanceBranchName())
                    && StringUtils.isNotEmpty(item.getManagementMaintenanceBranchId())) {
                item.setManagementMaintenanceBranchName(deptMap.get(item.getManagementMaintenanceBranchId()));
            }

            // 补充路线名称/编码
            if (StringUtils.isNotEmpty(item.getRouteId())) {
                BaseRouteDTO dto = routeMap.get(item.getRouteId());
                if (dto != null) {
                    if (StringUtils.isEmpty(item.getRouteName())) {
                        item.setRouteName(dto.getRouteName());
                    }
                    if (StringUtils.isEmpty(item.getRouteCode())) {
                        item.setRouteCode(dto.getRouteCode());
                    }
                }
            }
        });
    }

    /** 实际调用 RemoteDeptAuthService 并转换结果为 Map */
    private Map<String, String> fetchDeptMap() {
        try {
            R<Map<String, String>> resp = remoteDeptAuthService.getDeptIdNameMap();
            if (resp != null && resp.getCode() == 200 && resp.getData() != null) {
                return resp.getData();
            }
            log.warn("remoteDeptAuthService 响应异常: code={}, data={}", 
                     resp != null ? resp.getCode() : "null",
                     resp != null ? resp.getData() : "null");
        } catch (Exception e) {
            log.error("远程获取部门映射出错", e);
        }
        return Collections.emptyMap();
    }

    /** 实际调用 RemoteRouteService 并转换结果为 Map */
    private Map<String, BaseRouteDTO> fetchRouteMap() {
        try {
            R<List<BaseRouteDTO>> resp = remoteRouteService.listAll();
            if (resp != null && resp.getCode() == 200 && !CollectionUtils.isEmpty(resp.getData())) {
                return resp.getData().stream()
                        .filter(dto -> StringUtils.isNoneEmpty(dto.getRouteId()))
                        .collect(Collectors.toMap(BaseRouteDTO::getRouteId,
                                                  Function.identity(),
                                                  (a, b) -> a));
            }
            log.warn("remoteRouteService 响应异常: code={}, size={}", 
                     resp != null ? resp.getCode() : "null",
                     resp != null && resp.getData() != null ? resp.getData().size() : "null");
        } catch (Exception e) {
            log.error("远程获取路线映射出错", e);
        }
        return Collections.emptyMap();
    }
}