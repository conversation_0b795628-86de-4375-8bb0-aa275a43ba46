package com.ruoyi.repote.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.repote.domain.RepoteAnnex;

/**
 * 上传附件Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface RepoteAnnexService extends IService<RepoteAnnex> {

    /**
     * 根据条件查询上传附件数据列表
     * @param params
     */
    List<RepoteAnnex> findListByParam(Map<String, Object> params);

}
