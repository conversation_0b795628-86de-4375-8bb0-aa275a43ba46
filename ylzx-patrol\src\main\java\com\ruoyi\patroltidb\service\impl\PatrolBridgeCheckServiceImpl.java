package com.ruoyi.patroltidb.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.map.Paramap;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.manage.api.domain.bridge.BridgeStaticRequest;
import com.ruoyi.manage.api.domain.culvert.BaseCulvertRequest;
import com.ruoyi.manage.api.domain.tunnel.BaseTunnelRequest;
import com.ruoyi.manage.api.service.BaseBridgeStaticService;
import com.ruoyi.manage.api.service.BaseCulvertService;
import com.ruoyi.manage.api.service.BaseTunnelService;
import com.ruoyi.patrol.domain.PatrolPartsIgnore;
import com.ruoyi.patrol.enums.AuditStatusType;
import com.ruoyi.patrol.enums.PatrolCategory;
import com.ruoyi.patrol.enums.StageType;
import com.ruoyi.patrol.service.PatrolPartsIgnoreService;
import com.ruoyi.patroltidb.domain.*;
import com.ruoyi.patroltidb.mapper.*;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.patroltidb.service.PatrolBridgeCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolBridgeCheckService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 桥梁巡检查记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Service
@Slave
public class PatrolBridgeCheckServiceImpl extends ServiceImpl<PatrolBridgeCheckMapper, PatrolBridgeCheck> implements PatrolBridgeCheckService {

//    @Resource
//    private PatrolBridgeCheckMapper patrolBridgeCheckMapper;
    @Resource
    private PatrolBridgeCheckDetailService patrolBridgeCheckDetailService;
    @Resource
    private PatrolCulvertCheckMapper patrolCulvertCheckMapper;
    @Resource
    private PatrolCulvertCheckDetailMapper patrolCulvertCheckDetailMapper;
    @Resource
    private PatrolTunnelCheckMapper patrolTunnelCheckMapper;
    @Resource
    private PatrolTunnelCheckDetailMapper patrolTunnelCheckDetailMapper;
    @Resource
    private FileInfoMapper fileInfoMapper;
    @Resource
    private PatrolPartsIgnoreService patrolPartsIgnoreService;
    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;
    @Resource
    private BaseBridgeStaticService bridgeStaticService;
    @Resource
    private BaseTunnelService tunnelService;
    @Resource
    private BaseCulvertService culvertService;

    /**
     * 通过id获取实体（带子表）
     * @param id
     * @return
     */
    @Override
    public PatrolBridgeCheck getById(Serializable id) {
        PatrolBridgeCheck patrolBridgeCheck = super.getById(id);
        List<PatrolBridgeCheckDetail> patrolBridgeCheckDetailList = patrolBridgeCheckDetailService.list(new QueryWrapper<>(PatrolBridgeCheckDetail.class) {{
            this.orderByDesc("create_time");
            this.eq("check_id", patrolBridgeCheck.getId());
        }});
        //筛选不检查项
        if ("2".equals(patrolBridgeCheck.getType().getCode())) {
            List<PatrolPartsIgnore> ignoreList = patrolPartsIgnoreService.listByMap(Paramap.create()
                    .put("asset_id", patrolBridgeCheck.getAssetId()));
            List<String> partsIds = ignoreList.stream().map(PatrolPartsIgnore::getPartsId).toList();
            patrolBridgeCheckDetailList.forEach(detail -> detail.setIgnore(partsIds.contains(detail.getPartsTypeId())));
        }
        patrolBridgeCheck.setPatrolBridgeCheckDetailList(patrolBridgeCheckDetailList);
        return patrolBridgeCheck;
    }
    /**
     * 通过id获取实体（不带子表）
     */
    @Override
    public PatrolBridgeCheck getPatrolBridgeCheckById(Serializable id) {
        return super.getById(id);
    }

    /**
     * 保存（带子表）
     * @param entity 实体
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(PatrolBridgeCheck entity) {
        patrolAssetCheckService.setExpiryAndFrequency(entity);
        patrolAssetCheckService.setSign(entity);
//        if(!entity.getType().getFlag()){
//            entity.setStage(StageType.COMPLETED);
//        }
        String id = IdWorker.getIdStr();
        entity.setId(id);
        entity.setStage(StageType.COMPLETED);
        if (entity.getCategory() == null) {
            entity.setCategory(PatrolCategory.REGULAR_PATROL);
        }
        List<PatrolBridgeCheckDetail> patrolBridgeCheckDetailList = entity.getPatrolBridgeCheckDetailList();
        if(CollectionUtil.isNotEmpty(patrolBridgeCheckDetailList)){
            patrolBridgeCheckDetailList.forEach(item -> item.setCheckId(entity.getId()));
            patrolBridgeCheckDetailService.saveBatch(patrolBridgeCheckDetailList);
        }
        patrolAssetCheckService.setDiseaseNum(entity);
        return super.save(entity);
    }

    /**
     * 更新（带子表）
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(PatrolBridgeCheck entity) {
        // 获取当前数据库中的实体，用于比较状态变化
        PatrolBridgeCheck originalEntity = super.getById(entity.getId());
        
        // 只有当状态从非审核状态变为审核状态时，才更新审核人信息
        if (originalEntity != null && 
            !EnumSet.of(AuditStatusType.APPROVED, AuditStatusType.REJECTED).contains(originalEntity.getStatus()) &&
            EnumSet.of(AuditStatusType.APPROVED, AuditStatusType.REJECTED).contains(entity.getStatus())) {
            
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if(entity.getAuditTime() == null){
                entity.setAuditTime(new Date());
            }
            entity.setKahunaId(loginUser.getUserid() + " ");
            entity.setKahunaName(loginUser.getSysUser().getNickName());
            entity.setKahunaSign(loginUser.getSysUser().getSignId());
        }
        // 如果检查时间修改了
        if(originalEntity != null && entity.getCheckTime() != null && !entity.getCheckTime().equals(originalEntity.getCheckTime())){
            // 如果检查时间修改了，则需要更新养护路段的检查时间
            patrolAssetCheckService.setExpiryAndFrequency(entity);
        }

        patrolAssetCheckService.setSign(entity);
        entity.setStage(StageType.COMPLETED);
        List<PatrolBridgeCheckDetail> patrolBridgeCheckDetailList = entity.getPatrolBridgeCheckDetailList();
        if(patrolBridgeCheckDetailList !=null && !patrolBridgeCheckDetailList.isEmpty()){
            patrolBridgeCheckDetailList.forEach(item -> item.setCheckId(entity.getId()));
            patrolBridgeCheckDetailService.saveOrUpdateBatch(patrolBridgeCheckDetailList);
        }
        patrolAssetCheckService.setDiseaseNum(entity);
        boolean update = super.updateById(entity);
        return update;
    }

    /**
     * 删除（带子表）
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<?> list) {
        boolean a = super.removeByIds(list);
        boolean b = patrolBridgeCheckDetailService.remove(new QueryWrapper<>(PatrolBridgeCheckDetail.class) {{
            this.in("check_id", list);
        }});

        return a && b;
    }
    
    @Override
    public List<PatrolBridgeCheck> findListByParam(Map<String, Object> params) {
        DynamicDataSourceContextHolder.push("slave");
        List<PatrolBridgeCheck> list =  baseMapper.findListByParam(params);
        DynamicDataSourceContextHolder.clear();
        return list;
    }

    @Override
    public MTableDataInfo getListMTableDataInfo(Map<String, Object> params) {
        MTableDataInfo listPage = new MTableDataInfo();//1-桥梁，2-隧道，3-涵洞
        Integer type = MapUtil.getInt(params, "type");
        // 1、查询用户所管养的桥梁信息列表（分页列表，可根据管养单位、路段名称、桥梁名称、桥梁编码进行搜索）
        BaseDataDomain dataDomain = this.getBaseDataDomain(params, type);
        if (type == 1) {
            // 桥梁
            BridgeStaticRequest request = new BridgeStaticRequest();
            BeanUtils.copyProperties(dataDomain, request);
            listPage = bridgeStaticService.getListPage(request);
        } else if (type == 2) {
            // 隧道巡查频率
            BaseTunnelRequest request = new BaseTunnelRequest();
            BeanUtils.copyProperties(dataDomain, request);
            listPage = tunnelService.getListPage(request);
        } else if (type == 3) {
            // 涵洞巡查频率
            BaseCulvertRequest request = new BaseCulvertRequest();
            BeanUtils.copyProperties(dataDomain, request);
            listPage = culvertService.getListPage(request);
        }
        this.buildFrequency(listPage);
        return listPage;
    }

    /**
     * 封装请求数据
     */
    private BaseDataDomain getBaseDataDomain(Map<String, Object> params, Integer type) {
        BaseDataDomain dataDomain = new BaseDataDomain();
        dataDomain.setPageNum(MapUtil.getInt(params, "pageNum"));
        dataDomain.setPageSize(MapUtil.getInt(params, "pageSize"));
        dataDomain.setMaintenanceSectionId(MapUtil.getStr(params, "maintenanceSectionId"));//养护路段Id
        dataDomain.setManagementMaintenanceId(MapUtil.getStr(params, "deptId"));//管养单位id
        dataDomain.setAssetName(MapUtil.getStr(params, "assetName"));//资产名称
        dataDomain.setAssetCode(MapUtil.getStr(params, "assetCode"));//资产编码
        if (null != MapUtil.getInt(params, "isInspect")) {
            //切换数据源
            DynamicDataSourceContextHolder.push("slave");
            List<PatrolBridgeCheck> listByMap = baseMapper.findListByParam(Paramap.create()
                    .put("isInspect", 1).put("type", type));
            DynamicDataSourceContextHolder.clear();

            List<String> toList = listByMap.stream().map(PatrolBridgeCheck::getAssetId).toList();
            if (MapUtil.getInt(params, "isSettings") == 1) {
                dataDomain.setIds(toList);//桥梁ids
            } else {
                dataDomain.setNotIds(toList);//桥梁排除ids
            }
        }
        return dataDomain;
    }

    /**
     * 查询资产配置巡查频率
     */
    private void buildFrequency(MTableDataInfo listPage) {
        List<BaseDataDomain> list = (List<BaseDataDomain>) listPage.getRows();
        if (null != list) {
            // 2、关联桥梁表配置巡查频率
//            List<String> assetIdList = list.stream().map(BaseDataDomain::getId).toList();
//            List<PatrolFrequencySettings> list1 = frequencySettingsMapper.findListByParam(Paramap.create()
//                    .put("assetIdList", assetIdList));
//            for (BaseDataDomain bridge : list) {
//                for (PatrolFrequencySettings settings : list1) {
//                    if (bridge.getId().equals(settings.getAssetId())) {
//                        bridge.setDayFrequency(settings.getDayFrequency());
//                        bridge.setMonthFrequency(settings.getMonthFrequency());
//                        bridge.setUpdateTime(settings.getUpdateTime());
//                    }
//                }
//            }
        }
    }

    /**
     * 迁移巡检查图片
     */
    @Override
    public void extracted() {
        // 主表
        this.processMainTable();
        // 子表
        this.processSubTable();
        // 补全
//        this.completionData();
    }

    private static final int PAGE_SIZE = 500;

    /**
     *  主表
     */
    public void processMainTable() {
        int offset = 0;
        while (true) {
            List<TestFileImg> checkList = patrolBridgeCheckDetailService.findFileImgList(Paramap.create().put("workType", 130).put("pageNum", offset).put("pageSize", PAGE_SIZE));
            if (checkList.isEmpty()) {
                break;
            }
            Set<String> checkIds = checkList.stream().map(TestFileImg::getDataId).collect(Collectors.toSet());

            if (checkIds.isEmpty()) break;
            List<PatrolBridgeCheck> bridgeChecks = super.list(new QueryWrapper<PatrolBridgeCheck>().in("id", checkIds).isNull("image"));
            List<String> bridgeIds = bridgeChecks.stream().map(PatrolBridgeCheck::getId).toList();
            checkIds.removeIf(bridgeIds::contains);
            if (checkIds.isEmpty()) {
                offset += PAGE_SIZE;
                continue;
            }
            List<PatrolCulvertCheck> culvertCheckList = patrolCulvertCheckMapper.selectList(new QueryWrapper<PatrolCulvertCheck>().in("id", checkIds).isNull("image"));
            List<String> culvertIds = culvertCheckList.stream().map(PatrolCulvertCheck::getId).toList();
            checkIds.removeIf(culvertIds::contains);
            if (checkIds.isEmpty()) {
                offset += PAGE_SIZE;
                continue;
            }
            List<PatrolTunnelCheck> tunnelCheckList = patrolTunnelCheckMapper.selectList(new QueryWrapper<PatrolTunnelCheck>().in("id", checkIds).isNull("image"));

            List<FileInfoEntity> list = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            for (TestFileImg fileImg : checkList) {
                String ownerId = IdWorker.getIdStr();
                boolean flag = false;
                for (PatrolBridgeCheck check : bridgeChecks) {
                    if (check.getId().equals(fileImg.getDataId())) {
                        check.setImage(ownerId);
                        super.updateById(check);
                        this.getFileInfoEntity(fileImg.getImgPathStr(), ownerId, now, list);
                        flag = true;
                        break;
                    }
                }
                if (flag) continue;
                for (PatrolCulvertCheck check : culvertCheckList) {
                    if (check.getId().equals(fileImg.getDataId())) {
                        check.setImage(ownerId);
                        patrolCulvertCheckMapper.updateById(check);
                        this.getFileInfoEntity(fileImg.getImgPathStr(), ownerId, now, list);
                        flag = true;
                        break;
                    }
                }
                if (flag) continue;
                for (PatrolTunnelCheck check : tunnelCheckList) {
                    if (check.getId().equals(fileImg.getDataId())) {
                        check.setImage(ownerId);
                        patrolTunnelCheckMapper.updateById(check);
                        this.getFileInfoEntity(fileImg.getImgPathStr(), ownerId, now, list);
                        break;
                    }
                }
            }
            if (!list.isEmpty()) fileInfoMapper.insertBatch(list);
            offset += PAGE_SIZE;
        }
    }

    /**
     * 子表
     */
    private void processSubTable() {
        int offset = 0;
        while (true) {
            List<TestFileImg> checkList = patrolBridgeCheckDetailService.findFileImgList(Paramap.create().put("workType", 131).put("pageNum", offset).put("pageSize", PAGE_SIZE));
            if (checkList.isEmpty()) {
                break;
            }
            Set<String> dataIdSet = checkList.stream().map(TestFileImg::getDataId).collect(Collectors.toSet());

            if (dataIdSet.isEmpty()) break;
            List<PatrolBridgeCheckDetail> bridgeCheckDetails = patrolBridgeCheckDetailService.list(new QueryWrapper<PatrolBridgeCheckDetail>().in("id", dataIdSet).isNull("image"));
            List<String> bridgeIds = bridgeCheckDetails.stream().map(PatrolBridgeCheckDetail::getId).toList();
            dataIdSet.removeIf(bridgeIds::contains);
            if (dataIdSet.isEmpty()) {
                offset += PAGE_SIZE;
                continue;
            }
            List<PatrolCulvertCheckDetail> culvertCheckDetails = patrolCulvertCheckDetailMapper.selectList(new QueryWrapper<PatrolCulvertCheckDetail>().in("id", dataIdSet).isNull("image"));
            List<String> culverts = culvertCheckDetails.stream().map(PatrolCulvertCheckDetail::getId).toList();
            dataIdSet.removeIf(culverts::contains);
            if (dataIdSet.isEmpty()) {
                offset += PAGE_SIZE;
                continue;
            }
            List<PatrolTunnelCheckDetail> tunnelCheckDetails = patrolTunnelCheckDetailMapper.selectList(new QueryWrapper<PatrolTunnelCheckDetail>().in("id", dataIdSet).isNull("image"));

            List<FileInfoEntity> list = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            for (TestFileImg fileImg : checkList) {
                String ownerId = IdWorker.getIdStr();
                boolean flag = false;
                for (PatrolBridgeCheckDetail detail : bridgeCheckDetails) {
                    if (detail.getId().equals(fileImg.getDataId())) {
                        detail.setImage(ownerId);
                        patrolBridgeCheckDetailService.updateById(detail);
                        this.getFileInfoEntity(fileImg.getImgPathStr(), ownerId, now, list);
                        flag = true;
                        break;
                    }
                }
                if (flag) continue;
                for (PatrolCulvertCheckDetail detail : culvertCheckDetails) {
                    if (detail.getId().equals(fileImg.getDataId())) {
                        detail.setImage(ownerId);
                        patrolCulvertCheckDetailMapper.updateById(detail);
                        this.getFileInfoEntity(fileImg.getImgPathStr(), ownerId, now, list);
                        flag = true;
                        break;
                    }
                }
                if (flag) continue;
                for (PatrolTunnelCheckDetail detail : tunnelCheckDetails) {
                    if (detail.getId().equals(fileImg.getDataId())) {
                        detail.setImage(ownerId);
                        patrolTunnelCheckDetailMapper.updateById(detail);
                        this.getFileInfoEntity(fileImg.getImgPathStr(), ownerId, now, list);
                        break;
                    }
                }
            }
            if (!list.isEmpty()) fileInfoMapper.insertBatch(list);
            offset += PAGE_SIZE;
        }
    }

    private void completionData() {
        List<PatrolBridgeCheck> checkList = super.list(new QueryWrapper<PatrolBridgeCheck>().isNull("asset_code"));
        Set<String> idSet = checkList.stream().map(PatrolBridgeCheck::getAssetId).collect(Collectors.toSet());

        for (PatrolBridgeCheck check : checkList) {
            if (check.getAssetCode() == null){

            }
        }



    }

    private void getFileInfoEntity(String imgPathStr, String ownerId, LocalDateTime now, List<FileInfoEntity> list) {
        FileInfoEntity fileInfoEntity;
        for (String path : imgPathStr.split(",")) {
            fileInfoEntity = new FileInfoEntity();
            fileInfoEntity.setId(IdWorker.getIdStr());
            fileInfoEntity.setOwnerId(ownerId);
            fileInfoEntity.setFileSize(0L);
            fileInfoEntity.setExt(path.substring(path.lastIndexOf(".") + 1));
            if (path.contains("/")) {
                fileInfoEntity.setFileName(path.substring(path.lastIndexOf("/") + 1));
                fileInfoEntity.setStoragePath(path.substring(path.indexOf("/"), path.lastIndexOf("/") + 1));
            } else {
                fileInfoEntity.setFileName(path);
                fileInfoEntity.setStoragePath(path);
            }
            fileInfoEntity.setPlatform("OldFile");
            fileInfoEntity.setRemark("巡检查图片迁移");
            fileInfoEntity.setCreateBy("admin");
            fileInfoEntity.setCreateTime(now);
            fileInfoEntity.setUpdateBy("admin");
            fileInfoEntity.setUpdateTime(now);
            fileInfoEntity.setIsStatic(true);
            list.add(fileInfoEntity);

        }
    }

    public static void main(String[] args) {
        String path = "K406+928地约科1号大桥检查. (2)_27315412001284_20240702032951.jpg";
        String s1 = path.substring(path.lastIndexOf(".") + 1);
//        String s2 = path.substring(path.lastIndexOf("/") + 1);
//        String s3 = path.substring(path.indexOf("/"), path.lastIndexOf("/") + 1);
        System.out.println(s1);
//        System.out.println(s2);
//        System.out.println(s3);

    }

}
