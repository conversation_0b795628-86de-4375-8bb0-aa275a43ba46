package com.ruoyi.patroltidb.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.patrol.enums.PatrolCategory;
import com.ruoyi.patrol.enums.StageType;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheck;
import com.ruoyi.patroltidb.domain.PatrolCulvertCheck;
import com.ruoyi.patroltidb.domain.PatrolCulvertCheckDetail;
import com.ruoyi.patrol.enums.AuditStatusType;
import com.ruoyi.patroltidb.mapper.PatrolCulvertCheckMapper;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.patroltidb.service.PatrolCulvertCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolCulvertCheckService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.EnumSet;
import java.util.List;

/**
 * 涵洞巡检查记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Service
@Slave
public class PatrolCulvertCheckServiceImpl extends ServiceImpl<PatrolCulvertCheckMapper, PatrolCulvertCheck> implements PatrolCulvertCheckService {

    @Resource
    private PatrolCulvertCheckDetailService patrolCulvertCheckDetailService;

    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;

    /**
     * 通过id获取实体（带子表）
     * @param id
     * @return
     */
    @Override
    public PatrolCulvertCheck getById(Serializable id) {
        PatrolCulvertCheck patrolCulvertCheck = super.getById(id);
        
        List<PatrolCulvertCheckDetail> patrolTunnelCheckDetailList = patrolCulvertCheckDetailService.list(new QueryWrapper<>(PatrolCulvertCheckDetail.class) {{
            this.orderByDesc("create_time");
            this.eq("check_id", patrolCulvertCheck.getId());
        }});
        
        patrolCulvertCheck.setPatrolCulvertCheckDetailList(patrolTunnelCheckDetailList);
        return patrolCulvertCheck;
    }

    /**
     * 通过id获取实体（不带子表）
     */
    @Override
    public PatrolCulvertCheck getPatrolCulverCheckById(Serializable id) {
        return super.getById(id);
    }

    /**
     * 保存（带子表）
     * @param entity 实体
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(PatrolCulvertCheck entity) {
        patrolAssetCheckService.setExpiryAndFrequency(entity);
        patrolAssetCheckService.setSign(entity);
        //        if(!entity.getType().getFlag()){
//            entity.setStage(StageType.COMPLETED);
//        }
        String id = IdWorker.getIdStr();
        entity.setId(id);
        entity.setStage(StageType.COMPLETED);
        if (entity.getCategory() == null) {
            entity.setCategory(PatrolCategory.REGULAR_PATROL);
        }
        List<PatrolCulvertCheckDetail> patrolCulvertCheckDetailList = entity.getPatrolCulvertCheckDetailList();
        if(patrolCulvertCheckDetailList!=null && !patrolCulvertCheckDetailList.isEmpty())
        {
            patrolCulvertCheckDetailList.forEach(item -> item.setCheckId(entity.getId()));
            boolean res = patrolCulvertCheckDetailService.saveBatch(patrolCulvertCheckDetailList);
        }

        patrolAssetCheckService.setDiseaseNum(entity);
        boolean save = super.save(entity);
        return save;
    }

    /**
     * 更新（带子表）
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(PatrolCulvertCheck entity) {
        // 获取当前数据库中的实体，用于比较状态变化
        PatrolCulvertCheck originalEntity = super.getById(entity.getId());
        
        // 只有当状态从非审核状态变为审核状态时，才更新审核人信息
        if (originalEntity != null && 
            !EnumSet.of(AuditStatusType.APPROVED, AuditStatusType.REJECTED).contains(originalEntity.getStatus()) &&
            EnumSet.of(AuditStatusType.APPROVED, AuditStatusType.REJECTED).contains(entity.getStatus())) {
            
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if(entity.getAuditTime() == null){
                entity.setAuditTime(new Date());
            }
            entity.setKahunaId(loginUser.getUserid() + " ");
            entity.setKahunaName(loginUser.getSysUser().getNickName());
            entity.setKahunaSign(loginUser.getSysUser().getSignId());
        }
        
        // 如果检查时间修改了
        if(originalEntity != null && entity.getCheckTime() != null && !entity.getCheckTime().equals(originalEntity.getCheckTime())){
            // 如果检查时间修改了，则需要更新养护路段的检查时间
            patrolAssetCheckService.setExpiryAndFrequency(entity);
        }

        patrolAssetCheckService.setSign(entity);
        entity.setStage(StageType.COMPLETED);
        List<PatrolCulvertCheckDetail> patrolTunnelCheckDetailList = entity.getPatrolCulvertCheckDetailList();
        if (!CollectionUtil.isEmpty(patrolTunnelCheckDetailList)) {
            patrolTunnelCheckDetailList.forEach(item -> item.setCheckId(entity.getId()));
            patrolCulvertCheckDetailService.saveOrUpdateBatch(patrolTunnelCheckDetailList);
        }
        patrolAssetCheckService.setDiseaseNum(entity);
        boolean update = super.updateById(entity);
        return update;
    }

    /**
     * 删除（带子表）
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<?> list) {
        boolean a = super.removeByIds(list);
        boolean b = patrolCulvertCheckDetailService.remove(new QueryWrapper<>(PatrolCulvertCheckDetail.class) {{
            this.in("check_id", list);
        }});

        return a && b;
    }


}
