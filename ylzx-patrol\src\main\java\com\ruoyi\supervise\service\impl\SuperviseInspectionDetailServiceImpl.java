package com.ruoyi.supervise.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.injector.methods.UpdateById;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.supervise.domain.SuperviseInspectionRecord;
import com.ruoyi.supervise.service.SuperviseInspectionRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.ruoyi.supervise.mapper.SuperviseInspectionDetailMapper;
import com.ruoyi.supervise.domain.SuperviseInspectionDetail;
import com.ruoyi.supervise.service.SuperviseInspectionDetailService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 督查详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
@Master
public class SuperviseInspectionDetailServiceImpl extends ServiceImpl<SuperviseInspectionDetailMapper, SuperviseInspectionDetail> implements SuperviseInspectionDetailService {

    @Autowired
    private SuperviseInspectionDetailMapper superviseInspectionDetailMapper;

    @Lazy
    @Autowired
    private SuperviseInspectionRecordService superviseInspectionRecordService;

    @Override
    public List<SuperviseInspectionDetail> findListByParam(Map<String, Object> params) {
        return superviseInspectionDetailMapper.findListByParam(params);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(SuperviseInspectionDetail entity) {
        boolean a = super.updateById(entity);


        SuperviseInspectionRecord superviseInspectionRecord1 = new SuperviseInspectionRecord();
        // 整改责任人id
        superviseInspectionRecord1.setRectifierId(String.valueOf(SecurityUtils.getUserId()));
        // 整改责任人name
        superviseInspectionRecord1.setRectifierName(SecurityUtils.getUsername());
        // 接收时间
//        superviseInspectionRecord1.setReceiveTime(new Date());
        // 状态
//        superviseInspectionRecord1.setStatus("已接收");
        superviseInspectionRecordService.updateById(superviseInspectionRecord1);

        List<SuperviseInspectionDetail> superviseInspectionDetailList = this.list(new QueryWrapper<>(SuperviseInspectionDetail.class) {{
            this.eq("inspection_record_id", entity.getInspectionRecordId());
        }});
        for(SuperviseInspectionDetail detail : superviseInspectionDetailList){
            if ("未整改".equals(detail.getStatus())){
                return  a;
            }
        }
        SuperviseInspectionRecord superviseInspectionRecord = new SuperviseInspectionRecord();
        superviseInspectionRecord.setId(entity.getInspectionRecordId());
        superviseInspectionRecord.setStatus("已完成");
        boolean b = superviseInspectionRecordService.updateById(superviseInspectionRecord);
        return b & b;
    }
}
