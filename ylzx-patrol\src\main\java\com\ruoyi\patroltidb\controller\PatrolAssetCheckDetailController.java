package com.ruoyi.patroltidb.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import com.ruoyi.patroltidb.service.PatrolAssetCheckDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 资产寻检查子表Controller
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
@Api(tags = "资产寻检查子表" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/checkDetail")
public class PatrolAssetCheckDetailController extends BaseController {
    final private PatrolAssetCheckDetailService patrolAssetCheckDetailService;


    /**
     * 查询资产寻检查子表列表(分页)
     */
    @ApiOperation("查询资产寻检查子表列表")
    //@RequiresPermissions("patrol:checkDetail:list")
    @GetMapping("/list")
    public TableDataInfo list(PatrolAssetCheckDetail patrolAssetCheckDetail) {
        startPage();
        List<PatrolAssetCheckDetail> list = patrolAssetCheckDetailService.list(patrolAssetCheckDetail);
        return getDataTable(list);
    }

    /**
     * 查询资产寻检查子表列表(不分页)
     */
    @ApiOperation("查询资产寻检查子表列表(不分页)")
    //@RequiresPermissions("patrol:checkDetail:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll(PatrolAssetCheckDetail patrolAssetCheckDetail) {
        List<PatrolAssetCheckDetail> list = patrolAssetCheckDetailService.list(patrolAssetCheckDetail);
        return success(list);
    }

    /**
     * 根据id查询资产寻检查子表数据
     */
    @ApiOperation("根据id查询资产寻检查子表数据")
    //@RequiresPermissions("patrol:checkDetail:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            PatrolAssetCheckDetail patrolAssetCheckDetail = patrolAssetCheckDetailService.getById(id);
        if (patrolAssetCheckDetail == null) {
            return error("未查询到【资产寻检查子表】记录");
        }
        return success(patrolAssetCheckDetail);
    }

    /**
     * 新增资产寻检查子表
     */
    @ApiOperation("新增资产寻检查子表")
    //@RequiresPermissions("patrol:checkDetail:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolAssetCheckDetail patrolAssetCheckDetail) {
        return toAjax(patrolAssetCheckDetailService.save(patrolAssetCheckDetail));
    }

    /**
     * 修改资产寻检查子表
     */
    @ApiOperation("修改资产寻检查子表")
    //@RequiresPermissions("patrol:checkDetail:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolAssetCheckDetail patrolAssetCheckDetail) {
        return toAjax(patrolAssetCheckDetailService.updateById(patrolAssetCheckDetail));
    }

    /**
     * 删除资产寻检查子表
     */
    @ApiOperation("删除资产寻检查子表")
    //@RequiresPermissions("patrol:checkDetail:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolAssetCheckDetailService.removeById(id));
    }

    /**
     * 导出资产寻检查子表列表
     */
    @ApiOperation("导出资产寻检查子表列表")
    //@RequiresPermissions("patrol:checkDetail:export")
    @Log(title = "资产寻检查子表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolAssetCheckDetail> list = patrolAssetCheckDetailService.list();
        ExcelUtil<PatrolAssetCheckDetail> util = new ExcelUtil<PatrolAssetCheckDetail>(PatrolAssetCheckDetail.class);
        util.exportExcel(response, list, "资产寻检查子表数据");
    }


}
