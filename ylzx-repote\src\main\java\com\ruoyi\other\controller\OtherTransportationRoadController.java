package com.ruoyi.other.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.other.domain.OtherTransportation;
import com.ruoyi.other.domain.OtherTransportationDept;
import com.ruoyi.other.domain.OtherTransportationRoad;
import com.ruoyi.other.service.OtherTransportationRoadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.web.domain.AjaxResult;

import java.util.List;

/**
 * 大件运输Controller
 *
 * <AUTHOR>
 * @date 2024-08-5
 */
@Api(tags = "大件运输路段" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/transportationRoad")
public class OtherTransportationRoadController extends BaseController {
    final private OtherTransportationRoadService otherTransportationRoadService;

    /**
     * 新增大件运输路段
     */
    @ApiOperation("新增大件运输路段")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody OtherTransportationRoad otherTransportationRoad) {
        return toAjax(otherTransportationRoadService.save(otherTransportationRoad));
    }

    /**
     * 编辑大件运输路段
     */
    @ApiOperation("新增大件运输路段")
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody OtherTransportationRoad otherTransportationRoad) {
        boolean success = otherTransportationRoadService.updateRoadByTransportationId(
                otherTransportationRoad.getTransportationId(),
                otherTransportationRoad.getRoadId(),
                otherTransportationRoad.getRoadName()
        );
        return success ? AjaxResult.success("修改成功") : AjaxResult.error("修改失败");
    }

    /**
     * 删除大件运输路段
     */
    @ApiOperation("删除大件运输路段")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(otherTransportationRoadService.removeById(id));
    }

    /**
     * 根据id查询路段ID列表
     */
    @ApiOperation("根据id查询路段ID列表")
    //@RequiresPermissions("other:transportation:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {

        // 查询路段ID列表
        List<String> roadIds = otherTransportationRoadService.getRoadIdsByTransportationId(id);

        if (roadIds == null) {
            return error("未查询到【大件运输部门】");
        }

        return success(roadIds);
    }

    @ApiOperation("删除大件运输路段")
    @PostMapping("/delete")
    public AjaxResult removeAll(@RequestBody String transportationId) {
        // 使用 transportationId 和 roadId 执行删除操作
        boolean result = otherTransportationRoadService.removeByTransportationId(transportationId);
        return toAjax(result);
    }
}
