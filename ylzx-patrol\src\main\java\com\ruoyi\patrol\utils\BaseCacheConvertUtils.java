package com.ruoyi.patrol.utils;


import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseCulvertResponseCache;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.mapstruct.BaseDataDomainMapper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

public class BaseCacheConvertUtils {
    
    private static final int PARALLEL_THRESHOLD = 1000;
    private static final int BATCH_SIZE = 2000;
    private static final ForkJoinPool COMMON_POOL = ForkJoinPool.commonPool();

    public static <T extends BaseDataCache> BaseDataDomain convertToBaseData(T cache) {
        if (cache instanceof BaseBridgeResponseCache) {
            return BaseDataDomainMapper.INSTANCE.bridgeToBaseData((BaseBridgeResponseCache) cache);
        } else if (cache instanceof BaseCulvertResponseCache) {
            return BaseDataDomainMapper.INSTANCE.culvertToBaseData((BaseCulvertResponseCache) cache);
        } else if (cache instanceof BaseTunnelResponseCache) {
            return BaseDataDomainMapper.INSTANCE.tunnelToBaseData((BaseTunnelResponseCache) cache);
        }
        throw new IllegalArgumentException("不支持的缓存类型: " + cache.getClass().getName());
    }

    /**
     * 批量转换缓存对象列表
     */
    public static <T extends BaseDataCache> List<BaseDataDomain> convertList(List<T> list) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        int size = list.size();
        if (size > PARALLEL_THRESHOLD) {
            return processBatchParallel(list);
        } else {
            return processSequential(list);
        }
    }

    private static <T extends BaseDataCache> List<BaseDataDomain> processBatchParallel(List<T> list) {
        int threads = Math.min(Runtime.getRuntime().availableProcessors(), 
                             (list.size() + BATCH_SIZE - 1) / BATCH_SIZE);
        
        try {
            return COMMON_POOL.submit(() ->
                list.parallelStream()
                    .map(BaseCacheConvertUtils::convertToBaseData)
                    .collect(Collectors.toList())
            ).get();
        } catch (Exception e) {
            throw new IllegalStateException("并行处理失败", e);
        }
    }

    private static <T extends BaseDataCache> List<BaseDataDomain> processSequential(List<T> list) {
        List<BaseDataDomain> result = new ArrayList<>(list.size());
        for (T item : list) {
            result.add(convertToBaseData(item));
        }
        return result;
    }
}
