package com.ruoyi.patrol.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 巡查频率配置对象 patrol_frequency_settings
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="巡查频率配置")
@TableName("patrol_frequency_settings")
@Data
public class PatrolFrequencySettings extends BaseTableEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "资产ID")
    private String assetId;

    @ApiModelProperty(value = "资产类型（1-桥梁，2-隧道，3-涵洞）")
    private Integer type;

    @ApiModelProperty(value = "日常巡查频率（天/次）")
    private Integer dayFrequency;

    @ApiModelProperty(value = "经常检查频率（月/次）")
    private Integer monthFrequency;

    @TableField(exist = false)
    private List<String> ids;

}
