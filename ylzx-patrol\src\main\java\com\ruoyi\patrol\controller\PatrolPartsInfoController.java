package com.ruoyi.patrol.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.patrol.domain.PatrolPartsInfo;
import com.ruoyi.patrol.service.PatrolPartsInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 检查类型Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Api(tags = "检查类型")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/parts")
public class PatrolPartsInfoController extends BaseController {
    final private PatrolPartsInfoService patrolPartsInfoService;


    /**
     * 查询检查类型列表(分页)
     */
    @ApiOperation("查询检查类型列表")
    //@RequiresPermissions("patrol:parts:list")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody PatrolPartsInfo patrolPartsInfo) {
        QueryWrapper<PatrolPartsInfo> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        qw.setEntity(patrolPartsInfo);
        startPage();
        List<PatrolPartsInfo> list = patrolPartsInfoService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询检查类型列表(不分页)
     */
    @ApiOperation("查询检查类型列表(不分页)")
    //@RequiresPermissions("patrol:parts:listAll")
    @PostMapping("/listAll")
    public AjaxResult listAll(@RequestBody PatrolPartsInfo patrolPartsInfo) {
        QueryWrapper<PatrolPartsInfo> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        qw.setEntity(patrolPartsInfo);
        List<PatrolPartsInfo> list = patrolPartsInfoService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询检查类型数据
     */
    @ApiOperation("根据id查询检查类型数据")
    //@RequiresPermissions("patrol:parts:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        PatrolPartsInfo patrolPartsInfo = patrolPartsInfoService.getById(id);
        if (patrolPartsInfo == null) {
            return error("未查询到【检查类型】记录");
        }
        return success(patrolPartsInfo);
    }

    /**
     * 新增检查类型
     */
    @ApiOperation("新增检查类型")
    //@RequiresPermissions("patrol:parts:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolPartsInfo patrolPartsInfo) {

        return toAjax(patrolPartsInfoService.save(patrolPartsInfo));
    }

    /**
     * 修改检查类型
     */
    @ApiOperation("修改检查类型")
    //@RequiresPermissions("patrol:parts:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolPartsInfo patrolPartsInfo) {
        return toAjax(patrolPartsInfoService.updateById(patrolPartsInfo));
    }

    /**
     * 删除检查类型
     */
    @ApiOperation("删除检查类型")
    //@RequiresPermissions("patrol:parts:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolPartsInfoService.removeById(id));
    }

    /**
     * 导出检查类型列表
     */
    @ApiOperation("导出检查类型列表")
    //@RequiresPermissions("patrol:parts:export")
    @Log(title = "检查类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolPartsInfo> list = patrolPartsInfoService.list();
        ExcelUtil<PatrolPartsInfo> util = new ExcelUtil<PatrolPartsInfo>(PatrolPartsInfo.class);
        util.exportExcel(response, list, "检查类型数据");
    }


}
