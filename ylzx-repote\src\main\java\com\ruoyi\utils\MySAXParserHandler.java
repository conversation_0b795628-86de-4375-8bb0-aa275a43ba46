package com.ruoyi.utils;

import org.xml.sax.Attributes;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @author: QD
 * @date: 2024年07月30日 15:05
 */
public class MySAXParserHandler extends <PERSON>faultHandler {
    String value = null;
    Integer currentRow = null;
    Integer currentColumn = null;

    List<String> rows = new ArrayList<>();
    Map<String, String> crMap = new HashMap<>();

    int rowIndex = 0;

    public List<String> getRows() {
        return rows;
    }

    public Map<String, String> getCRMap() {
        return crMap;
    }

    @Override
    public void startDocument() throws SAXException {
        super.startDocument();
    }

    @Override
    public void endDocument() throws SAXException {
        super.endDocument();
    }

    public int columnToIndex(String column) {
        int index = 0;
        for (int i = 0; i < column.length(); i++) {
            index = index * 26 + (column.charAt(i) - 'A' + 1);
        }
        return index;
    }

    @Override
    public void startElement(String uri, String localName, String qName, Attributes attributes) throws SAXException {
        super.startElement(uri, localName, qName, attributes);
        if (qName.equals("row")) {
            currentRow = Integer.valueOf(attributes.getValue("r"));
            value = "";
        } else if (qName.equals("c")) {
            String currentColumnStr = attributes.getValue("r");
            if (currentColumnStr != null && currentRow != null) {
                String currentRowStr = currentRow.toString();
//                currentColumn = columnToIndex(currentColumnStr.replaceAll(currentRow + "$", ""));
                currentColumn = columnToIndex(currentColumnStr.substring(0, currentColumnStr.length() - currentRowStr.length()));
            }
        }
    }

    @Override
    public void endElement(String uri, String localName, String qName) throws SAXException {
        super.endElement(uri, localName, qName);
        if (qName.equals("c")) {
            if (value != null && value.contains("DISPIMG")) {
                value = value.substring(value.lastIndexOf("DISPIMG(")).replace("DISPIMG(\"", "");
                value = value.substring(0, value.indexOf("\""));
                rows.add(rowIndex, value);
                // 使用row和column作为key，value为value
                currentRow--; // 将行号减1
                currentColumn--; // 将列号减1
                String key = currentColumn + "_" + currentRow;
                crMap.put(key, value);
            } else {
                rows.add(rowIndex, null);
            }
            rowIndex++;
            value = "";
        }
    }

    @Override
    public void characters(char[] ch, int start, int length) throws SAXException {
        super.characters(ch, start, length);
        value += new String(ch, start, length);
    }
}



