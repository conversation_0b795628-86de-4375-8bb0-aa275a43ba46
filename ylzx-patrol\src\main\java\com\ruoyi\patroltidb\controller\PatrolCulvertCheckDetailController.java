package com.ruoyi.patroltidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheckDetail;
import com.ruoyi.patroltidb.domain.PatrolCulvertCheckDetail;
import com.ruoyi.patroltidb.service.PatrolCulvertCheckDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 涵洞巡检查子表Controller
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Api(tags = "涵洞巡检查子表" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/culvertCheckDetail")
public class PatrolCulvertCheckDetailController extends BaseController {
    final private PatrolCulvertCheckDetailService patrolCulvertCheckDetailService;


    /**
     * 查询涵洞巡检查子表列表(分页)
     */
    @ApiOperation("查询涵洞巡检查子表列表")
    //@RequiresPermissions("patrol:culvertCheckDetail:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<PatrolCulvertCheckDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<PatrolCulvertCheckDetail> list = patrolCulvertCheckDetailService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询涵洞巡检查子表列表(不分页)
     */
    @ApiOperation("查询涵洞巡检查子表列表(不分页)")
    //@RequiresPermissions("patrol:culvertCheckDetail:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolCulvertCheckDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolCulvertCheckDetail> list = patrolCulvertCheckDetailService.list(qw);
        return success(list);
    }

    /**
     * assertCheckId查询涵洞巡检查子数据
     */
    @ApiOperation("assertCheckId查询涵洞巡检查子数据")
    //@RequiresPermissions("patrol:bridgeCheckDetail:query")
    @GetMapping(value = "/getByCheckId")
    public AjaxResult getByCheckId(@RequestParam String assertCheckId) {
        QueryWrapper<PatrolCulvertCheckDetail> qw = new QueryWrapper<>();
        qw.eq("check_id", assertCheckId);
        List<PatrolCulvertCheckDetail> list = patrolCulvertCheckDetailService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询涵洞巡检查子表数据
     */
    @ApiOperation("根据id查询涵洞巡检查子表数据")
    //@RequiresPermissions("patrol:culvertCheckDetail:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            PatrolCulvertCheckDetail patrolCulvertCheckDetail = patrolCulvertCheckDetailService.getById(id);
        if (patrolCulvertCheckDetail == null) {
            return error("未查询到【涵洞巡检查子表】记录");
        }
        return success(patrolCulvertCheckDetail);
    }

    /**
     * 新增涵洞巡检查子表
     */
    @ApiOperation("新增涵洞巡检查子表")
    //@RequiresPermissions("patrol:culvertCheckDetail:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolCulvertCheckDetail patrolCulvertCheckDetail) {
        return toAjax(patrolCulvertCheckDetailService.save(patrolCulvertCheckDetail));
    }

    /**
     * 修改涵洞巡检查子表
     */
    @ApiOperation("修改涵洞巡检查子表")
    //@RequiresPermissions("patrol:culvertCheckDetail:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolCulvertCheckDetail patrolCulvertCheckDetail) {
        return toAjax(patrolCulvertCheckDetailService.updateById(patrolCulvertCheckDetail));
    }

    /**
     * 删除涵洞巡检查子表
     */
    @ApiOperation("删除涵洞巡检查子表")
    //@RequiresPermissions("patrol:culvertCheckDetail:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolCulvertCheckDetailService.removeById(id));
    }

    /**
     * 导出涵洞巡检查子表列表
     */
    @ApiOperation("导出涵洞巡检查子表列表")
    //@RequiresPermissions("patrol:culvertCheckDetail:export")
    @Log(title = "涵洞巡检查子表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolCulvertCheckDetail> list = patrolCulvertCheckDetailService.list();
        ExcelUtil<PatrolCulvertCheckDetail> util = new ExcelUtil<PatrolCulvertCheckDetail>(PatrolCulvertCheckDetail.class);
        util.exportExcel(response, list, "涵洞巡检查子表数据");
    }


}
