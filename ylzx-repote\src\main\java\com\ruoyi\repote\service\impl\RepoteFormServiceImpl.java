package com.ruoyi.repote.service.impl;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.file.MiniOStorage;
import com.ruoyi.repote.domain.RepoteRecord;
import com.ruoyi.repote.service.RepoteRecordService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.vo.SysFileSearchVO;
import com.ruoyi.utils.ExcelUtilService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import com.ruoyi.repote.mapper.RepoteFormMapper;
import com.ruoyi.repote.domain.RepoteForm;
import com.ruoyi.repote.service.RepoteFormService;

import javax.annotation.Resource;

/**
 * 填报格规范Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@Slf4j
@Service
public class RepoteFormServiceImpl extends ServiceImpl<RepoteFormMapper, RepoteForm> implements RepoteFormService {

    @Resource
    private RepoteFormMapper repoteFormMapper;

    @Resource
    private RepoteRecordService recordService;

    @Resource
    private RemoteFileService fileService;

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private ExcelUtilService excelUtilService;

    @Resource
    private ExcelUtilService excelUtil;

    @Override
    public List<RepoteForm> findListByParam(Map<String, Object> params) {
        return repoteFormMapper.findListByParam(params);
    }

    /**
     * 传入formId，合并所有的excel文件
     *
     * @param formId 表单ID
     */
    @Override
    public void mergeExcel(String formId) {
        RepoteForm form = this.getById(formId);
        try {
            this.mergeExcel(form);
        } catch (Exception e) {
            log.error("合并Excel文件失败", e);
            throw new RuntimeException("合并Excel文件失败");
        }
    }

    /**
     * 传入form，合并所有的excel文件
     *
     * @param form 表单
     */
    @Override
    public void mergeExcel(RepoteForm form) throws Exception {
        //模板
        R<SysFile> sysFileR = fileService.getFile(form.getUrl());
        SysFile sysFile = sysFileR.getData();
        File modeFile = excelUtil.writeFileFromUrl(sysFile.getUrl());
        List<String> headers = excelUtil.readExcelHeaderFromFile(form.getHeadRow(),modeFile);
        //已提交的报表记录
        Path tempDir = Files.createTempDirectory("downloaded_files_" + UUID.randomUUID());
        Map<String, Path> deptFilesMap = downloadFilesToTempDir(form.getId(), tempDir, false);
//        Map<String, Integer> perHeighMap = new HashMap<>();
        List<List<Object>> fileList = new ArrayList<>();
        ExcelWriter writer = new ExcelWriter(modeFile);
        for (Map.Entry<String, Path> entry : deptFilesMap.entrySet()) {
            String recordId = entry.getKey();
            Path filePath = entry.getValue();
            File file = filePath.toFile();
            Integer perHeight = fileList.size();
            List<List<Object>> writeDatp = excelUtil.readExcelDataFromFile(headers,form.getHeadRow(), file);
            excelUtil.writeExcelData(form.getHeadRow() + perHeight, writeDatp, writer);
            excelUtilService.readEmbed(file, writer, perHeight);
            excelUtilService.readPic(file, writer, perHeight);
//            perHeighMap.put(recordId, perHeight);
            fileList.addAll(writeDatp);
        }
        writer.close();
        SysFileSearchVO sysFileSearchVO = new SysFileSearchVO();
        String ownerId = IdWorker.getIdStr();
        sysFileSearchVO.setOwnerId(ownerId);
        sysFileSearchVO.setPlatform(MiniOStorage.MINIO2.getStorage());
        sysFileSearchVO.setStoragePath("/ylzx-repote/merge");
        R<SysFile> r = fileService.upload(excelUtil.getMultipartFileByFile(modeFile), sysFileSearchVO);
        log.info("文件信息：{}", r.getData());
        FileUtils.delete(modeFile);
        form.setOwnerId(r.getData().getOwnerId());

        // 清理临时目录
        cleanUpTempDirectory(tempDir);
        updateById(form);
    }


    /**
     * 从指定 URL 的 Excel 文件中获取最后一个有值的行号
     *
     * @param fileUrl Excel 文件的 URL
     * @return 最后一个有值的行号（从0开始计数）
     */
    public Integer findLastRowWithValues(String fileUrl) {
        try (BufferedInputStream inputStream = new BufferedInputStream(new URL(fileUrl).openStream());
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum();

            return IntStream.rangeClosed(0, lastRowNum)
                    .parallel()
                    .map(i -> {
                        Row row = sheet.getRow(i);
                        if (row != null) {
                            for (Cell cell : row) {
                                if (cell.getCellType() != CellType.BLANK) {
                                    return i;
                                }
                            }
                        }
                        return -1;
                    })
                    .filter(i -> i != -1)
                    .max()
                    .orElse(-1);
        } catch (Exception e) {
            log.error("读取Excel文件时出错", e);
            return -1;
        }
    }

//    /**
//     * 从指定 URL 的 Excel 文件中获取最后一个有值的行号
//     *
//     * @param fileUrl Excel 文件的 URL
//     * @return 最后一个有值的行号（从0开始计数）
//     */
//    public Integer findLastRowWithValues(String fileUrl) {
//        try (BufferedInputStream inputStream = new BufferedInputStream(new URL(fileUrl).openStream());
//             Workbook workbook = WorkbookFactory.create(inputStream)) {
//
//            Sheet sheet = workbook.getSheetAt(0);
//            int lastRowWithValue = -1;
//
//            for (Row row : sheet) {
//                boolean rowHasValue = false;
//
//                for (Cell cell : row) {
//                    if (cell.getCellType() != CellType.BLANK) {
//                        rowHasValue = true;
//                        break;
//                    }
//                }
//
//                if (rowHasValue) {
//                    lastRowWithValue = row.getRowNum();
//                } else if (lastRowWithValue != -1) {
//                    break;
//                }
//            }
//
//            return lastRowWithValue;
//        } catch (Exception e) {
//            log.error("读取Excel文件时出错", e);
//            return -1;
//        }
//    }


    /**
     * 从指定 URL 的 Excel 文件中获取最后一个有值的行号
     *
     * @param formId       表单ID
     * @param outputStream 输出流
     */
    @Override
    public void downloadAndZipFiles(String formId, OutputStream outputStream) throws Exception {
        Path tempDir = Files.createTempDirectory("downloaded_files_" + UUID.randomUUID());
        Map<String, Path> deptFilesMap = downloadFilesToTempDir(formId, tempDir, true);

        // 将所有文件压缩到一个ZIP文件并写入输出流
        try (ZipArchiveOutputStream zipOutputStream = new ZipArchiveOutputStream(outputStream)) {
            for (Map.Entry<String, Path> entry : deptFilesMap.entrySet()) {
                Path filePath = entry.getValue();
                ZipArchiveEntry zipEntry = new ZipArchiveEntry(tempDir.relativize(filePath).toString());
                zipOutputStream.putArchiveEntry(zipEntry);
                Files.copy(filePath, zipOutputStream);
                zipOutputStream.closeArchiveEntry();
            }
            zipOutputStream.finish();
        } catch (IOException e) {
            log.error("压缩文件时出错", e);
        } finally {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        }
        // 清理临时目录
        cleanUpTempDirectory(tempDir);
    }

    private Map<String, Path> downloadFilesToTempDir(String formId, Path tempDir, boolean hasAnnex) {
        // 获取所有已提交的报表记录 根据update_time升序排序(修改最早的排在前面)
        QueryWrapper<RepoteRecord> qw = new QueryWrapper<>();
        qw.eq("form_id", formId).eq("status", 3).orderByAsc("update_time");
        List<RepoteRecord> recordList = recordService.list(qw);

        // 获取所有需要下载的表格URL、附件URL和UserId
        CompletableFuture<Map<Long, String>> urlMapFuture = CompletableFuture.supplyAsync(() ->
                recordList.parallelStream()
                        .filter(record -> record.getUrl() != null && !record.getUrl().isEmpty())
                        .collect(Collectors.toMap(RepoteRecord::getUserId,
                                record -> fileService.getFile(record.getUrl()).getData().getUrl()))
        );

        CompletableFuture<Map<Long, String>> annexUrlsMapFuture = hasAnnex ? CompletableFuture.supplyAsync(() ->
                recordList.parallelStream()
                        .filter(record -> record.getAnnexUrls() != null && !record.getAnnexUrls().isEmpty())
                        .collect(Collectors.toMap(RepoteRecord::getUserId,
                                record -> fileService.getFile(record.getAnnexUrls()).getData().getUrl()))
        ) : CompletableFuture.completedFuture(Collections.emptyMap());

        // 获取UserId对应的SysUser
        CompletableFuture<List<SysUser>> sysUsersFuture = CompletableFuture.supplyAsync(() -> {
            List<Long> userIds = recordList.stream()
                    .map(RepoteRecord::getUserId)
                    .collect(Collectors.toList());
            // 检查userIds是否为空
            if (userIds.isEmpty()) {
                throw new RuntimeException("记录内容没有用户信息，请联系管理员。");
            }
            R<List<SysUser>> response = remoteUserService.findListParam(userIds);
            log.info("获取用户信息：{}", response);
            log.info("获取提示信息：{}", response.getMsg());

            // 检查响应码
            if (response.getCode() != 200) {
                throw new RuntimeException("系统出问题，获取用户失败。");
            }

            return response.getData();
        });

        // 获取UserId对应的部门名称和昵称
        CompletableFuture<Map<Long, String>> userDeptMapFuture = sysUsersFuture.thenApply(sysUsers ->
                sysUsers.parallelStream()
                        .collect(Collectors.toMap(SysUser::getUserId, user -> user.getDept().getDeptName()))
        );

        CompletableFuture<Map<Long, String>> nickNamesFuture = sysUsersFuture.thenApply(sysUsers ->
                sysUsers.parallelStream()
                        .collect(Collectors.toMap(SysUser::getUserId, SysUser::getNickName))
        );

        // 等待所有异步任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                urlMapFuture, annexUrlsMapFuture, userDeptMapFuture, nickNamesFuture);

        allFutures.join();

        Map<Long, String> urlMap = urlMapFuture.join();
        Map<Long, String> annexUrlsMap = annexUrlsMapFuture.join();
        Map<Long, String> userDeptMap = userDeptMapFuture.join();
        Map<Long, String> nickNames = nickNamesFuture.join();

        // 下载文件并按部门分类
        Map<String, Path> deptFilesMap = new ConcurrentHashMap<>();
        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

        List<CompletableFuture<Void>> futures = recordList.stream().flatMap(record -> {
            Long userId = record.getUserId();
            String deptName = userDeptMap.get(userId);
            String recordId = record.getId();
            Path deptDir = tempDir.resolve(deptName);
            String nickName = nickNames.get(userId);

            Stream<CompletableFuture<Void>> fileFutures = Stream.of(
                    urlMap.containsKey(userId) ? downloadFile(urlMap.get(userId),
                            deptDir.resolve(nickName + ".xlsx"),
                            deptFilesMap,
                            recordId,
                            executorService) : CompletableFuture.completedFuture(null),
                    hasAnnex && annexUrlsMap.containsKey(userId) ?
                            handleAnnexUrl(
                                    annexUrlsMap.get(userId),
                                    deptDir.resolve(nickName),
                                    deptFilesMap,
                                    recordId,
                                    executorService) : CompletableFuture.completedFuture(null)
            );

            return fileFutures.filter(Objects::nonNull);
        }).collect(Collectors.toList());

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executorService.shutdown();

        // 根据 recordList 的顺序重新排列 deptFilesMap
        Map<String, Path> sortedDeptFilesMap = new LinkedHashMap<>();
        for (RepoteRecord record : recordList) {
            String recordId = record.getId();
            if (deptFilesMap.containsKey(recordId)) {
                sortedDeptFilesMap.put(recordId, deptFilesMap.get(recordId));
            }
        }

        return sortedDeptFilesMap;
    }

    private CompletableFuture<Void> handleAnnexUrl(String annexUrl, Path annexPath, Map<String, Path> deptFilesMap, String recordId, ExecutorService executorService) {
        try {
            Path resolvedPath = annexPath.resolve(Paths.get(new URL(annexUrl).getPath()).getFileName());
            return downloadFile(annexUrl, resolvedPath, deptFilesMap, recordId, executorService);
        } catch (MalformedURLException e) {
            // 处理URL格式异常
            CompletableFuture<Void> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }


    private CompletableFuture<Void> downloadFile(String url, Path filePath, Map<String, Path> deptFilesMap, String id, ExecutorService executorService) {
        return CompletableFuture.runAsync(() -> {
            try {
                Files.createDirectories(filePath.getParent());
                try (BufferedInputStream inputStream = new BufferedInputStream(new URL(url).openStream());
                     BufferedOutputStream outputStreamTemp = new BufferedOutputStream(Files.newOutputStream(filePath))) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStreamTemp.write(buffer, 0, bytesRead);
                    }
                }
                deptFilesMap.computeIfAbsent(id, k -> filePath);
            } catch (IOException e) {
                log.error("下载文件时出错", e);
            }
        }, executorService);
    }


    private void cleanUpTempDirectory(Path tempDir) {
        try {
            FileUtils.deleteDirectory(tempDir.toFile());
        } catch (IOException e) {
            log.error("清理临时目录时出错", e);
        }
    }


}
