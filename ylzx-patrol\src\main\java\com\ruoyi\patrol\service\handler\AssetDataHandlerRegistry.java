package com.ruoyi.patrol.service.handler;

import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.enums.AssetType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资产数据处理器注册表
 * 管理所有资产类型处理器的注册和获取
 */
@Slf4j
@Component
public class AssetDataHandlerRegistry {
    
    private final Map<AssetType, AssetDataHandler<? extends BaseDataDomain, ?, ? extends BaseDataCache>> handlerMap = new HashMap<>();
    
    @Autowired
    private List<AssetDataHandler<? extends BaseDataDomain, ?, ? extends BaseDataCache>> handlers;
    
    /**
     * 初始化时自动注册所有实现了AssetDataHandler接口的处理器
     */
    @PostConstruct
    public void init() {
        if (handlers == null || handlers.isEmpty()) {
            log.warn("没有找到任何资产数据处理器实现");
            return;
        }
        
        for (AssetDataHandler<? extends BaseDataDomain, ?, ? extends BaseDataCache> handler : handlers) {
            AssetType assetType = handler.getAssetType();
            handlerMap.put(assetType, handler);
            log.info("注册资产数据处理器: {} -> {}", assetType, handler.getClass().getSimpleName());
        }
        
        // 验证是否有未实现的资产类型
        for (AssetType type : AssetType.values()) {
            if (!handlerMap.containsKey(type)) {
                log.warn("资产类型 {} 没有对应的处理器实现", type);
            }
        }
    }
    
    /**
     * 获取指定资产类型的处理器
     * 
     * @param assetType 资产类型
     * @return 对应的处理器，如果不存在则返回null
     */
    public AssetDataHandler<? extends BaseDataDomain, ?, ? extends BaseDataCache> getHandler(AssetType assetType) {
        AssetDataHandler<? extends BaseDataDomain, ?, ? extends BaseDataCache> handler = handlerMap.get(assetType);
        if (handler == null) {
            throw new IllegalArgumentException("未找到资产类型 " + assetType + " 的处理器");
        }
        return handler;
    }
    
    /**
     * 检查是否存在指定资产类型的处理器
     * 
     * @param assetType 资产类型
     * @return 是否存在
     */
    public boolean hasHandler(AssetType assetType) {
        return handlerMap.containsKey(assetType);
    }
    
    /**
     * 获取所有已注册的处理器
     * 
     * @return 处理器映射表
     */
    public Map<AssetType, AssetDataHandler<? extends BaseDataDomain, ?, ? extends BaseDataCache>> getAllHandlers() {
        return new HashMap<>(handlerMap);
    }
} 