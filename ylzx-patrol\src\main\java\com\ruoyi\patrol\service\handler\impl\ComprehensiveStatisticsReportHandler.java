package com.ruoyi.patrol.service.handler.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ruoyi.manage.api.domain.RemoteRoadDiseaseResponse;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import com.ruoyi.patrol.domain.PatrolInspectionLogs;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.awt.Color;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * "桥梁日常巡查记录表"生成处理器。
 * 继承自抽象处理器以重用通用的Excel生成逻辑。
 */
@Slf4j
public class ComprehensiveStatisticsReportHandler extends AbstractExcelReportHandler<Object> {

    private final List<PatrolInspectionLogs> logList;
    private final List<RemoteRoadDiseaseResponse> eventList;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");

    // --- 此报表的特定样式定义 ---
    // 继承的样式：titleStyle, headerLabelStyle, headerValueStyle, itemCellStyle, itemFirstColStyle
    // 重新定义的样式（确保有边框）: itemHeaderStyle
    private CellStyle itemHeaderStyleWithBg; // 带背景的项目表头样式
    private CellStyle itemContentStyle;       // 检查内容样式 (居中) - 基于itemCellStyle
    private CellStyle itemStatusStyle;        // 状态描述样式 (居中) - 基于itemCellStyle
    private CellStyle itemMaintenanceStyle;   // 保养措施意见样式 (居中) - 基于itemCellStyle
    private CellStyle footerLabelStyle;       // 页脚标签样式 (无背景)
    private CellStyle noBorderStyle;      // 无边框左对齐自动换行样式
    private CellStyle footerLabelStyleWithBg; // 带背景的页脚标签样式
    private CellStyle footerValueStyle;       // 页脚值样式 (需要确保有边框)
    private CellStyle normalBorderStyle;      // 仅用于细边框的样式

    // 继承的：signUrlMap, signImageMap, failedSignIds, log, SIGNATURE_HEIGHT_POINTS

    public ComprehensiveStatisticsReportHandler(List<PatrolInspectionLogs> logList, List<RemoteRoadDiseaseResponse> eventList, Map<String, String> signUrlMap) {
        // super(); // 隐式调用超类构造函数
        this.logList = logList != null ? logList : new ArrayList<>();
        this.eventList = eventList != null ? eventList : new ArrayList<>();
        if (signUrlMap != null) {
            this.signUrlMap.putAll(signUrlMap); // 填充继承的映射
        }
        // 预加载在beforeSheetCreate中调用
    }

    /**
     * 重写createStyles方法，为此报表类型添加特定样式
     * 并初始化基类中的通用样式。
     */
    @Override
    protected void createStyles(Workbook workbook) {
        super.createStyles(workbook); // 首先初始化通用样式

        // --- 此报表的特定样式 ---

        // --- 通用边框样式 ---
        normalBorderStyle = workbook.createCellStyle();
        setThinBorders(normalBorderStyle); // 使用继承的辅助方法

        // 无边框左对齐自动换行样式
        noBorderStyle = workbook.createCellStyle();
        noBorderStyle.setBorderTop(BorderStyle.NONE);
        noBorderStyle.setBorderBottom(BorderStyle.NONE);
        noBorderStyle.setBorderLeft(BorderStyle.NONE);
        noBorderStyle.setBorderRight(BorderStyle.NONE);
        noBorderStyle.setAlignment(HorizontalAlignment.LEFT);
        noBorderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        noBorderStyle.setWrapText(true);

        // --- 如需要，重新初始化/确保继承样式上的边框 ---
        // 克隆基类样式并应用边框，以确保此报告中的所有内容都有边框
        // 标题标签样式
        CellStyle baseHeaderLabelStyle = super.headerLabelStyle;
        headerLabelStyle = workbook.createCellStyle();
        headerLabelStyle.cloneStyleFrom(baseHeaderLabelStyle);
        setSolidBackground(headerLabelStyle, new Color(217, 217, 217));
        copyBorders(normalBorderStyle, headerLabelStyle); // 应用边框

        // 标题值样式
        CellStyle baseHeaderValueStyle = super.headerValueStyle;
        headerValueStyle = workbook.createCellStyle();
        headerValueStyle.cloneStyleFrom(baseHeaderValueStyle);
        copyBorders(normalBorderStyle, headerValueStyle);

        // 项目表头样式 (基础，不带背景)
        CellStyle baseItemHeaderStyle = super.itemHeaderStyle;
        itemHeaderStyle = workbook.createCellStyle();
        itemHeaderStyle.cloneStyleFrom(baseItemHeaderStyle);
        copyBorders(normalBorderStyle, itemHeaderStyle); // 应用边框

        // 项目单元格样式 (通用居中)
        CellStyle baseItemCellStyle = super.itemCellStyle;
        itemCellStyle = workbook.createCellStyle();
        itemCellStyle.cloneStyleFrom(baseItemCellStyle);
        copyBorders(normalBorderStyle, itemCellStyle);

        // 项目第一列样式 (左对齐)
        CellStyle baseItemFirstColStyle = super.itemFirstColStyle;
        itemFirstColStyle = workbook.createCellStyle();
        itemFirstColStyle.cloneStyleFrom(baseItemFirstColStyle);
        copyBorders(normalBorderStyle, itemFirstColStyle); // 应用边框


        // --- 新的/特定样式 ---
        Color lightGray = new Color(217, 217, 217);

        // 带背景的项目表头样式
        itemHeaderStyleWithBg = workbook.createCellStyle();
        itemHeaderStyleWithBg.cloneStyleFrom(itemHeaderStyle); // 继承字体、对齐、边框
        setSolidBackground(itemHeaderStyleWithBg, lightGray); // 设置背景色
        itemHeaderStyleWithBg.setWrapText(true);

        // 检查内容样式 (基于itemCellStyle，已居中带边框)
        itemContentStyle = workbook.createCellStyle();
        itemContentStyle.cloneStyleFrom(itemCellStyle);
        itemContentStyle.setWrapText(true);

        // 状态描述样式 (基于itemCellStyle，已居中带边框)
        itemStatusStyle = workbook.createCellStyle();
        itemStatusStyle.cloneStyleFrom(itemCellStyle);
        itemStatusStyle.setWrapText(true);

        // 保养措施意见样式 (基于itemCellStyle，已居中带边框)
        itemMaintenanceStyle = workbook.createCellStyle();
        itemMaintenanceStyle.cloneStyleFrom(itemCellStyle);
        itemMaintenanceStyle.setWrapText(true);

        // 页脚标签样式 (继承headerLabelStyle对齐方式，应用边框)
        footerLabelStyle = workbook.createCellStyle(); // 先定义无背景的
        footerLabelStyle.cloneStyleFrom(headerLabelStyle); // 继承字体、右对齐、边框
        footerLabelStyle.setWrapText(true);

        // 带背景的页脚标签样式
        footerLabelStyleWithBg = workbook.createCellStyle();
        footerLabelStyleWithBg.cloneStyleFrom(footerLabelStyle); // 继承字体、右对齐、边框
        setSolidBackground(footerLabelStyleWithBg, lightGray); // 设置背景色
        footerLabelStyleWithBg.setWrapText(true);

        // 页脚值样式 (继承headerValueStyle对齐方式，应用边框)
        footerValueStyle = workbook.createCellStyle();
        footerValueStyle.cloneStyleFrom(headerValueStyle); // 继承字体、左对齐、边框
        footerValueStyle.setWrapText(true);
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        super.beforeSheetCreate(writeWorkbookHolder, writeSheetHolder); // 通过createStyles重写创建样式
        // 在生成表格内容前预加载签名
        if (!signUrlMap.isEmpty()) {
            preloadSignImages(); // 调用继承的预加载器
        }
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        // 诊断日志
        if (signUrlMap.isEmpty()) {
            log.warn("签名URL映射为空。签名可能不会出现。");
        } else {
            log.info("签名URL映射包含 {} 条记录。", signUrlMap.size());
            log.info("预加载的签名图像缓存包含 {} 条记录。", signImageMap.size());
            if (!failedSignIds.isEmpty()) {
                log.warn("有 {} 个签名下载失败。ID列表: {}", failedSignIds.size(), failedSignIds);
            }
        }
        int currentRowIndex = 0; // 当前行索引

        // 1. 标题
        Row titleRow = sheet.createRow(currentRowIndex);
        titleRow.setHeightInPoints(35);
        createCell(titleRow, 0, "日常巡查记录表（"+ logList.get(0).getMaintenanceSectionName() +" 公路）", titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));
        applyRowBorder(titleRow, 0, 5, normalBorderStyle);
        currentRowIndex++;

        // 2. 字段
        Row agencyRow = sheet.createRow(currentRowIndex);

        // --- 设置列宽 (可以根据需要调整，这里暂时沿用Regular的宽度) ---
        sheet.setColumnWidth(0, 16 * 256);  // A列 (路线/桥梁编码 标签)
        sheet.setColumnWidth(1, 18 * 256);  // B列 (路线/桥梁编码 值)
        sheet.setColumnWidth(2, 18 * 256);  // C列 (路线/桥梁名称 标签)
        sheet.setColumnWidth(3, 12 * 256);  // D列 (路线/桥梁名称 值)
        sheet.setColumnWidth(4, 12 * 256);  // E列 (桩号/养护单位 标签)
        sheet.setColumnWidth(5, 8 * 256);  // F列 (桩号/养护单位 值)

        // 存储列宽信息用于行高调整
        int[] columnWidths = new int[]{16, 18, 18, 12, 12, 8};

        // 创建单元格并应用边框，然后合并
        createCell(agencyRow, 0, "巡查项目", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 1, "巡查内容", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 2, "巡查、检查情况", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 3, "处理情况", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 4, "备注", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 5, "频次", headerLabelStyle); // 为边框/合并创建空单元格
        agencyRow.setHeightInPoints(25.6F);
        currentRowIndex++;

        List<RemoteRoadDiseaseResponse> tempEventList;

        // 沥青路面巡查
        Row agentRow = sheet.createRow(currentRowIndex);
        createCell(agentRow, 0, "沥青路面巡查", itemFirstColStyle);
        createCell(agentRow, 1, "积水、积雪、污染物、散落物、路障等情况", itemFirstColStyle);
        createCell(agentRow, 2, "", itemFirstColStyle);
        createCell(agentRow, 3, "", itemFirstColStyle);
        createCell(agentRow, 4, "", itemFirstColStyle);
        createCell(agentRow, 5, "1天1次", itemFirstColStyle);
//        setConditionalRowHeight(agentRow, 0, 5, columnWidths);

        tempEventList = eventList.stream().filter(item -> item.getAssetMainTypeName().equals("路面")).toList();
        if (CollectionUtil.isNotEmpty(tempEventList)) {

            List<RemoteRoadDiseaseResponse> finalTempEventList1 = tempEventList;
            String str = IntStream.range(0, tempEventList.size())
                    .mapToObj(i -> {
                        String diseaseName = finalTempEventList1.get(i).getDiseaseName();
                        String diseaseDesc = finalTempEventList1.get(i).getDiseaseDesc();
                        return String.format("%d.%s，%s", i + 1, diseaseName, diseaseDesc != null ? diseaseDesc : "");
                    })
                    .collect(Collectors.joining("\n"));


            createCell(agentRow, 2, str, itemFirstColStyle);
            createCell(agentRow, 3, "已上报系统", itemFirstColStyle);
        }


        currentRowIndex++;

        // 桥梁日常巡查
        agentRow = sheet.createRow(currentRowIndex);
        createCell(agentRow, 0, "桥梁日常巡查", itemFirstColStyle);
        createCell(agentRow, 1, "桥路连接处、桥面铺装、伸缩缝、桥面系、栏杆或护栏、标志标牌、桥梁线形、异常响动、桥梁安全等情况", itemFirstColStyle);
        createCell(agentRow, 2, "", itemFirstColStyle);
        createCell(agentRow, 3, "", itemFirstColStyle);
        createCell(agentRow, 4, "", itemFirstColStyle);
        createCell(agentRow, 5, "1天1次", itemFirstColStyle);
        tempEventList = eventList.stream().filter(item -> item.getAssetTypeName().equals("桥梁")).toList();
        if (CollectionUtil.isNotEmpty(tempEventList)) {

            List<RemoteRoadDiseaseResponse> finalTempEventList = tempEventList;
            String str = IntStream.range(0, tempEventList.size())
                    .mapToObj(i -> {
                        String diseaseName = finalTempEventList.get(i).getDiseaseName();
                        String diseaseDesc = finalTempEventList.get(i).getDiseaseDesc();
                        return String.format("%d.%s，%s", i + 1, diseaseName, diseaseDesc != null ? diseaseDesc : "");
                    })
                    .collect(Collectors.joining("\n"));


            createCell(agentRow, 2, str, itemFirstColStyle);
            createCell(agentRow, 3, "已上报系统", itemFirstColStyle);
        }
        currentRowIndex++;

        // 隧道日常巡查
        agentRow = sheet.createRow(currentRowIndex);
        createCell(agentRow, 0, "隧道日常巡查", itemFirstColStyle);
        createCell(agentRow, 1, "洞口仰坡、洞门结构、衬砌、路面、洞顶预埋件等情况", itemFirstColStyle);
        createCell(agentRow, 2, "", itemFirstColStyle);
        createCell(agentRow, 3, "", itemFirstColStyle);
        createCell(agentRow, 4, "", itemFirstColStyle);
        createCell(agentRow, 5, "1天1次", itemFirstColStyle);
        tempEventList = eventList.stream().filter(item -> item.getAssetTypeName().equals("隧道")).toList();
        if (CollectionUtil.isNotEmpty(tempEventList)) {

            List<RemoteRoadDiseaseResponse> finalTempEventList = tempEventList;
            String str = IntStream.range(0, tempEventList.size())
                    .mapToObj(i -> {
                        String diseaseName = finalTempEventList.get(i).getDiseaseName();
                        String diseaseDesc = finalTempEventList.get(i).getDiseaseDesc();
                        return String.format("%d.%s，%s", i + 1, diseaseName, diseaseDesc != null ? diseaseDesc : "");
                    })
                    .collect(Collectors.joining("\n"));


            createCell(agentRow, 2, str, itemFirstColStyle);
            createCell(agentRow, 3, "已上报系统", itemFirstColStyle);
        }
        currentRowIndex++;

        // 路基一般巡查
        agentRow = sheet.createRow(currentRowIndex);
        createCell(agentRow, 0, "路基一般巡查", itemFirstColStyle);
        createCell(agentRow, 1, "路肩、路堤、边坡、防护及支挡结构物、排水设施等情况", itemFirstColStyle);
        createCell(agentRow, 2, "", itemFirstColStyle);
        createCell(agentRow, 3, "", itemFirstColStyle);
        createCell(agentRow, 4, "", itemFirstColStyle);
        createCell(agentRow, 5, "1周1次", itemFirstColStyle);

        tempEventList = eventList.stream().filter(item -> item.getAssetMainTypeName().equals("路基")).toList();
        if (CollectionUtil.isNotEmpty(tempEventList)) {

            List<RemoteRoadDiseaseResponse> finalTempEventList = tempEventList;
            String str = IntStream.range(0, tempEventList.size())
                    .mapToObj(i -> {
                        String diseaseName = finalTempEventList.get(i).getDiseaseName();
                        String diseaseDesc = finalTempEventList.get(i).getDiseaseDesc();
                        return String.format("%d.%s，%s", i + 1, diseaseName, diseaseDesc != null ? diseaseDesc : "");
                    })
                    .collect(Collectors.joining("\n"));


            createCell(agentRow, 2, str, itemFirstColStyle);
            createCell(agentRow, 3, "已上报系统", itemFirstColStyle);
        }

        currentRowIndex++;

        // 绿化巡查
        agentRow = sheet.createRow(currentRowIndex);
        createCell(agentRow, 0, "绿化巡查", itemFirstColStyle);
        createCell(agentRow, 1, "绿化养护执行、养护效果、养护作业安全等", itemFirstColStyle);
        createCell(agentRow, 2, "", itemFirstColStyle);
        createCell(agentRow, 3, "", itemFirstColStyle);
        createCell(agentRow, 4, "", itemFirstColStyle);
        createCell(agentRow, 5, "1月一轮", itemFirstColStyle);

        tempEventList = eventList.stream().filter(item -> item.getAssetMainTypeName().equals("绿化")).toList();
        if (CollectionUtil.isNotEmpty(tempEventList)) {

            List<RemoteRoadDiseaseResponse> finalTempEventList = tempEventList;
            String str = IntStream.range(0, tempEventList.size())
                    .mapToObj(i -> {
                        String diseaseName = finalTempEventList.get(i).getDiseaseName();
                        String diseaseDesc = finalTempEventList.get(i).getDiseaseDesc();
                        return String.format("%d.%s，%s", i + 1, diseaseName, diseaseDesc != null ? diseaseDesc : "");
                    })
                    .collect(Collectors.joining("\n"));


            createCell(agentRow, 2, str, itemFirstColStyle);
            createCell(agentRow, 3, "已上报系统", itemFirstColStyle);
        }
        currentRowIndex++;

        // 交通工程经常性检查
        agentRow = sheet.createRow(currentRowIndex);
        createCell(agentRow, 0, "交通工程经常性检查", itemFirstColStyle);
        createCell(agentRow, 1, "交通标志、路面标线、突起路标、轮廓标、护栏、隔离栅、防眩设施及其他设施等", itemFirstColStyle);
        createCell(agentRow, 2, "", itemFirstColStyle);
        createCell(agentRow, 3, "", itemFirstColStyle);
        createCell(agentRow, 4, "", itemFirstColStyle);
        createCell(agentRow, 5, "1月1次", itemFirstColStyle);
        tempEventList = eventList.stream().filter(item -> item.getAssetMainTypeName().equals("交通安全设施")).toList();
        if (CollectionUtil.isNotEmpty(tempEventList)) {

            List<RemoteRoadDiseaseResponse> finalTempEventList = tempEventList;
            String str = IntStream.range(0, tempEventList.size())
                    .mapToObj(i -> {
                        String diseaseName = finalTempEventList.get(i).getDiseaseName();
                        String diseaseDesc = finalTempEventList.get(i).getDiseaseDesc();
                        return String.format("%d.%s，%s", i + 1, diseaseName, diseaseDesc != null ? diseaseDesc : "");
                    })
                    .collect(Collectors.joining("\n"));


            createCell(agentRow, 2, str, itemFirstColStyle);
            createCell(agentRow, 3, "已上报系统", itemFirstColStyle);
        }
        currentRowIndex++;

        // 备注
        agentRow = sheet.createRow(currentRowIndex);
        agentRow.setHeightInPoints(60);
        createCell(agentRow, 0, "备注", itemFirstColStyle);


        String contentStr = IntStream.range(0, logList.size())
                .mapToObj(i -> {
                    String content = logList.get(i).getContent();
                    // 处理空值并添加序号
                    return String.format("%d.%s: %s", i + 1,logList.get(i).getUserNames(), content != null ? content : "");
                })
                .collect(Collectors.joining("\n"));
        createCell(agentRow, 1, contentStr, itemFirstColStyle);
        applyRowBorder(agentRow, 0, 5, itemFirstColStyle);
        sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 5));
//        setConditionalRowHeight(agentRow, 0, 5, columnWidths);
        currentRowIndex++;

        // 8. 记录人
        Row endRow = sheet.createRow(currentRowIndex);
        endRow.setHeightInPoints(35);
        String collect = logList.stream().map(PatrolInspectionLogs::getUserNameList).flatMap(List::stream).distinct()                                // 去重（替代 Set）
                .collect(Collectors.joining(", "));


        {
            // 如果有签名ID，添加签名图片
            List<String> signIdList = logList.stream().map(PatrolInspectionLogs::getSignNameList).flatMap(List::stream).distinct().collect(Collectors.toList());
            // 添加签名图片（水平方向，只显示第一个有效图片）
            createCell(endRow, 0, "记录人(巡查人)：", null);
            currentRowIndex = addSignatureImages(sheet, currentRowIndex, 1, 2, signIdList,
                    collect, noBorderStyle, false);

        }


//        createCell(endRow, 0, "记录人(巡查人)：" + Objects.toString(collect, ""), null);
//        sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 2));
        currentRowIndex++;

        // 巡查时间
        String timeStr = "";
        if (logList.get(0).getStartTime() != null) {
            timeStr = dateFormat.format(logList.get(0).getStartTime());
        }
        createCell(endRow, 3, "巡查时间：" + timeStr, null);
        sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 3, 5));


        currentRowIndex++;
        Row descRow = sheet.createRow(currentRowIndex);
        descRow.setHeightInPoints(35);
        createCell(descRow, 0, "填表说明\n" +
                "1、巡查结果有无异常均需记录；2.路产路权、涉路项目巡查结合现场实际情况进行巡查并记录。", noBorderStyle);
        sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));

    }
}