package com.ruoyi.patrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patrol.domain.PatrolInspectionLogs;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.vo.PatrolInspectionLogsTotalVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 巡查日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
public interface PatrolInspectionLogsMapper extends BaseMapper<PatrolInspectionLogs> {

    List<PatrolInspectionLogs> findListByParam(Map<String, Object> params);

    List<PatrolInspectionLogs> findListByUser(Map<String, Object> params);

    /**
     * 统计用户巡查记录数量
     * @param params 查询参数
     * @return 巡查记录数量
     */
    Integer countListByUser(Map<String, Object> params);

    PatrolInspectionLogs selectPatrolInspectionLogsById(@Param("id") String id);

    List<PatrolInspectionLogs> selectLatestPatrolInspectionLogs(
            @Param("lastTime") LocalDate lastTime,
            @Param("nowTime") LocalDate nowTime,
            @Param("sectionIdList") List<String> sectionIdList
    );

    /**
     * 获取当前用户的巡查记录，时间区间内的 preTime<= checkTime < endTime
     * @param sectionIds 养护路段id列表
     * @param preTime 开始时间
     * @param endTime 结束时间
     */
    List<PatrolInspectionLogs> findListByUserAndTime(
            @Param("sectionIds") List<String> sectionIds,
            @Param("preTime") LocalDateTime preTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("nowTime") Integer nowTime
    );

    /**
     * 获取当前条件的养护里程总和
     * @param request 查询条件
     * @return 养护里程总和
     */
    BigDecimal getTotalPatrolMileage(@Param("request") AssetBaseDataRequest request);

    /**
     * 查询指定养护路段和月份内有数据的日期列表
     *
     * @param maintenanceSectionId 养护路段ID
     * @param yearMonth            年月，格式为yyyy-MM
     * @return 日期列表
     */
    List<Integer> findDaysWithDataBySectionAndMonth(@Param("maintenanceSectionId") String maintenanceSectionId,
                                                   @Param("yearMonth") String yearMonth);


    /**
     * 查询指定养护路段和日期内有数据的小时列表
     * @param sectionId 养护路段ID
     * @param time 日期
     * @return 小时列表
     */
    PatrolInspectionLogs findLatestBySectionIdAndTime(@Param("sectionId") String sectionId, @Param("time") LocalDate time);

    /**
     * 批量插入巡查日志
     * @param list 巡查日志列表
     */
    int batchInsert(@Param("list") List<PatrolInspectionLogs> list);

    /**
     * 批量删除巡查日志
     * @param list 巡查日志ID列表
     */
    int batchDelete(@Param("list") List<String> list);

    /**
     * 批量更新里程数据
     */
    int batchUpdateMileage(@Param("list") List<PatrolInspectionLogs> list);

    List<PatrolInspectionLogsTotalVO> countByPatrolUnit(Map<String, Object> params);

    List<PatrolInspectionLogsTotalVO> countByMaintenanceSubUnit(Map<String, Object> params);
    /**
     * 通过参数查找集合 (userNames 和 signIds 按顺序一一对应, NULL 的 sign_id 显示为 'NULL' 字符串)
     * @param params 查询参数
     * @return 巡查日志列表
     */
    List<PatrolInspectionLogs> findListByUserWithOrderedSigns(Map<String, Object> params);
}
