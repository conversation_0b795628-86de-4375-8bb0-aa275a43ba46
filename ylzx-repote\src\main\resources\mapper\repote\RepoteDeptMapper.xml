<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repote.mapper.RepoteDeptMapper">

    <resultMap type="com.ruoyi.repote.domain.RepoteDept" id="RepoteDeptResult">
            <result property="missionId" column="mission_id"/>
            <result property="deptName" column="dept_name"/>
    </resultMap>

    <sql id="base_column">
 mission_id, dept_name    </sql>

    <sql id="where_column">
        <if test="missionId != null and missionId != ''">
            AND mission_id = #{missionId}
        </if>
        <if test="missionIdLike != null and missionIdLike != ''">
            AND mission_id like CONCAT('%', #{missionIdLike}, '%')
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name = #{deptName}
        </if>
    </sql>

    <sql id="set_column">
            <if test="missionId != null">
                mission_id = #{missionId},
            </if>
            <if test="deptName != null">
                dept_name = #{deptName},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="RepoteDeptResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_dept
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="RepoteDeptResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_dept
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

</mapper>