package com.ruoyi.patrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patrol.domain.PatrolInspectionUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡查人员关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
public interface PatrolInspectionUserMapper extends BaseMapper<PatrolInspectionUser> {

    /**
     * 批量插入巡查人员关联
     * @param list 巡查人员关联列表
     */
    int batchInsert(@Param("list") List<PatrolInspectionUser> list);

    /**
     * 批量删除巡查人员关联
     * @param list 巡查ID列表
     */
    int batchDeleteByPatrolIds(@Param("list") List<String> list);

}
