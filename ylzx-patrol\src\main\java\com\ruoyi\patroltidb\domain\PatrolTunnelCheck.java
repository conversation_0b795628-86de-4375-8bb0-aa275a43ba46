package com.ruoyi.patroltidb.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;
import java.util.List;



/**
 * (PatrolTunnelCheck)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-25 09:48:22
 */

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@ApiModel(value = "隧道巡检查主表", description = "隧道巡检查主表")
@TableName("patrol_tunnel_check")
public class PatrolTunnelCheck  extends PatrolAssetCheck {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 状态list */
    @TableField(exist = false)
    List<String> statusList;

    /** 巡查结果 */
    @TableField(exist = false)
    Boolean isException;

    /** 隧道巡检查详情 */
    @TableField(exist = false)
    @JsonProperty("details")
    List<PatrolTunnelCheckDetail> patrolTunnelCheckDetailList;

}

