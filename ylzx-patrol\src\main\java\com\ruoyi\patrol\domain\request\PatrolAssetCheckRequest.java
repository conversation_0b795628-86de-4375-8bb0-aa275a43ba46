package com.ruoyi.patrol.domain.request;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.ruoyi.patrol.enums.InspectionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @author: QD
 * @date: 2024年06月19日 11:17
 */
@Data
@AllArgsConstructor
public class PatrolAssetCheckRequest {
    @ApiModelProperty(value = "资产List<String> assetIdList")
    List<String> assetIdList;

    @ApiModelProperty(value = "检查类型(1:桥梁日常巡查;2:桥梁经常检查;3:涵洞定期检查;4:涵洞经常检查;5:隧道日常巡查;6:隧道经常检查;)")
    @EnumValue
    InspectionType type;

    String typeCode;

    @ApiModelProperty(value = "最近检查时间")
    LocalDate lastCheckTime;

    @ApiModelProperty(value = "当前检查时间")
    LocalDate nowCheckTime;

    public PatrolAssetCheckRequest(List<String> assetIdList, InspectionType type, LocalDate lastCheckTime, LocalDate nowCheckTime) {
        this.assetIdList = assetIdList;
        this.type = type;
        this.typeCode = type.getCode();
        this.lastCheckTime = lastCheckTime;
        this.nowCheckTime = nowCheckTime;
    }

    String getTypeCode() {
        return this.type.getCode();
    }
}
