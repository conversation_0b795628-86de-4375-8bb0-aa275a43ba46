package com.ruoyi.repote.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.repote.domain.RepoteConfigure;
import com.ruoyi.repote.service.RepoteConfigureService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 填报配置Controller
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Api(tags = "填报配置" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/configure")
public class RepoteConfigureController extends BaseController {
    final private RepoteConfigureService repoteConfigureService;


    /**
     * 查询填报配置列表(分页)
     */
    @ApiOperation("查询填报配置列表")
    //@RequiresPermissions("repote:configure:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<RepoteConfigure> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<RepoteConfigure> list = repoteConfigureService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询填报配置列表(不分页)
     */
    @ApiOperation("查询填报配置列表(不分页)")
    //@RequiresPermissions("repote:configure:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<RepoteConfigure> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<RepoteConfigure> list = repoteConfigureService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询填报配置数据
     */
    @ApiOperation("根据id查询填报配置数据")
    //@RequiresPermissions("repote:configure:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            RepoteConfigure repoteConfigure = repoteConfigureService.getById(id);
        if (repoteConfigure == null) {
            return error("未查询到【填报配置】记录");
        }
        return success(repoteConfigure);
    }

    /**
     * 新增填报配置
     */
    @ApiOperation("新增填报配置")
    //@RequiresPermissions("repote:configure:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody RepoteConfigure repoteConfigure) {
        return toAjax(repoteConfigureService.save(repoteConfigure));
    }

    /**
     * 修改填报配置
     */
    @ApiOperation("修改填报配置")
    //@RequiresPermissions("repote:configure:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody RepoteConfigure repoteConfigure) {
        return toAjax(repoteConfigureService.updateById(repoteConfigure));
    }

    /**
     * 删除填报配置
     */
    @ApiOperation("删除填报配置")
    //@RequiresPermissions("repote:configure:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(repoteConfigureService.removeById(id));
    }

    /**
     * 导出填报配置列表
     */
    @ApiOperation("导出填报配置列表")
    //@RequiresPermissions("repote:configure:export")
    @Log(title = "填报配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<RepoteConfigure> list = repoteConfigureService.list();
        ExcelUtil<RepoteConfigure> util = new ExcelUtil<RepoteConfigure>(RepoteConfigure.class);
        util.exportExcel(response, list, "填报配置数据");
    }


}
