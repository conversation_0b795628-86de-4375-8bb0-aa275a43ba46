package com.ruoyi.patrol.service.handler.impl;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.manage.api.domain.bridge.BridgeStaticRequest;
import com.ruoyi.manage.api.domain.bridge.BridgeStaticResponse;
import com.ruoyi.manage.api.service.BaseBridgeStaticService;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.service.BaseBridgeResponseCacheService;
import com.ruoyi.patrol.service.enrichment.DataEnrichmentService;
import com.ruoyi.patrol.service.handler.AssetDataHandler;
import com.ruoyi.patrol.utils.DomainToCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 桥梁数据处理器
 */
@Slf4j
@Component
public class BridgeDataHandler implements AssetDataHandler<BridgeStaticRequest, BridgeStaticResponse, BaseBridgeResponseCache> {

    @Resource
    private BaseBridgeStaticService baseBridgeStaticService;
    
    @Resource
    private BaseBridgeResponseCacheService cacheService;
    
    @Resource
    private DataEnrichmentService dataEnrichmentService;

    @Override
    public AssetType getAssetType() {
        return AssetType.BRIDGE;
    }

    @Override
    public Class<BridgeStaticRequest> getRequestClass() {
        return BridgeStaticRequest.class;
    }

    @Override
    public Class<BaseBridgeResponseCache> getCacheClass() {
        return BaseBridgeResponseCache.class;
    }

    @Override
    public BridgeStaticRequest createRequestInstance() throws Exception {
        return BridgeStaticRequest.class.getDeclaredConstructor().newInstance();
    }

    @Override
    public MTableDataInfo<List<BridgeStaticResponse>> fetchData(BridgeStaticRequest request) {
        return baseBridgeStaticService.getInnerListPage(request, SecurityConstants.INNER);
    }

    /**
     * 将桥梁响应对象列表转换为缓存对象列表
     * 直接使用原始类型转换，避免通过BaseDataDomain转换造成的字段丢失
     * 
     * @param responses 桥梁响应对象列表
     * @return 桥梁缓存对象列表
     */
    @Override
    public List<BaseBridgeResponseCache> convertToCacheList(List<BridgeStaticResponse> responses) {
        log.info("开始转换桥梁数据，数量：{}", responses != null ? responses.size() : 0);
        // 使用直接转换方法，保留特定字段信息
        List<BaseBridgeResponseCache> cacheList = DomainToCacheUtils.convertBridgeList(responses);
        log.info("桥梁数据转换完成，结果数量：{}", cacheList.size());
        
        if (log.isDebugEnabled() && !cacheList.isEmpty()) {
            BaseBridgeResponseCache sample = cacheList.get(0);
            log.debug("桥梁数据转换示例: ID={}, assetId={}, assetName={}",
                    sample.getId(), sample.getAssetId(), sample.getAssetName());
        }
        
        return cacheList;
    }

    @Override
    public void prepareRequest(AssetBaseDataRequest assetRequest, BridgeStaticRequest request, boolean dataRule) {
        BeanUtils.copyProperties(assetRequest, request);
        request.setIfDataRule(dataRule);
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T extends BaseDataCache> void saveCache(List<T> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        try {
            List<BaseBridgeResponseCache> typedList = (List<BaseBridgeResponseCache>) dataList;
            cacheService.clearTable();

            // 分批保存，每批1000条记录
            int batchSize = 1000;
            int totalSize = typedList.size();
            log.info("开始分批保存桥梁数据，总记录数: {}，批次大小: {}", totalSize, batchSize);

            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<BaseBridgeResponseCache> batch = typedList.subList(i, endIndex);

                try {
                    cacheService.insertBatch(batch);
                    log.info("桥梁数据批次保存成功，批次: {}-{}/{}", i + 1, endIndex, totalSize);
                } catch (Exception e) {
                    log.error("桥梁数据批次保存失败，批次: {}-{}", i + 1, endIndex, e);
                    throw e;
                }
            }

            log.info("桥梁数据缓存成功，共{}条记录", totalSize);
        } catch (ClassCastException e) {
            log.error("桥梁数据类型转换失败", e);
            throw new RuntimeException("桥梁数据类型转换失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("桥梁数据缓存失败", e);
            throw new RuntimeException("桥梁数据缓存失败: " + e.getMessage());
        }
    }
    
    @Override
    public <T extends BaseDataCache> void enrichData(List<T> dataList) {
        dataEnrichmentService.enrichData(dataList);
    }
} 