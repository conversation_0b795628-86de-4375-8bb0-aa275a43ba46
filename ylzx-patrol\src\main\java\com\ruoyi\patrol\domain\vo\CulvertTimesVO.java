package com.ruoyi.patrol.domain.vo;

import java.math.BigDecimal;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.patrol.utils.StackUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 涵洞巡查统计VO
 * @author: QD
 * @date: 2024年11月25日 14:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CulvertTimesVO {
    private String id;
    @Excel(name = "涵洞编码", sort = 1)
    private String assetCode;
    @Excel(name = "养护路段", sort = 2)
    private String maintenanceSectionName;
    @Excel(name = "路线编码", sort = 3)
    private String routeCode;
    @Excel(name = "桩号", sort = 4)
    private String centerStakeFrom;
    @Excel(name = "本月应检查次数", sort = 5)
    private Integer monthRequiredCount;
    @Excel(name = "本月已检查次数", sort = 6)
    private Integer monthCheckedCount;
    @Excel(name = "本季度应检查次数", sort = 7)
    private Integer quarterRequiredCount;
    @Excel(name = "本季度已检查次数", sort = 8)
    private Integer quarterCheckedCount;
    @Excel(name = "本年应检查次数", sort = 9)
    private Integer yearRequiredCount;
    @Excel(name = "本年已检查次数", sort = 10)
    private Integer yearCheckedCount;

    private BigDecimal centerStake;

    public String getCenterStakeFrom() {
        return StackUtils.formatStack(centerStake);
    }
}
