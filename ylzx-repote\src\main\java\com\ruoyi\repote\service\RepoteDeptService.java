package com.ruoyi.repote.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.repote.domain.RepoteDept;

/**
 * 填报单位Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface RepoteDeptService extends IService<RepoteDept> {

    /**
     * 根据条件查询填报单位数据列表
     * @param params
     */
    List<RepoteDept> findListByParam(Map<String, Object> params);

}
