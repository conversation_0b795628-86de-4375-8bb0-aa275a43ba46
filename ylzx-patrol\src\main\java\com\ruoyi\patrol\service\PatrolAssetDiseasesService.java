package com.ruoyi.patrol.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patrol.domain.PatrolAssetDiseases;

import java.util.List;

/**
 * 资产病害信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface PatrolAssetDiseasesService extends IService<PatrolAssetDiseases> {

    /**
     * 根据部件类型查询病害信息
     * @param partsType 部件类型
     * @return 病害信息
     */
    List<PatrolAssetDiseases> selectPatrolPartsInfoByPartsType(String partsType);
}
