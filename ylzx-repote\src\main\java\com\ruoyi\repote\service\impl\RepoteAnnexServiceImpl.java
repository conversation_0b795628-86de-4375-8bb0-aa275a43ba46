package com.ruoyi.repote.service.impl;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repote.mapper.RepoteAnnexMapper;
import com.ruoyi.repote.domain.RepoteAnnex;
import com.ruoyi.repote.service.RepoteAnnexService;

/**
 * 上传附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class RepoteAnnexServiceImpl extends ServiceImpl<RepoteAnnexMapper, RepoteAnnex> implements RepoteAnnexService {

    @Autowired
    private RepoteAnnexMapper repoteAnnexMapper;

    @Override
    public List<RepoteAnnex> findListByParam(Map<String, Object> params) {
        return repoteAnnexMapper.findListByParam(params);
    }


}
