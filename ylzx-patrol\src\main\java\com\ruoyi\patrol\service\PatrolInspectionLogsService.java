package com.ruoyi.patrol.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.manage.api.domain.RemoteRoadDiseaseResponse;
import com.ruoyi.patrol.domain.PatrolInspectionLogs;
import com.ruoyi.patrol.domain.dto.AssetStatsDTO;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.vo.*;
import io.swagger.models.auth.In;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * 巡查日志Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
public interface PatrolInspectionLogsService extends IService<PatrolInspectionLogs> {

    List<PatrolInspectionLogs> findListByParam(Map<String, Object> params);

    /**
     * 统计用户巡查记录数量
     * @param params 查询参数
     * @return 巡查记录数量
     */
    Integer countListByUser(Map<String, Object> params);

    /**
     * 根据id查询
     *
     * @param id id
     * @return PatrolInspectionLogs
     */
    PatrolInspectionLogs selectById(String id);


    /**
     * 寻找时间区间内各个路段最新的一条巡查记录
     * @param lastTime 最近生成巡查记录的时间
     * @param nowTime 当前时间
     * @param sectionIdList 路段id列表
     * @return List<PatrolInspectionLogs>
     */
    List<PatrolInspectionLogs> findLastOneByTime(LocalDate lastTime, LocalDate nowTime, List<String> sectionIdList);


    /**
     * 获取当前用户的巡查记录，时间区间内的 preTime<= checkTime < endTime
     * @param sectionIds 养护路段id列表
     * @param preTime 开始时间
     * @param endTime 结束时间
     */
    List<PatrolInspectionLogs> findListByUserAndTime(List<String> sectionIds, LocalDateTime preTime,
            LocalDateTime endTime, Integer nowTime);


    /**
     * 根据巡查记录id获取巡查事件
     * @param recordId 巡查记录id
     * @return 巡查事件列表
     */
    List<RemoteRoadDiseaseResponse> getRoadDiseaseList(String recordId);

    /**
     * 获取指定养护路段和月份内有数据的日期列表
     *
     * @param maintenanceSectionId 养护路段ID
     * @param yearMonth            年月，格式为yyyy-MM
     * @return 日期列表
     */
    List<Integer> getDaysWithData(String maintenanceSectionId, String yearMonth);

    /**
     * 查询指定养护路段和日期内有数据的小时列表
     * @param sectionId 养护路段ID
     * @param lastDate 上次日期
     * @param nowDate 当前日期
     * @return 小时列表
     */
    PatrolInspectionLogs findLatestBySectionIdAndTime(String sectionId,LocalDate lastDate, LocalDate nowDate);

    /**
     * 批量插入巡查记录
     * @param logsList 巡查记录列表
     */
    int insertBatch(List<PatrolInspectionLogs> logsList);

    /**
     * 批量删除巡查记录
     * @param ids 巡查记录ID
     * @return 结果
     */
    int deleteByIds(List<String> ids);

    /**
     * 获取巡查统计数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 巡查统计数据
     */
    InspectionStatsVO patrolDetail(LocalDateTime startTime, LocalDateTime endTime);

    InspectionStatsVO patrolDetailNew(String managementMaintenanceId, Integer year, Integer month);

    InspectionStatsVO patrolDetailNew();

    /**
     * 获取巡查统计数据(按部门)分组
     * @param startTime  开始时间
     * @param endTime   结束时间
     * @return 巡查统计数据
     */
    List<InspectionStatsVO> patrolDetailByDepartments(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取当前条件下的养护里程总和
     * @param request 查询条件
     * @return 养护里程总和
     */
    BigDecimal getTotalPatrolMileage(AssetBaseDataRequest request);

    /**
     * 权限id设置
     *
     * @param request 资产请求对象
     */
    void setDeptIds(AssetBaseDataRequest request);

    /**
     * 设置巡查日志的用户信息和签名URL
     *
     * @param logsList 巡查日志列表
     */
    void setUserInfoAndSignUrl(List<PatrolInspectionLogs> logsList);

    /**
     * 保存巡查日志（包含巡查人员）
     *
     * @param patrolInspectionLogs 巡查日志信息
     * @return 巡查日志
     */
    PatrolInspectionLogs savePatrolInspectionLogs(PatrolInspectionLogs patrolInspectionLogs);

    /**
     * 更新巡查日志（包含巡查人员）
     *
     * @param patrolInspectionLogs 巡查日志信息
     * @return 更新结果
     */
    boolean updatePatrolInspectionLogs(PatrolInspectionLogs patrolInspectionLogs);

    AssetStatsDTO getAssetStats(Integer type);

    List<AssetOftenPatrolVO> getDeptAssetOftenCountList(Integer type);

    List<InspectionGroupCountVO> deptPatrolMileageCount(Integer year, Integer type);

    List<MileageCountVO> getMaintenanceSectionIdMileage(Integer year, String maintenanceSectionId);


    /**
     * 导出报表卡片，可存储签名URL
     * 
     * @param params 查询参数
     * @param signUrlMap 存储签名URL的Map
     * @return 巡查日志列表
     */
    List<PatrolInspectionLogs> exportReportCard(Map<String, Object> params, Map<String, String> signUrlMap);


    List<PatrolInspectionLogsTotalVO> countByPatrolUnit(Map<String, Object> params) throws ExecutionException, InterruptedException;
    List<PatrolInspectionLogsTotalVO> countByMaintenanceSubUnit(Map<String, Object> params) throws ExecutionException, InterruptedException;

    List<PatrolInspectionLogsTotalVO> countByMaintenanceUnit(Map<String, Object> params);

    List<PatrolInspectionLogsTotalVO> fillOtherFields(List<PatrolInspectionLogsTotalVO> patrolInspectionLogsTotalVOList) throws InterruptedException, ExecutionException ;

    /**
     * 针对较差网络环境下的巡查日志新增
     *
     * @param patrolInspectionLogs 巡查日志信息
     * @return 巡查日志
     */
    PatrolInspectionLogs addPatrolInspectionLogsInPoorNetwork(PatrolInspectionLogs patrolInspectionLogs);
}
