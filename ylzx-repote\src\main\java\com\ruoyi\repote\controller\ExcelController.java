package com.ruoyi.repote.controller;


import com.ruoyi.utils.ExcelUtilService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/excel")
public class ExcelController {
    final ExcelUtilService excelService;
    @RequestMapping("/list")
    public Object list() {



        return null;
    }
}
