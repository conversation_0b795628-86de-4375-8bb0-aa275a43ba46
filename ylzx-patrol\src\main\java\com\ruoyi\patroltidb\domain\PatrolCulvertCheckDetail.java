package com.ruoyi.patroltidb.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 涵洞巡检查子表对象 patrol_culvert_check_detail
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="涵洞巡检查子表")
@TableName("patrol_culvert_check_detail")
@Data
public class PatrolCulvertCheckDetail extends PatrolAssetCheckDetail {
    private static final long serialVersionUID = 1L;

}
