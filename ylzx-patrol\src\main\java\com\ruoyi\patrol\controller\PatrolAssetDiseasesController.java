package com.ruoyi.patrol.controller;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.patrol.domain.PatrolAssetDiseases;
import com.ruoyi.patrol.service.PatrolAssetDiseasesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 资产病害信息Controller
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Api(tags = "资产病害信息" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/diseases")
public class PatrolAssetDiseasesController extends BaseController {
    final private PatrolAssetDiseasesService patrolAssetDiseasesService;


    /**
     * 查询资产病害信息列表(分页)
     */
    @ApiOperation("查询资产病害信息列表")
    //@RequiresPermissions("patrol:diseases:list")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam  Map<String, Object> params) {
        startPage();
        List<PatrolAssetDiseases> list = patrolAssetDiseasesService.list(generateQueryWrapper(params));
            return getDataTable(list);
    }

    /**
     * 查询资产病害信息列表(不分页)
     */
    @ApiOperation("查询资产病害信息列表(不分页)")
    //@RequiresPermissions("patrol:diseases:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll(@RequestParam  Map<String, Object> params) {
        List<PatrolAssetDiseases> list = patrolAssetDiseasesService.list(generateQueryWrapper(params));
        return success(list);
    }

    /**
     * 查询资产病害信息列表(微服务调用)
     */
    @ApiOperation("查询资产病害信息列表(微服务调用)")
    @GetMapping("/getAssetDiseasesList")
    public R<List<PatrolAssetDiseases>> getAssetDiseasesList() {
        return R.ok(patrolAssetDiseasesService.list());
    }

    /**
     * 根据id查询资产病害信息数据
     */
    @ApiOperation("根据id查询资产病害信息数据")
    //@RequiresPermissions("patrol:diseases:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            PatrolAssetDiseases patrolAssetDiseases = patrolAssetDiseasesService.getById(id);
        if (patrolAssetDiseases == null) {
            return error("未查询到【资产病害信息】记录");
        }
        return success(patrolAssetDiseases);
    }

    /**
     * 新增资产病害信息
     */
    @ApiOperation("新增资产病害信息")
    //@RequiresPermissions("patrol:diseases:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolAssetDiseases patrolAssetDiseases) {
        return toAjax(patrolAssetDiseasesService.save(patrolAssetDiseases));
    }

    /**
     * 修改资产病害信息
     */
    @ApiOperation("修改资产病害信息")
    //@RequiresPermissions("patrol:diseases:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolAssetDiseases patrolAssetDiseases) {
        return toAjax(patrolAssetDiseasesService.updateById(patrolAssetDiseases));
    }

    /**
     * 删除资产病害信息
     */
    @ApiOperation("删除资产病害信息")
    //@RequiresPermissions("patrol:diseases:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolAssetDiseasesService.removeById(id));
    }

    /**
     * 导出资产病害信息列表
     */
    @ApiOperation("导出资产病害信息列表")
    //@RequiresPermissions("patrol:diseases:export")
    @Log(title = "资产病害信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolAssetDiseases> list = patrolAssetDiseasesService.list();
        ExcelUtil<PatrolAssetDiseases> util = new ExcelUtil<PatrolAssetDiseases>(PatrolAssetDiseases.class);
        util.exportExcel(response, list, "资产病害信息数据");
    }


    // TODO 后面代码生成器改好了在重新生成下
    private QueryWrapper<PatrolAssetDiseases> generateQueryWrapper(@RequestParam  Map<String, Object> params){

        String assetType = MapUtil.getStr(params, "assetType");
        String assetName = MapUtil.getStr(params, "assetName");
        String diseaseCode = MapUtil.getStr(params, "diseaseCode");
        String diseaseName = MapUtil.getStr(params, "diseaseName");
        String pileMode = MapUtil.getStr(params, "pileMode");
        QueryWrapper<PatrolAssetDiseases> qw = new QueryWrapper<>();
        qw.orderByAsc("sort");
        qw.orderByDesc("create_time");
        qw.eq(StringUtils.isNotBlank(assetType),"asset_type", assetType);
        qw.eq(StringUtils.isNotBlank(assetName),"asset_name", assetName);
        qw.like(StringUtils.isNotBlank(diseaseCode),"disease_code", diseaseCode);
        qw.like(StringUtils.isNotBlank(diseaseName),"disease_name", diseaseName);
        qw.eq(StringUtils.isNotBlank(pileMode),"pile_mode", pileMode);
        return qw;
    }

}
