package com.ruoyi.other.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.ruoyi.other.domain.OtherTransportationRoad;
import io.lettuce.core.dynamic.annotation.Param;


/**
 * 大件运输Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface OtherTransportationRoadMapper extends BaseMapper<OtherTransportationRoad> {

    List<OtherTransportationRoad> findListByParam(Map<String, Object> params);

    List<String> getRoadIdsByTransportationId(String id);

    int deleteByTransportationIdAndRoadId(@Param("transportationId") String transportationId);
}
