package com.ruoyi.repote.mapper;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.repote.domain.RepoteRecord;
import org.apache.ibatis.annotations.Mapper;

/**
 * 填报记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Mapper
public interface RepoteRecordMapper extends BaseMapper<RepoteRecord> {

    List<RepoteRecord> findListByParam(Map<String, Object> params);


}
