package com.ruoyi.patrol.service.handler.impl;

import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * "隧道日常巡查记录表"生成处理器。
 * 继承自抽象处理器以重用通用的Excel生成逻辑。
 */
@Slf4j
public class TunnelDailyReportHandler extends AbstractExcelReportHandler<PatrolAssetCheck> {

    private final List<PatrolAssetCheck> reportDataList;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");

    // --- 此报表的特定样式定义 ---
    // 继承的样式：titleStyle, headerLabelStyle, headerValueStyle, itemHeaderStyle, itemCellStyle, itemFirstColStyle
    // 我们可能需要为隧道报表中的特定列调整对齐方式
    private CellStyle itemContentStyle;     // 检查内容样式 (左对齐, 自动换行)
    private CellStyle itemStatusStyle;      // 状态描述样式 (居中)
    private CellStyle itemConclusionStyle;  // 判断结论样式 (居中)
    private CellStyle footerLabelStyle;     // 页脚标签样式 (继承并确保边框)
    private CellStyle footerValueStyle;     // 页脚值样式 (继承并确保边框)
    private CellStyle normalBorderStyle;    // 仅用于细边框的通用样式

    private CellStyle noBorderStyle;      // 无边框左对齐自动换行样式

    // 继承的：signUrlMap, signImageMap, failedSignIds, log, SIGNATURE_HEIGHT_POINTS, httpClient

    public TunnelDailyReportHandler(List<PatrolAssetCheck> reportDataList, Map<String, String> signUrlMap) {
        // super(); // 隐式调用
        this.reportDataList = reportDataList != null ? reportDataList : new ArrayList<>();
        if (signUrlMap != null) {
            this.signUrlMap.putAll(signUrlMap); // 填充继承的签名URL映射
        }
        // 签名预加载将在 beforeSheetCreate 中调用
    }

    /**
     * 重写createStyles方法，为此隧道报表类型添加特定样式
     * 并初始化基类中的通用样式，确保所有样式都有边框。
     */
    @Override
    protected void createStyles(Workbook workbook) {
        super.createStyles(workbook); // 首先初始化通用样式

        // --- 通用边框样式 ---
        normalBorderStyle = workbook.createCellStyle();
        setThinBorders(normalBorderStyle); // 使用继承的辅助方法设置细黑边框

        // --- 确保所有继承的样式都有边框 ---
        // 克隆基类样式并应用边框，因为此报表几乎处处需要边框

        // 无边框左对齐自动换行样式
        noBorderStyle = workbook.createCellStyle();
        noBorderStyle.setBorderTop(BorderStyle.NONE);
        noBorderStyle.setBorderBottom(BorderStyle.NONE);
        noBorderStyle.setBorderLeft(BorderStyle.NONE);
        noBorderStyle.setBorderRight(BorderStyle.NONE);
        noBorderStyle.setAlignment(HorizontalAlignment.LEFT);
        noBorderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        noBorderStyle.setWrapText(true);

        // 标题样式 (通常不需要边框，保持原样)
        // titleStyle = super.titleStyle;

        // 标题标签样式
        CellStyle baseHeaderLabelStyle = super.headerLabelStyle;
        headerLabelStyle = workbook.createCellStyle();
        headerLabelStyle.cloneStyleFrom(baseHeaderLabelStyle);
        setSolidBackground(headerLabelStyle, new java.awt.Color(217, 217, 217));
        copyBorders(normalBorderStyle, headerLabelStyle); // 应用边框

        // 标题值样式
        CellStyle baseHeaderValueStyle = super.headerValueStyle;
        headerValueStyle = workbook.createCellStyle();
        headerValueStyle.cloneStyleFrom(baseHeaderValueStyle);
        copyBorders(normalBorderStyle, headerValueStyle); // 应用边框

        // 项目标题样式
        CellStyle baseItemHeaderStyle = super.itemHeaderStyle;
        itemHeaderStyle = workbook.createCellStyle();
        itemHeaderStyle.cloneStyleFrom(baseItemHeaderStyle);
        setSolidBackground(itemHeaderStyle, new java.awt.Color(217, 217, 217));
        copyBorders(normalBorderStyle, itemHeaderStyle); // 应用边框

        // 项目单元格样式 (通用居中，带边框)
        CellStyle baseItemCellStyle = super.itemCellStyle;
        itemCellStyle = workbook.createCellStyle();
        itemCellStyle.cloneStyleFrom(baseItemCellStyle);
        copyBorders(normalBorderStyle, itemCellStyle); // 应用边框

        // 项目第一列样式 (左对齐，带边框)
        CellStyle baseItemFirstColStyle = super.itemFirstColStyle;
        itemFirstColStyle = workbook.createCellStyle();
        itemFirstColStyle.cloneStyleFrom(baseItemFirstColStyle);
        copyBorders(normalBorderStyle, itemFirstColStyle); // 应用边框

        // --- 隧道报表特定的项目样式 ---
        // 检查内容样式 (基于第一列样式，左对齐，自动换行，带边框)
        itemContentStyle = workbook.createCellStyle();
        itemContentStyle.cloneStyleFrom(itemFirstColStyle); // 继承字体, 边框, 换行, 左对齐
        // 确保自动换行开启 (已从itemFirstColStyle继承)
        itemContentStyle.setWrapText(true);

        // 状态描述样式 (基于通用项目样式，居中，自动换行，带边框)
        itemStatusStyle = workbook.createCellStyle();
        itemStatusStyle.cloneStyleFrom(itemCellStyle); // 继承字体, 边框, 换行, 居中
        itemStatusStyle.setWrapText(true);

        // 判断结论样式 (基于通用项目样式，居中，自动换行，带边框)
        itemConclusionStyle = workbook.createCellStyle();
        itemConclusionStyle.cloneStyleFrom(itemCellStyle); // 继承字体, 边框, 换行, 居中
        itemConclusionStyle.setWrapText(true);

        // --- 页脚样式 (基于带边框的标题样式) ---
        // 页脚标签样式 (右对齐，带边框)
        footerLabelStyle = workbook.createCellStyle();
        footerLabelStyle.cloneStyleFrom(headerLabelStyle); // 继承字体, 对齐, 边框
        footerLabelStyle.setWrapText(true);

        // 页脚值样式 (左对齐，带边框) - 用于显示名字或留空给签名
        footerValueStyle = workbook.createCellStyle();
        footerValueStyle.cloneStyleFrom(headerValueStyle); // 继承字体, 对齐, 边框
        footerValueStyle.setWrapText(true);
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        super.beforeSheetCreate(writeWorkbookHolder, writeSheetHolder); // 调用createStyles创建样式
        // 在生成表格内容前预加载签名图像
        if (!signUrlMap.isEmpty()) {
            preloadSignImages(); // 调用继承的预加载方法
        } else {
            log.warn("签名URL映射为空，将无法加载签名图片。");
        }
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        // Workbook workbook = writeWorkbookHolder.getWorkbook(); // 可以通过 sheet.getWorkbook() 获取

        // 诊断日志
        log.info("开始生成隧道日常巡查记录表...");
        log.info("签名URL映射包含 {} 条记录。", signUrlMap.size());
        log.info("预加载的签名图像缓存包含 {} 条记录。", signImageMap.size());
        if (!failedSignIds.isEmpty()) {
            log.warn("有 {} 个签名图像下载失败。失败的ID: {}", failedSignIds.size(), failedSignIds);
        }

        int currentRowIndex = 0; // 当前行索引

        // --- 设置列宽 (根据图片估算) ---
        // A: 里程桩号/检查人标签  B: 项目名称/检查人值  C: 检查内容/记录人标签  D: 状态描述/记录人值  E: 判断结论  F: 空/天气值
        sheet.setColumnWidth(0, 10 * 256);  // A列
        sheet.setColumnWidth(1, 13 * 256);  // B列
        sheet.setColumnWidth(2, 14 * 256);  // C列 (检查内容较长)
        sheet.setColumnWidth(3, 16 * 256);  // D列
        sheet.setColumnWidth(4, 11 * 256);  // E列
        sheet.setColumnWidth(5, 20 * 256);  // F列 (用于天气等)

        // 存储列宽信息用于行高调整
        int[] columnWidths = new int[] {10, 13, 14, 16, 11, 20};

        if (reportDataList == null || reportDataList.isEmpty()) {
            log.warn("报告数据列表为空。生成空表格。");
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "隧道日常巡查记录表 (无数据)", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并A-F
            return; // 结束处理
        }

        // 循环处理每个PatrolAssetCheck对象，生成一个表格
        for (int i = 0; i < reportDataList.size(); i++) {
            PatrolAssetCheck reportData = reportDataList.get(i);
            if (reportData == null) {
                log.warn("跳过索引 {} 处的null报告数据对象", i);
                continue;
            }

            log.debug("正在为资产编码 {} 生成隧道报告部分", reportData.getAssetCode());

            // 1. 标题行
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "隧道日常巡查记录表", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并A-F
            currentRowIndex++;

            // 2. 管理单位行
            Row managementUnitRow = sheet.createRow(currentRowIndex);
            String unitName = reportData.getPropertyUnitName() != null ? reportData.getPropertyUnitName() : reportData.getMaintainUnitName();
            // 先创建单元格再合并，确保样式和边框
            for (int col = 0; col <= 5; col++) {
                if (col == 0) {
                    createCell(managementUnitRow, col, "管理单位：" + Objects.toString(unitName, ""), headerValueStyle); // 左对齐值样式
                } else {
                    createCell(managementUnitRow, col, "", headerValueStyle); // 为合并和边框创建空单元格
                }
            }
            applyRowBorder(managementUnitRow, 0, 5, normalBorderStyle); // 应用行边框
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并A-F
            setConditionalRowHeight(managementUnitRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 3. 隧道信息行
            Row tunnelInfoRow = sheet.createRow(currentRowIndex);
            createCell(tunnelInfoRow, 0, "隧道名称", headerLabelStyle);
            createCell(tunnelInfoRow, 1, Objects.toString(reportData.getAssetName(), ""), headerValueStyle);
            createCell(tunnelInfoRow, 2, "隧道编码", headerLabelStyle);
            createCell(tunnelInfoRow, 3, Objects.toString(reportData.getAssetCode(), ""), headerValueStyle);
            createCell(tunnelInfoRow, 4, "中心桩号", headerLabelStyle);
            // 使用继承的 formatStake 方法格式化桩号
            createCell(tunnelInfoRow, 5, formatStake(reportData.getCenterStake(), reportData.getStakeFormat()), headerValueStyle);
            applyRowBorder(tunnelInfoRow, 0, 5, normalBorderStyle);
            setConditionalRowHeight(tunnelInfoRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 4. 路线信息行
            Row routeInfoRow = sheet.createRow(currentRowIndex);
            createCell(routeInfoRow, 0, "路线编码", headerLabelStyle);
            createCell(routeInfoRow, 1, Objects.toString(reportData.getRouteCode(), ""), headerValueStyle);
            createCell(routeInfoRow, 2, "路线名称", headerLabelStyle);
            // 路线名称可能在 maintenanceSectionName 或 routeName 字段，根据实际情况调整
            createCell(routeInfoRow, 3, Objects.toString(reportData.getMaintenanceSectionName(), ""), headerValueStyle);
            createCell(routeInfoRow, 4, "养护单位", headerLabelStyle);
            createCell(routeInfoRow, 5, Objects.toString(reportData.getMaintainUnitName(), ""), headerValueStyle);
            applyRowBorder(routeInfoRow, 0, 5, normalBorderStyle);
            setConditionalRowHeight(routeInfoRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 5. 检查日期和天气行
            Row dateWeatherRow = sheet.createRow(currentRowIndex);
            createCell(dateWeatherRow, 0, "检查日期", headerLabelStyle);
            Date checkDate = reportData.getCheckTime();
            createCell(dateWeatherRow, 1, checkDate != null ? dateFormat.format(checkDate) : "", headerValueStyle);
            createCell(dateWeatherRow, 2, "天气", headerLabelStyle);
            // 天气信息需要从 PatrolAssetCheck 获取，假设字段为 weather
            String weather = reportData.getWeather();
            createCell(dateWeatherRow, 3, Objects.toString(weather, ""), headerValueStyle); // 天气值放在D列
            // 为合并创建空单元格 (E, F) 并应用样式以保持边框
            createCell(dateWeatherRow, 4, "", headerLabelStyle);
            createCell(dateWeatherRow, 5, "", headerValueStyle);
            applyRowBorder(dateWeatherRow, 0, 5, normalBorderStyle); // 应用行边框
            setConditionalRowHeight(dateWeatherRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 6. 项目标题行
            Row itemHeaderRow = sheet.createRow(currentRowIndex);
            itemHeaderRow.setHeightInPoints(25); // 设置合适的行高
            createCell(itemHeaderRow, 0, "里程桩号", itemHeaderStyle); // A
            createCell(itemHeaderRow, 1, "项目名称", itemHeaderStyle); // B
            createCell(itemHeaderRow, 2, "检查内容", itemHeaderStyle); // C
            createCell(itemHeaderRow, 3, "", itemHeaderStyle); // D
            createCell(itemHeaderRow, 4, "状态描述", itemHeaderStyle); // E
            createCell(itemHeaderRow, 5, "判断结论", itemHeaderStyle);        // F (保持表格结构和边框)
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 2, 3)); // 合并D-F作为天气区域
            // itemHeaderStyle 已经包含边框，无需单独调用 applyRowBorder
            currentRowIndex++;

            // 7. 项目详情行
            List<PatrolAssetCheckDetail> details = reportData.getPatrolCheckDetailList();
            if (details != null && !details.isEmpty()) {
                for (PatrolAssetCheckDetail item : details) {
                    if (item == null) {
                        log.warn("跳过资产编码 {} 的null详情项", reportData.getAssetCode());
                        continue;
                    }
                    Row itemRow = sheet.createRow(currentRowIndex);
                    // 设置自动行高或根据内容估算
                    // itemRow.setHeightInPoints(18);

                    // A列: 里程桩号 (通常是部件类型名称，如洞口、洞门) - 左对齐
                    createCell(itemRow, 0, "", itemFirstColStyle);

                    // B列: 项目名称 (也可能是部件类型名称) - 左对齐
                    createCell(itemRow, 1, Objects.toString(item.getPartsTypeName(), ""), itemFirstColStyle);

                    // C列: 检查内容 (长文本) - 左对齐，自动换行
                    createCell(itemRow, 2, item.getDes() == null || item.getDes().trim().isEmpty() ? "无" : item.getDes(), itemContentStyle);

                    // D列: 空白单元格，但应用边框以保持一致性
                    createCell(itemRow, 3, "", itemFirstColStyle);

                    // E列: 状态描述 (通常是 "未见异常" 或具体缺陷) - 居中
                    // 使用 item.getDefect()，如果为空则显示 "未见异常"
                    createCell(itemRow, 4, item.getDefect() == null || item.getDefect().trim().isEmpty() ? "未见异常" : item.getDefect(), itemStatusStyle);

                    // F列: 判断结论 (通常是 "情况正常" 或其他) - 居中
                    // 使用 item.getAdvice()，如果为空则显示 "情况正常"
                    createCell(itemRow, 5, item.getAdvice() == null || item.getAdvice().trim().isEmpty() ? "情况正常" : item.getAdvice(), itemConclusionStyle);


                    sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 2, 3)); // 合并D-F作为天气区域

                    // 应用条件高度而不是固定高度
                    setConditionalRowHeight(itemRow, 0, 5, columnWidths);

                    currentRowIndex++;
                }
            } else {
                // 如果没有检查明细，添加一行提示
                log.info("资产编码 {} 未找到检查详情", reportData.getAssetCode());
                Row emptyRow = sheet.createRow(currentRowIndex);
                createCell(emptyRow, 0, "无检查明细", itemFirstColStyle); // 左对齐
                // 创建其他空单元格并合并，应用边框
                for (int col = 1; col <= 5; col++) {
                    createCell(emptyRow, col, "", itemFirstColStyle); // 使用相同样式保持一致
                }
                applyRowBorder(emptyRow, 0, 5, normalBorderStyle); // 确保边框
                sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并A-F
                setConditionalRowHeight(emptyRow, 0, 5, columnWidths);
                currentRowIndex++;
            }

            // 2. 添加空白行防止签名越界
            Row blankRow1 = sheet.createRow(currentRowIndex);
            createCell(blankRow1, 1, "", noBorderStyle); // 合并创建空单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            blankRow1.setHeightInPoints(5);
            currentRowIndex++;

            // 8. 页脚行 (检查人、记录人标签和签名区域)
            int footerLabelRowIndex = currentRowIndex;
            Row footerLabelRow = sheet.createRow(footerLabelRowIndex);
            footerLabelRow.setHeightInPoints(25); // 为标签行设置高度

            // A列: 检查人标签 (右对齐)
            createCell(footerLabelRow, 0, "检查人", footerLabelStyle);
            // B列: 检查人姓名/签名占位符 (左对齐)
            Cell kahunaNameCell = createCell(footerLabelRow, 1, "", footerValueStyle);
            // C列: 空白，但应用边框
            createCell(footerLabelRow, 2, "", footerValueStyle);
            // D列: 记录人标签 (右对齐)
            createCell(footerLabelRow, 3, "记录人", footerLabelStyle);
            // E列: 记录人姓名/签名占位符 (左对齐)
            Cell oprUserNameCell = createCell(footerLabelRow, 4, "", footerValueStyle);
            // F 列: 空白，但应用边框
            createCell(footerLabelRow, 5, "", footerValueStyle);

            // 应用整行边框
            applyRowBorder(footerLabelRow, 0, 5, normalBorderStyle);

            // 9. 添加签名图片 (使用继承的方法)
            int signatureStartRowIndex = footerLabelRowIndex; // 签名图片从标签行开始放置（图片锚点在这一行）
            int lastRowForKahuna = signatureStartRowIndex; // 默认下一行
            int lastRowForOpr = signatureStartRowIndex; // 默认下一行

            List<String> kahunaSignList = reportData.getKahunaSignList(); // 负责人/检查人签名ID列表
            List<String> oprUserSignList = reportData.getOprUserSignList(); // 操作员/记录人签名ID列表

            log.debug("检查人 ({}) 签名ID数量: {}", reportData.getKahunaName(), kahunaSignList != null ? kahunaSignList.size() : 0);
            log.debug("记录人 ({}) 签名ID数量: {}", reportData.getOprUserName(), oprUserSignList != null ? oprUserSignList.size() : 0);

            // 添加检查人签名到 B 列 (索引 1)
            if (kahunaSignList != null && !kahunaSignList.isEmpty()) {
                // 如果有签名图片，调用方法添加图片，它会返回图片占用的最后一行之后的新行索引
                lastRowForKahuna = addSignatureImages(sheet, signatureStartRowIndex, 1,1,
                        kahunaSignList, Objects.toString(reportData.getKahunaName(), ""), footerValueStyle, true);
            } else {
                // 如果没有签名图片URL，在标签行的 B 列显示名字
                kahunaNameCell.setCellValue(Objects.toString(reportData.getKahunaName(), ""));
                log.warn("检查人 {} ({}) 无有效签名图片，仅显示姓名", reportData.getKahunaName(), reportData.getKahunaId());
            }

            // 添加记录人签名到 D 列 (索引 3)
            if (oprUserSignList != null && !oprUserSignList.isEmpty()) {
                // 如果有签名图片，调用方法添加图片
                lastRowForOpr = addSignatureImages(sheet, signatureStartRowIndex, 4,4,
                        oprUserSignList, Objects.toString(reportData.getOprUserName(), ""), footerValueStyle, true);
            } else {
                // 如果没有签名图片URL，在标签行的 D 列显示名字
                oprUserNameCell.setCellValue(Objects.toString(reportData.getOprUserName(), ""));
                log.warn("记录人 {} ({}) 无有效签名图片，仅显示姓名", reportData.getOprUserName(), reportData.getOprUserId());
            }

            // 确定页脚部分（包括签名）使用的最终行索引
            // 取决于哪个签名的图片占用了更多行
            int footerEndRowIndex = Math.max(lastRowForKahuna, lastRowForOpr);
            currentRowIndex = footerEndRowIndex; // 更新当前行索引到签名区域之后

            // === 当前隧道报告部分完成 ===

            // 在下一个报告部分之前插入分页符 (如果不是最后一个)
            if (i < reportDataList.size() - 1) {
                // 在当前报告的最后一行之后设置分页符
                sheet.setRowBreak(footerEndRowIndex - 1);
                log.debug("已在行 {} 之后为下一个隧道报告插入分页符", footerEndRowIndex - 1);
            }
        }

        log.info("已完成生成所有隧道日常巡查记录表部分。");
    }
}
