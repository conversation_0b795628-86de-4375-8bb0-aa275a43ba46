package com.ruoyi.patrol.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部件-病害关系对象 patrol_parts_diseases
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="部件-病害关系")
@TableName("patrol_parts_diseases")
@Data
public class PatrolPartsDiseases extends BaseTableEntity {
    private static final long serialVersionUID = 1L;

    /** 部件id */
    @Excel(name = "部件id")
    @ApiModelProperty(value = "部件id")
    private String partsId;

    /** 病害id */
    @Excel(name = "病害id")
    @ApiModelProperty(value = "病害id")
    private String diseasesId;

}
