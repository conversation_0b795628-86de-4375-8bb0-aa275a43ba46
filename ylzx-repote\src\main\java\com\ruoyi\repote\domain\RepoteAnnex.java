package com.ruoyi.repote.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseModelEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 上传附件对象 repote_annex
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Getter
@Setter
public class RepoteAnnex extends BaseModelEntity<RepoteAnnex> {
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @Excel(name = "任务ID")
    private Long missionIdAnnex;

    /**
     * 附件地址ID，需要确认
     */
    @Excel(name = "附件地址ID，需要确认")
    private Long repoteAddressId;

}
