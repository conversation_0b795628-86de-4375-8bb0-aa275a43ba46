package com.ruoyi.supervise.domain;


import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 督查记录对象 supervise_inspection_record
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@ApiModel(value = "督查记录")
@TableName("supervise_inspection_record")
@Data
public class SuperviseInspectionRecord extends BaseTableEntity {
    private static final long serialVersionUID = 1L;





    /**
     * 检查单位id
     */
    @ApiModelProperty(value = "检查单位id")
    private String inspectionUnitId;


    /**
     * 检查单位name
     */
    @Excel(name = "检查单位")
    @ApiModelProperty(value = "检查单位name")
    private String inspectionUnitName;


    @Pattern(regexp = "^[^\\s,](?!.*[\\s]).*$", message = "inspectionUserIds数据不规范！example： '1,2,3' ")
    @ApiModelProperty(value = "检查人ids", example = "1,2,3")
    private String inspectionUserIds;

    @Excel(name = "检查人")
    @ApiModelProperty(value = "检查人names", example = "张三,李四,王五")
    private String inspectionUserNames;

    /**
     * 受检单位id
     */
    @ApiModelProperty(value = "受检单位id")
    private String inspectedUnitId;

    /**
     * 受检单位name
     */
    @Excel(name = "受检单位")
    @ApiModelProperty(value = "受检单位name")
    private String inspectedUnitName;

    /**
     * 检查日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检查日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "检查日期")
    private LocalDate inspectionTime;


    /** 检查开始日期 */
    @TableField(exist = false)
    @ApiModelProperty(value = "检查开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date inspectionStartTime;

    /** 检查结束日期 */
    @TableField(exist = false)
    @ApiModelProperty(value = "检查结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date inspectionEndTime;


    /**
     * 检查问题责任人（督察人）id
     */
    @ApiModelProperty(value = "检查问题责任人（督察人）id")
    private String supervisorId;

    /**
     * 检查问题责任人（督察人）name
     */
    @Excel(name = "检查问题责任人")
    @ApiModelProperty(value = "检查问题责任人（督察人）name")
    private String supervisorName;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "接收时间")
    private Date receiveTime;

    /**
     * 合并后的检查内容
     */
    @Excel(name = "检查内容")
    @ApiModelProperty(value = "合并后的检查内容")
    private String contents;


    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 合并后整改要求及建议
     */
    @Excel(name = "整改要求及建议")
    @ApiModelProperty(value = "合并后整改要求及建议")
    private String suggestions;

    /**
     * 状态(暂存、待下发、待接收、已接收)
     */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态(待下发、待接收、已接收、已完成)")
    private String status;


    /** 状态list */
    @TableField(exist = false)
    @ApiModelProperty(value = "状态多选查询条件")
    List<String> statusList;


    /***/
    @TableField(exist = false)
    @ApiModelProperty(value = "rectifierIdOrSupervisorId")
    String rectifierIdOrSupervisorId;


    @ApiModelProperty(value = "userId")
    private String userId;


    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 整改责任人id
     */
    @ApiModelProperty(value = "整改责任人id")
    private String rectifierId;

    /**
     * 整改责任人name
     */
    @Excel(name = "整改责任人")
    @ApiModelProperty(value = "整改责任人name")
    private String rectifierName;

    @TableField(exist = false)
    private  List<String> ids;


    /** 隧道巡检查详情 */
    @TableField(exist = false)
    @ApiModelProperty(value = "详情list")
    List<SuperviseInspectionDetail> superviseInspectionDetailList;
}
