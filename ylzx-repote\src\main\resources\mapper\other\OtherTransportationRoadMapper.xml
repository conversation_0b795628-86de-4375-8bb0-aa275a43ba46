<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.other.mapper.OtherTransportationRoadMapper">

    <resultMap type="com.ruoyi.other.domain.OtherTransportationRoad" id="OtherTransportationRoadResult">
        <result property="id" column="id"/>
        <result property="transportationId" column="transportation_id"/>
        <result property="roadId" column="road_id"/>
        <result property="roadName" column="road_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">
        id, transportation_id, road_id, road_name,  create_by, create_time, update_by, update_time    </sql>

    <sql id="where_column">
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="transportation_id != null and transportation_id != ''">
            AND transportation_id = #{transportation_id}
        </if>
        <if test="roadId != null and roadId != ''">
            AND roadId = #{roadId}
        </if>

    </sql>

    <sql id="set_column">
        <if test="transportationId != null">
            transportation_id = #{transportationId},
        </if>
        <if test="roadId != null">
            road_id = #{roadId},
        </if>
        <if test="roadName != null">
            road_name = #{roadName},
        </if>
    </sql>

    <!-- 根据 transportationId 更新部门信息 -->
    <update id="updateRoadByTransportationId">
        UPDATE other_transportation_dept
        SET
            road_id = #{roadId},
            road_name = #{raodName}
        WHERE
            transportation_id = #{transportationId}
    </update>
    <delete id="deleteByTransportationIdAndRoadId">
        DELETE FROM other_transportation_road
        WHERE transportation_id = #{transportationId}
    </delete>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="OtherTransportationRoadResult">
        SELECT
        <include refid="base_column"/>
        FROM other_transportation_road
        <where>
            <include refid="where_column"/>
        </where>
    </select>



    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="OtherTransportationRoadResult">
        SELECT
        <include refid="base_column"/>
        FROM other_transportation_road
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>
    <select id="getRoadIdsByTransportationId" resultType="java.lang.String">
        SELECT
            road_id
        FROM
            other_transportation_road
        WHERE
            transportation_id = #{id}
    </select>


</mapper>