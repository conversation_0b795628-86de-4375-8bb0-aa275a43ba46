package com.ruoyi.repote.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.entity.BaseModelEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;

/**
 * 填报单位对象 repote_dept
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@ApiModel(value="填报单位")
@TableName("repote_dept")
@Data
public class RepoteDept extends BaseModelEntity<RepoteDept> {
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    @Excel(name = "任务ID")
    @ApiModelProperty(value = "任务ID")
    private String missionId;

    /** 部门名称名称 */
    @Excel(name = "部门名称名称")
    @ApiModelProperty(value = "部门名称名称")
    private String deptName;

}
