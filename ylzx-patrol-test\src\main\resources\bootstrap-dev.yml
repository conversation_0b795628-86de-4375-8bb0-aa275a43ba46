# Tomcat
server:
  port: 9301
#url: http://192.168.10.99:9301
# Spring
spring:
  application:
    # 应用名称
    name: ylzx-patrol-test
  profiles:
    # 环境配置
    active: dev
  cloud:
    inetutils:
      preferred-networks:
        - 172.18.0
        - 192.168.10
#      ignored-interfaces:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 192.168.101.121:8848
      config:
        # 配置中心地址
        server-addr: 192.168.101.121:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - common-redis.yml
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}