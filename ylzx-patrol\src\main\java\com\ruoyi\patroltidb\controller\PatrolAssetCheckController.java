package com.ruoyi.patroltidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.context.SecurityContextHolder;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.request.BatchAuditRequest;
import com.ruoyi.patrol.domain.vo.BridgeDetailsVO;
import com.ruoyi.patrol.domain.vo.CulvertDetailsVO;
import com.ruoyi.patrol.domain.vo.TunnelDetailsVO;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.enums.AuditStatusType;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.service.BaseCacheService;
import com.ruoyi.patrol.domain.vo.BridgeTimesVO;
import com.ruoyi.patrol.domain.vo.CulvertTimesVO;
import com.ruoyi.patrol.domain.vo.TunnelTimesVO;
import com.ruoyi.patrol.service.handler.impl.*;
import com.ruoyi.patrol.utils.AssetConvertUtils;
import com.ruoyi.patrol.utils.BeanConvertUtils;
import com.ruoyi.patrol.utils.StackUtils;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheck;
import com.ruoyi.patroltidb.domain.PatrolCulvertCheck;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheck;
import com.ruoyi.patroltidb.service.PatrolAssetCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.patroltidb.service.PatrolAssetCheckStatisticsService;
import com.ruoyi.patroltidb.service.PatrolAssetTimesService;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import cn.idev.excel.EasyExcel;

import static com.ruoyi.common.security.utils.SecurityUtils.getLoginUser;

/**
 * 资产巡检查主表Controller
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Api(tags = "资产巡检查主表")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/assetCheck")
public class PatrolAssetCheckController extends BaseController {
    final private PatrolAssetCheckService patrolAssetCheckService;
    final private PatrolAssetCheckDetailService checkDetailService;
    final private BaseCacheService baseCacheService;
    final private PatrolAssetTimesService patrolAssetTimesService;
    final private PatrolAssetCheckStatisticsService patrolAssetCheckStatisticsService;
    final private RemoteUserService remoteUserService;
    final private RemoteDeptAuthService remoteDeptAuthService;


    /**
     * 查询资产巡检查主表列表(分页)
     */
    @ApiOperation("查询资产寻检查主表列表")
    //@RequiresPermissions("patrol:assetCheck:list")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody PatrolAssetCheck patrolAssetCheck) {
        startPage();
        List<PatrolAssetCheck> list = patrolAssetCheckService.list(patrolAssetCheck);
        return getDataTable(list);
    }

    /**
     * 查询资产巡检查主表列表(不分页)
     */
    @ApiOperation("查询资产巡检查主表列表(不分页)")
    //@RequiresPermissions("patrol:assetCheck:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolAssetCheck> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolAssetCheck> list = patrolAssetCheckService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询资产巡检查主表数据
     */
    @ApiOperation("根据id查询资产巡检查主表数据")
    //@RequiresPermissions("patrol:assetCheck:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        PatrolAssetCheck patrolAssetCheck = patrolAssetCheckService.getById(id);
        if (patrolAssetCheck == null) {
            return error("未查询到【资产巡检查主表】记录");
        }
        return success(patrolAssetCheck);
    }

    /**
     * 新增资产巡检查主表
     */
    @ApiOperation("新增资产巡检查主表")
    //@RequiresPermissions("patrol:assetCheck:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolAssetCheck patrolAssetCheck) {
        patrolAssetCheckService.save(patrolAssetCheck);
        List<PatrolAssetCheckDetail> details = patrolAssetCheck.getPatrolCheckDetailList();
        if (details.size() > 0) {
            for (PatrolAssetCheckDetail detail : details) {
                detail.setCheckId(patrolAssetCheck.getId());
            }
            checkDetailService.saveBatch(details);
        }
        return toAjax(true);
    }

    /**
     * 修改资产巡检查主表
     */
    @ApiOperation("修改资产巡检查主表")
    //@RequiresPermissions("patrol:assetCheck:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolAssetCheck patrolAssetCheck) {
        return toAjax(patrolAssetCheckService.updateById(patrolAssetCheck));
    }

    /**
     * 删除资产巡检查主表
     */
    @ApiOperation("删除资产巡检查主表")
    //@RequiresPermissions("patrol:assetCheck:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolAssetCheckService.removeById(id));
    }

    /**
     * 根据条件获取数量
     */
    @ApiOperation("根据条件获取数量")
    //@RequiresPermissions("patrol:assetCheck:getTotalCount")
    @PostMapping("/getAssetTotalCount")
    public AjaxResult getAssetTotalCount(@RequestBody AssetBaseDataRequest assetBaseDataRequest) {
        baseCacheService.setDeptIds(assetBaseDataRequest);
        return success(patrolAssetCheckService.countAssetCheckData(assetBaseDataRequest));
    }

    /**
     * 获取本年度的巡查记录次数统计
     */
    @ApiOperation("获取本年度的巡查记录次数统计")
    //@RequiresPermissions("patrol:assetCheck:getCheckCount")
    @PostMapping("/getCheckTimes")
    public AjaxResult getCheckCount(@RequestBody AssetBaseDataRequest assetBaseDataRequest) {
        baseCacheService.setDeptIds(assetBaseDataRequest);
        return success(
                patrolAssetTimesService.getCurrentTimesVO(
                        assetBaseDataRequest, null, null, new AtomicInteger()));
    }

    /**
     * 获取本年度的巡查记录次数统计
     */
    @ApiOperation("获取本年度的巡查记录次数统计 (分页)")
    //@RequiresPermissions("patrol:assetCheck:getCheckCount")
    @PostMapping("/getCheckTimesPage")
    public TableDataInfo getCheckTimesPage(@RequestBody AssetBaseDataRequest assetBaseDataRequest,
                                           @RequestParam Long pageNum,
               @RequestParam Long pageSize) {
        AtomicInteger total = new AtomicInteger();
        TableDataInfo tableDataInfo = getDataTable(
                patrolAssetTimesService.getCurrentTimesVO(assetBaseDataRequest, pageNum, pageSize, total));
        tableDataInfo.setTotal(total.get());
        tableDataInfo.setPageNum(pageNum);
        tableDataInfo.setPageSize(pageSize);
        return tableDataInfo;
    }
    
    /**
     * 导出本年度的巡查记录次数统计
     */
    @ApiOperation("导出本年度的巡查记录次数统计")
    //@RequiresPermissions("patrol:assetCheck:exportCheckTimes")
    @Log(title = "巡查记录次数统计", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCheckTimes")
    public void exportCheckTimes(@RequestBody AssetBaseDataRequest request, HttpServletResponse response) {
        AtomicInteger total = new AtomicInteger();
        baseCacheService.setDeptIds(request);
        List<?> dataList = patrolAssetTimesService.getCurrentTimesVO(request, null, null, total);

        // 根据资产类型选择对应的导出实体类
        AssetType assetType = null;
        if (request.getType() != null) {
            assetType = request.getType().getAssetType();
        }
        if (assetType == null) {
            throw new IllegalArgumentException("assetType不能为空");
        }

        switch (assetType) {
            case BRIDGE -> {
                List<BridgeTimesVO> bridgeTimes = BeanConvertUtils.convertList(dataList, BridgeTimesVO.class);
                ExcelUtil<BridgeTimesVO> util = new ExcelUtil<>(BridgeTimesVO.class);
                util.exportExcel(response, bridgeTimes, "桥梁巡查次数统计");
            }
            case TUNNEL -> {
                List<TunnelTimesVO> tunnelTimes = BeanConvertUtils.convertList(dataList, TunnelTimesVO.class);
                ExcelUtil<TunnelTimesVO> util = new ExcelUtil<>(TunnelTimesVO.class);
                util.exportExcel(response, tunnelTimes, "隧道巡查次数统计");
            }
            case CULVERT -> {
                List<CulvertTimesVO> culvertTimes = BeanConvertUtils.convertList(dataList, CulvertTimesVO.class);
                ExcelUtil<CulvertTimesVO> util = new ExcelUtil<>(CulvertTimesVO.class);
                util.exportExcel(response, culvertTimes, "涵洞巡查次数统计");
            }
            default -> {
                ExcelUtil<Object> util = new ExcelUtil<>(Object.class);
                List<Object> objects = (List<Object>) dataList;
                util.exportExcel(response, objects, "巡查次数统计数据");
            }
        }
    }

    /**
     * 获取年度统计数据
     */
    @ApiOperation("获取年度统计数据")
    @PostMapping("/getYearlyStatistics")
    public AjaxResult getYearlyStatistics(@RequestBody AssetBaseDataRequest assetBaseDataRequest) {
        return success(patrolAssetCheckStatisticsService.getYearlyStatistics(assetBaseDataRequest));
    }

    /**
     * 导出资产巡检查主表列表
     */
    @ApiOperation("导出资产巡检查主表列表")
    //@RequiresPermissions("patrol:assetCheck:export")
    @Log(title = "资产巡检查主表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody AssetBaseDataRequest request, HttpServletResponse response) {
        AtomicInteger total = new AtomicInteger();
        baseCacheService.setDeptIds(request);
        List<PatrolAssetCheck> dataList = patrolAssetCheckService.selectPatrolAssetCheck(request, null, null, total);

        // 根据资产类型选择对应的导出实体类
        AssetType assetType = request.getAssetType();
        if (assetType == null && request.getType() != null) {
            assetType = request.getType().getAssetType();
        }
        if (assetType == null) {
            throw new IllegalArgumentException("assetType不能为空");
        }

        switch (assetType) {
            case BRIDGE -> {
                List<PatrolBridgeCheck> bridgeChecks = BeanConvertUtils.convertList(dataList, PatrolBridgeCheck.class);
                ExcelUtil<PatrolBridgeCheck> util = new ExcelUtil<>(PatrolBridgeCheck.class);
                util.exportExcel(response, bridgeChecks, "桥梁巡检数据");
            }
            case TUNNEL -> {
                List<PatrolTunnelCheck> tunnelChecks = BeanConvertUtils.convertList(dataList, PatrolTunnelCheck.class);
                ExcelUtil<PatrolTunnelCheck> util = new ExcelUtil<>(PatrolTunnelCheck.class);
                util.exportExcel(response, tunnelChecks, "隧道巡检数据");
            }
            case CULVERT -> {
                List<PatrolCulvertCheck> culvertChecks = BeanConvertUtils.convertList(dataList, PatrolCulvertCheck.class);
                ExcelUtil<PatrolCulvertCheck> util = new ExcelUtil<>(PatrolCulvertCheck.class);
                util.exportExcel(response, culvertChecks, "涵洞巡检数据");
            }
            default -> {
                ExcelUtil<PatrolAssetCheck> util = new ExcelUtil<>(PatrolAssetCheck.class);
                util.exportExcel(response, dataList, "资产巡检查主表数据");
            }
        }
    }

    @ApiOperation("设置生成巡查日志记录")
    @PostMapping("/setGenerateLog")
    public AjaxResult setGenerateCheckByMonth(@RequestBody AssetBaseDataRequest assetBaseDataRequest) {
        if (assetBaseDataRequest.getType() == null) {
            return error("type不能为空");
        }
        if (assetBaseDataRequest.getNowDateList() == null || assetBaseDataRequest.getNowDateList().isEmpty()) {
            return error("nowDateList不能为空");
        }
        LoginUser loginUser = getLoginUser();
        if (SecurityUtils.isAdmin(loginUser.getUserid())) {
            assetBaseDataRequest.setDataRule(false);
        }
        if (assetBaseDataRequest.getDataRule() == null) {
            assetBaseDataRequest.setDataRule(true);
        }

        return success(patrolAssetCheckService.generateLogByCondition(assetBaseDataRequest, assetBaseDataRequest.getNowDateList()));
    }

    @ApiOperation("设置生成日常巡查记录")
    @PostMapping("/setGenerateCheck")
    public AjaxResult setGenerateCheck(@RequestBody AssetBaseDataRequest assetBaseDataRequest) {
//        if (assetBaseDataRequest.getType() == null) {
//            return error("type不能为空");
//        }
//        if (assetBaseDataRequest.getNowDateList() == null || assetBaseDataRequest.getNowDateList().isEmpty()) {
//            return error("nowDateList不能为空");
//        }
        LoginUser loginUser = getLoginUser();
        assetBaseDataRequest.setRoleUserId(loginUser.getUserid());
        assetBaseDataRequest.setUserName(loginUser.getUsername());
        if (SecurityUtils.isAdmin(loginUser.getUserid())) {
            assetBaseDataRequest.setRoleUserId(null);
            assetBaseDataRequest.setDataRule(false);
        }
        if (assetBaseDataRequest.getDataRule() == null) {
            assetBaseDataRequest.setDataRule(true);
        }
        return success((Object) patrolAssetCheckService.generateCheckByCondition(assetBaseDataRequest));
    }

    /**
     * 传入processId 是uuid查询生成的巡查记录
     *
     * @return Map<String, Object>
     */
    @ApiOperation("获取正在生成的巡查记录")
    @PostMapping("/getGeneratingCheck")
    public AjaxResult getGeneratingCheck(@RequestBody Map<String, String> params) {
        String processId = params.get("processId");
        if (StringUtils.isEmpty(processId)) {
            return error("processId不能为空");
        }
        return success(patrolAssetCheckService.getProgress(processId));
    }

    @ApiOperation("生成桥梁经常检查记录")
    @RequestMapping(value = "/generateBridgeCheck", method = RequestMethod.GET)
    public void generateBridgeCheck(@RequestParam(required = true) YearMonth yearMonth) {
        if (yearMonth == null) {
            yearMonth = YearMonth.now();
        }
        patrolAssetCheckService.scheduleGenerateCheck(yearMonth, AssetType.BRIDGE);
    }

    @ApiOperation("生成隧道经常检查记录")
    @RequestMapping(value = "/generateTunnelCheck", method = RequestMethod.GET)
    public void generateTunnelCheck(@RequestParam(required = true) YearMonth yearMonth) {
        if (yearMonth == null) {
            yearMonth = YearMonth.now();
        }
        patrolAssetCheckService.scheduleGenerateCheck(yearMonth, AssetType.TUNNEL);
    }

    @ApiOperation("生成涵洞经常检查记录")
    @RequestMapping(value = "/generateCulvertCheck", method = RequestMethod.GET)
    public void generateCulvertCheck(@RequestParam(required = false) YearMonth yearMonth) {
        if(yearMonth == null){
            yearMonth = YearMonth.now();
        }
        patrolAssetCheckService.scheduleGenerateCheck(yearMonth, AssetType.CULVERT);
    }

    @ApiOperation("生成当前用户的日常巡查记录")
    @PostMapping("/generateCheck")
    //@RequiresPermissions("patrol:assetCheck:generateCheck")
    public void generateCheck() {
        LocalDate nowDate = LocalDate.now();
        LoginUser loginUser = getLoginUser();
        success(loginUser);
//        patrolAssetCheckService.scheduleGenerateCheck(nowDate, AssetType.TUNNEL);
    }

    @ApiOperation("最近距离分页查询")
    @PostMapping("/listByDistance")
    //@RequiresPermissions("patrol:assetCheck:listByDistance")
    public TableDataInfo listByDistance(@RequestBody AssetBaseDataRequest assetBaseDataRequest,
                                        @RequestParam(required = false) Long pageNum,
                                        @RequestParam(required = false) Long pageSize) {
        AtomicInteger total = new AtomicInteger();
        LoginUser loginUser = getLoginUser();
        if (assetBaseDataRequest.getDataRule() == null) {
            assetBaseDataRequest.setDataRule(true);
        }
        if (!assetBaseDataRequest.getDataRule() || SecurityUtils.isAdmin(loginUser.getUserid())) {
            assetBaseDataRequest.setDataRule(false);
        }
        TableDataInfo tableDataInfo = getDataTable(baseCacheService.listBy(assetBaseDataRequest, pageNum, pageSize, total));
        tableDataInfo.setTotal(total.get());
        tableDataInfo.setPageNum(pageNum);
        tableDataInfo.setPageSize(pageSize);
        return tableDataInfo;
    }

    @ApiOperation("积木报表分页查询")
    @PostMapping("/listByJm")
    //@RequiresPermissions("patrol:assetCheck:listByJm")
    public TableDataInfo listByJm(@RequestBody AssetBaseDataRequest assetBaseDataRequest,
            @RequestParam(name = "pageNum", required = false) Long pageNum,
            @RequestParam(name = "pageSize", required = false) Long pageSize) {
        AtomicInteger total = new AtomicInteger();
        List<?> list = baseCacheService.extractBaseData(baseCacheService.listBy(assetBaseDataRequest, pageNum, pageSize, total));
        TableDataInfo tableDataInfo = getDataTable(list);
        tableDataInfo.setTotal(total.get());
        tableDataInfo.setPageNum(pageNum);
        tableDataInfo.setPageSize(pageSize);
        return tableDataInfo;
    }

    @ApiOperation("根据条件获取数量")
    @PostMapping("/getTotalCount")
    public AjaxResult getTotalCount(@RequestBody
                                    AssetBaseDataRequest assetBaseDataRequest) {
        LoginUser loginUser = getLoginUser();
        if (SecurityUtils.isAdmin(loginUser.getUserid())) {
            assetBaseDataRequest.setDataRule(false);
        }
        if (assetBaseDataRequest.getDataRule() == null) {
            assetBaseDataRequest.setDataRule(true);
        }
        baseCacheService.setDeptIds(assetBaseDataRequest);
        int total = baseCacheService.getTotalCount(assetBaseDataRequest);
        return success(total);
    }

    @ApiOperation("查询日期")
    @GetMapping("/getMergedDays")
    public AjaxResult getMergedDays(@RequestParam String assetId, @RequestParam String yearMonth, @RequestParam String type) {
        InspectionType inspectionType = InspectionType.fromCode(type);
        yearMonth = yearMonth.trim();
        List<Integer> list = patrolAssetCheckService.getMergedDays(inspectionType, assetId, yearMonth);
        return success(list);
    }


    @ApiOperation("获取缓存")
    @GetMapping("/getCache")
    public AjaxResult getCache(@RequestParam(required = false) Integer assetType) {
        AssetType assetTypeEnum = AssetType.fromCode(assetType);
        baseCacheService.selectBaseType(assetTypeEnum);
        return success();
    }

    @ApiOperation("获取桥梁基础数据")
    @GetMapping("/getBridgeBaseData")
    public AjaxResult getBridgeBaseData() {
        baseCacheService.selectBaseType(AssetType.BRIDGE);
        return success();
    }

    @ApiOperation("获取隧道基础数据")
    @GetMapping("/getTunnelBaseData")
    public AjaxResult getTunnelBaseData() {
        baseCacheService.selectBaseType(AssetType.TUNNEL);
        return success();
    }

    @ApiOperation("获取涵洞基础数据")
    @GetMapping("/getCulvertBaseData")
    public AjaxResult getCulvertBaseData() {
        baseCacheService.selectBaseType(AssetType.CULVERT);
        return success();
    }

    @ApiOperation("条件查询列表")
    @PostMapping("/selectAssetCheckData")
    public TableDataInfo selectAssetCheckData(@RequestBody AssetBaseDataRequest request,
                                              @RequestParam Long pageNum,
                                              @RequestParam Long pageSize) {
        AtomicInteger total = new AtomicInteger();
        baseCacheService.setDeptIds(request);
        TableDataInfo tableDataInfo = getDataTable(patrolAssetCheckService.selectPatrolAssetCheck(request, pageNum, pageSize, total));
        tableDataInfo.setTotal(total.get());
        tableDataInfo.setPageNum(pageNum);
        tableDataInfo.setPageSize(pageSize);
        return tableDataInfo;
    }

    @ApiOperation("积木报表条件查询列表")
    @GetMapping("/checkDataByJm")
    public JSONObject selectAssetCheckDataByJm(
            @RequestParam(name = "type", defaultValue = "1") String type,
            @RequestParam(name = "cid", required = false) String cid,
            @RequestParam(name = "cids", required = false) String cids,
            @RequestParam(name = "code", required = false) String code,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "sTime", required = false) String sTime,
            @RequestParam(name = "eTime", required = false) String eTime,
            @RequestParam(name = "sectionId", required = false) String sectionId,
            @RequestParam(name = "routeCodeStr", required = false) String routeCodeStr,
            @RequestParam(name = "manageIdStr", required = false) String manageIdStr,
            @RequestParam(name = "status", required = false) String status,
            @RequestParam(name = "pageNo", defaultValue = "1") Long pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10000") Long pageSize,
            HttpServletRequest response) {
        LoginUser loginUser = (LoginUser) SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        Long userId = null;
        if (loginUser == null) {
            String userIdHeader = response.getHeader("userid");
            if (StringUtils.isNotBlank(userIdHeader)) {
                try {
                    userId = Long.valueOf(userIdHeader);
                    SysUser sysUser = remoteUserService.findByUserId(userId).getData();
                    if (sysUser != null) {
                        loginUser = remoteUserService.getUserInfo(sysUser.getUserName(), SecurityConstants.INNER).getData();
                        SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的用户ID头部值: {}", userIdHeader);
                }
            }
        } else {
            userId = loginUser.getUserid();
        }

        // 如果还是没有获取到用户ID，使用默认值
        if (userId == null) {
            userId = 1L;
        }

        AtomicInteger total = new AtomicInteger();
        AssetBaseDataRequest.AssetBaseDataRequestBuilder requestBuilder = AssetBaseDataRequest.builder();
        requestBuilder.type(InspectionType.fromCode(type));
        requestBuilder.roleUserId(userId);
        requestBuilder.dataRule(true);
        requestBuilder.assetCode(code);
        requestBuilder.assetName(name);
        if (!StringUtils.isBlank(sTime)) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                requestBuilder.checkStartTime(LocalDateTime.parse(sTime, formatter));
            } catch (DateTimeParseException e) {
                log.warn("日期格式无效: {}，将使用当前时间代替", sTime);
                // 继续使用当前时间作为默认值
            }
        }
        if (!StringUtils.isBlank(eTime)) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                requestBuilder.checkEndTime(LocalDateTime.parse(eTime, formatter));
            } catch (DateTimeParseException e) {
                log.warn("日期格式无效: {}，将使用当前时间代替", eTime);
                // 继续使用当前时间作为默认值
            }
        }

        requestBuilder.maintenanceSectionId(sectionId);
        // 把routeCodes按逗号分隔
        if(routeCodeStr!=null && !StringUtils.isBlank(routeCodeStr)) {
            Set<String> routeCodes = Stream.of(routeCodeStr.split(","))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            // 如果routeCodes为空，设置为null
            if (routeCodes.isEmpty()) {
                routeCodes = null;
            }
            requestBuilder.routeCodes(routeCodes);
        }
        // 把managementMaintenanceIdStr按逗号分隔
        if(manageIdStr!=null && !StringUtils.isBlank(manageIdStr)) {
            Set<String> managementMaintenanceIds = Stream.of(manageIdStr.split(","))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            // 如果managementMaintenanceIds为空，设置为null
            if (managementMaintenanceIds.isEmpty()) {
                managementMaintenanceIds = null;
            }
            requestBuilder.managementMaintenanceIds(managementMaintenanceIds);
        }
        if (SecurityUtils.isAdmin(userId)) {
            requestBuilder.dataRule(false);
            requestBuilder.roleUserId(null);
        }
        if (StringUtils.isNotBlank(status)) {
            requestBuilder.status(AuditStatusType.fromCode(Integer.valueOf(status)));
        }
        if (StringUtils.isNotBlank(cid)) {
            requestBuilder.checkId(cid);
        }
        if (StringUtils.isNotBlank(cids)) {
            requestBuilder.checkIds(Stream.of(cids.split(","))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet()));
        }
        AssetBaseDataRequest request = requestBuilder.build();
        JSONObject object = new JSONObject();
        List<PatrolAssetCheck> patrolAssetChecks = patrolAssetCheckService.selectPatrolAssetCheck(request, pageNo, pageSize, total);
        patrolAssetCheckService.setSignUrlBatch(patrolAssetChecks);
        object.put("data", patrolAssetChecks);
        object.put("total", (total.get() + pageSize - 1) / pageSize);
        object.put("count", patrolAssetChecks.size());
        return object;
    }

    @ApiOperation("条件查询统计详情列表")
    @PostMapping("/selectAssetDetail")
    public TableDataInfo selectAssetDetail(@RequestBody AssetBaseDataRequest request,
                                           @RequestParam Long pageNum,
                                           @RequestParam Long pageSize) {
        AtomicInteger total = new AtomicInteger();
        baseCacheService.setDeptIds(request);
        List<PatrolAssetCheck> patrolAssetChecks = patrolAssetCheckService.selectPatrolAssetCheck(request, pageNum, pageSize, total);
        AssetType assetType = request.getAssetType();
        TableDataInfo tableDataInfo;
        switch (assetType) {
            case BRIDGE -> {
                tableDataInfo = getDataTable(AssetConvertUtils.convertList(patrolAssetChecks, BridgeDetailsVO.class));
            }
            case TUNNEL -> {
                tableDataInfo = getDataTable(AssetConvertUtils.convertList(patrolAssetChecks, TunnelDetailsVO.class));
            }
            case CULVERT -> {
                tableDataInfo = getDataTable(AssetConvertUtils.convertList(patrolAssetChecks, CulvertDetailsVO.class));
            }
            default -> {
                tableDataInfo = getDataTable(patrolAssetChecks);
            }
        }
        tableDataInfo.setTotal(total.get());
        tableDataInfo.setPageNum(pageNum);
        tableDataInfo.setPageSize(pageSize);
        return tableDataInfo;
    }

    /**
     * 导出资产巡检查主表列表
     */
    @ApiOperation("导出资产巡检查主表列表")
    //@RequiresPermissions("patrol:assetCheck:export")
    @Log(title = "资产巡检查主表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDetail")
    public void exportDetails(@RequestBody AssetBaseDataRequest request, HttpServletResponse response) {
        AtomicInteger total = new AtomicInteger();
        baseCacheService.setDeptIds(request);
        List<PatrolAssetCheck> dataList = patrolAssetCheckService.selectPatrolAssetCheck(request, null, null, total);

        // 根据资产类型选择对应的导出实体类
        AssetType assetType = request.getAssetType();
        if (assetType == null && request.getType() != null) {
            assetType = request.getType().getAssetType();
        }
        if (assetType == null) {
            throw new IllegalArgumentException("assetType不能为空");
        }

        switch (assetType) {
            case BRIDGE -> {
                List<BridgeDetailsVO> bridgeChecks = BeanConvertUtils.convertList(dataList, BridgeDetailsVO.class);
                ExcelUtil<BridgeDetailsVO> util = new ExcelUtil<>(BridgeDetailsVO.class);
                util.exportExcel(response, bridgeChecks, "桥梁巡检统计详情");
            }
            case TUNNEL -> {
                List<TunnelDetailsVO> tunnelChecks = BeanConvertUtils.convertList(dataList, TunnelDetailsVO.class);
                ExcelUtil<TunnelDetailsVO> util = new ExcelUtil<>(TunnelDetailsVO.class);
                util.exportExcel(response, tunnelChecks, "隧道巡检数据统计详情");
            }
            case CULVERT -> {
                List<CulvertDetailsVO> culvertChecks = BeanConvertUtils.convertList(dataList, CulvertDetailsVO.class);
                ExcelUtil<CulvertDetailsVO> util = new ExcelUtil<>(CulvertDetailsVO.class);
                util.exportExcel(response, culvertChecks, "涵洞巡检数据统计详情");
            }
            default -> {
                ExcelUtil<PatrolAssetCheck> util = new ExcelUtil<>(PatrolAssetCheck.class);
                util.exportExcel(response, dataList, "资产巡检查主表数据统计详情");
            }
        }
    }

    @ApiOperation("补充某个月得的经常检查数据")
    @GetMapping("/completeMonthData")
    public void completeMonthData(@RequestParam YearMonth yearMonth,@RequestParam String type) {
        InspectionType inspectionType = InspectionType.fromCode(type);
        if(inspectionType == null){
            throw new IllegalArgumentException("type不能为空或无效");
        }
        patrolAssetCheckService.completeDataByMonth(yearMonth,inspectionType);
    }

    @ApiOperation("补全老数据")
    @GetMapping("/completeOldData")
    public void completeOldData() {
        patrolAssetCheckService.completeData();
    }


    @ApiOperation("补全老数据的病害")
    @GetMapping("/completeOldDataDisease")
    public void completeOldDataDisease() {
        patrolAssetCheckService.completeDiseaseNum();
    }
    @ApiOperation("补全桥梁老数据的病害")
    @GetMapping("/completeBridgeDiseaseNum")
    public void completeBridgeDiseaseNum(){
        patrolAssetCheckService.completeBridgeDiseaseNum();
    }

    @ApiOperation("重新计算老数据的到期时间")
    @GetMapping("/completeOldDataExpiry")
    public void completeOldDataExpiry() {
        patrolAssetCheckService.recalculateExpiry();
    }

    @ApiOperation("重新修改管养单位，从隧管站修改为分处")
    @GetMapping("/updatePropertyUnitName")
    public void updatePropertyUnitName() {
        patrolAssetCheckService.updatePropertyUnitName();
    }

    @ApiOperation("重新计算新数据的到期时间")
    @GetMapping("/completeNewDataExpiry")
    public void completeNewDataExpiry() {
        patrolAssetCheckService.updateDiseaseCountByCreateTime();
    }

    /**
     * 导出桥梁经常检查记录表卡片
     */
    @ApiOperation("导出资产检查记录表卡片")
    @Log(title = "导出资产巡查检查记录表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAssetReportCard")
    public void exportAssetReportCard(@RequestBody AssetBaseDataRequest request, HttpServletResponse response) {
        try {
            Map<String, String> signUrlMap = new HashMap<>();
            InspectionType inspectionType = request.getType();
            if(inspectionType == null){
                throw new IllegalArgumentException("type不能为空或无效");
            }
            // 获取桥梁检查记录数据
            List<PatrolAssetCheck> reportData = patrolAssetCheckService.exportReportCard(request,signUrlMap, null, null);
            if (reportData == null || reportData.isEmpty()) {
                throw new RuntimeException("未找到符合条件的检查记录");
            }
            String fileName = null;
           if(request.getExportFileName()!=null) {
               fileName = URLEncoder.encode(request.getExportFileName(), StandardCharsets.UTF_8);
           }else{
               String inspectionTypeStr = inspectionType.getDescription() + "记录表";
               if (reportData.size() == 1) {
                   StringBuilder sb = new StringBuilder();
                   PatrolAssetCheck record = reportData.get(0);
                   
                   // 拼接文件名：assetName_routeCode_formattedCenterStake_inspectionTypeStr_checkTime
                   if (record.getAssetName() != null && !record.getAssetName().isEmpty()) {
                       sb.append(record.getAssetName());
                   }
                   
                   if (record.getRouteCode() != null && !record.getRouteCode().isEmpty()) {
                       if (!sb.isEmpty()) sb.append("_");
                       sb.append(record.getRouteCode());
                   }
                   
                   String formattedStake = null;
                   if (record.getCenterStake() != null) {
                       formattedStake = StackUtils.formatStack(record.getCenterStake());
                       if (!formattedStake.isEmpty()) {
                           if (!sb.isEmpty()) sb.append("_");
                           sb.append(formattedStake);
                       }
                   }

                   if (!sb.isEmpty()) sb.append("_");
                   sb.append(inspectionTypeStr);

                   if (record.getCheckTime() != null) {
                       if (!sb.isEmpty()) sb.append("_");
                       java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMddHHmmss");
                       sb.append(sdf.format(record.getCheckTime()));
                   }
                   
                   fileName = URLEncoder.encode(sb.toString(), StandardCharsets.UTF_8);
               } else {
                   StringBuilder sb = new StringBuilder();
                   sb.append(inspectionTypeStr);
                   sb.append("(");
                   sb.append(reportData.size());
                   sb.append("条_");
                   sb.append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                   sb.append(")");
                   fileName = URLEncoder.encode(sb.toString(), StandardCharsets.UTF_8);
               }
           }
           response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            // 使用EasyExcel导出
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                // 从查询结果中获取签名URL映射，已经在PatrolAssetCheckServiceImpl.exportReportCard中处理
                // 创建ExcelWriter
                switch (inspectionType) {
                    case BRIDGE_DAILY_INSPECTION -> {
                        EasyExcel.write(outputStream)
                            .registerWriteHandler(new BridgeDailyReportHandler(reportData, signUrlMap))
                            .sheet("桥梁日常检查记录表")
                            .doWrite(new ArrayList<>()); // 空列表，因为BridgeDailyReportHandler会处理数据绘制
                    }
                    case BRIDGE_REGULAR_INSPECTION -> {
                        EasyExcel.write(outputStream)
                            .registerWriteHandler(new BridgeRegularReportHandler(reportData, signUrlMap))
                            .sheet("桥梁经常检查记录表")
                            .doWrite(new ArrayList<>()); // 空列表，因为BridgeRegularReportHandler会处理数据绘制
                    }
                    case TUNNEL_REGULAR_INSPECTION -> {
                        EasyExcel.write(outputStream)
                            .registerWriteHandler(new TunnelRegularReportHandler(reportData, signUrlMap))
                            .sheet("隧道经常检查记录表")
                            .doWrite(new ArrayList<>()); // 空列表，因为TunnelRegularReportHandler会处理数据绘制
                    }
                    case TUNNEL_DAILY_INSPECTION -> {
                        EasyExcel.write(outputStream)
                            .registerWriteHandler(new TunnelDailyReportHandler(reportData, signUrlMap))
                            .sheet("隧道日常检查记录表")
                            .doWrite(new ArrayList<>()); // 空列表，因为TunnelDailyReportHandler会处理数据绘制
                    }
                    case CULVERT_REGULAR_INSPECTION -> {
                        EasyExcel.write(outputStream)
                            .registerWriteHandler(new CulvertRegularReportHandler(reportData, signUrlMap))
                            .sheet("涵洞经常检查记录表")
                            .doWrite(new ArrayList<>()); // 空列表，因为CulvertRegularReportHandler会处理数据绘制
                    }
                    default -> {
                        throw new IllegalArgumentException("不支持的检查类型: " + inspectionType);
                    }
                }
            } catch (Exception e) {
                log.error("导出巡检查检查记录表失败", e);
                throw new RuntimeException("导出失败: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("导出巡检查检查记录表失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    @ApiOperation("根据ID列表批量更新状态")
    @PostMapping("/updateStatusByIds")
    public AjaxResult updateStatusByIds(@RequestBody AssetBaseDataRequest request) {
        if (request == null || request.getCheckIds() == null || request.getCheckIds().isEmpty()) {
            return AjaxResult.error("参数错误：checkIds和status不能为空");
        }
        if (request.getAssetType() == null) {
            return AjaxResult.error("参数错误：assetType不能为空");
        }
        int rows = patrolAssetCheckService.updateStatusByIds(request);
        return toAjax(rows);
    }
    /**
     * 批量完成资产审核
     *
     * @param request 批量审核请求参数
     * @return 更新记录数
     */
    @PostMapping("/batchComplete")
    @Log(title = "批量审核", businessType = BusinessType.UPDATE)
    public Integer batchCompleteAudit(@RequestBody @Validated BatchAuditRequest request) {
        try {
            return patrolAssetCheckService.completeAuditInfo(request);
        } catch (Exception e) {
            log.error("批量审核失败", e);
            return 0;
        }
    }

    @ApiOperation("生成历史巡检数据")
    @PostMapping("/generateHistory")
    public AjaxResult generateHistory(@RequestBody AssetBaseDataRequest request) {
        patrolAssetCheckService.generateHistory(request);
        return AjaxResult.success("历史数据生成任务已启动");
    }
}
