package com.ruoyi.patrol.mapstruct;

import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseCulvertResponseCache;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * MapStruct映射器，用于将各类缓存对象转换为BaseDataDomain对象
 */
@Mapper
public interface CacheToBaseDomainMapper {
    CacheToBaseDomainMapper INSTANCE = Mappers.getMapper(CacheToBaseDomainMapper.class);

    /**
     * 将缓存对象转换为BaseDataDomain
     * @param baseDataCache 缓存对象
     * @return BaseDataDomain对象
     */
    BaseDataDomain toBaseDataDomain(BaseDataCache baseDataCache);
    
    /**
     * 桥梁基础数据缓存转为BaseDataDomain
     */
    BaseDataDomain bridgeToBaseData(BaseBridgeResponseCache baseBridgeResponseCache);

    /**
     * 涵洞基础数据缓存转为BaseDataDomain
     */
    BaseDataDomain culvertToBaseData(BaseCulvertResponseCache baseCulvertResponseCache);

    /**
     * 隧道基础数据缓存转为BaseDataDomain
     */
    BaseDataDomain tunnelToBaseData(BaseTunnelResponseCache baseTunnelResponseCache);
} 