package com.ruoyi.repote.service.impl;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repote.mapper.RepotePersonMapper;
import com.ruoyi.repote.domain.RepotePerson;
import com.ruoyi.repote.service.RepotePersonService;

/**
 * 填报人员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Service
public class RepotePersonServiceImpl extends ServiceImpl<RepotePersonMapper, RepotePerson> implements RepotePersonService {

    @Autowired
    private RepotePersonMapper repotePersonMapper;

    @Override
    public List<RepotePerson> findListByParam(Map<String, Object> params) {
        return repotePersonMapper.findListByParam(params);
    }


}
