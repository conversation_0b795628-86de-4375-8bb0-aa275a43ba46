package com.ruoyi.patrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patrol.domain.PatrolAssetDiseases;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产病害信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public interface PatrolAssetDiseasesMapper extends BaseMapper<PatrolAssetDiseases> {
    /**
     * 根据部件类型查询病害信息
     * @param partsType 部件类型
     * @return 病害信息
     */
    List<PatrolAssetDiseases> selectPatrolPartsInfoByPartsType(@Param("partsType") String partsType);
}
