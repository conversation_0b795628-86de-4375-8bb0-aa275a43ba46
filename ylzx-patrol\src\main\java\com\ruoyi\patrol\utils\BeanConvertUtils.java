package com.ruoyi.patrol.utils;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Function;
import org.springframework.beans.BeanUtils;

/**
 * Bean转换工具类
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
public class BeanConvertUtils {

    private static final ConcurrentHashMap<Class<?>, Constructor<?>> CONSTRUCTOR_CACHE = new ConcurrentHashMap<>();

    private static final int PARALLEL_THRESHOLD = 1000;
    private static final int BATCH_SIZE = 2000;
    private static final ForkJoinPool COMMON_POOL = ForkJoinPool.commonPool();

    /**
     * 将列表转换为指定类型
     * 优化点：
     * 1. 使用对象池复用实例
     * 2. 并行流提高性能
     * 3. 预分配容量
     * 4. 缓存构造器反射
     *
     * @param list  源列表
     * @param clazz 目标类型
     * @return 转换后的列表
     */
    public static <T> List<T> convertList(List<?> list, Class<T> clazz) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        Constructor<T> constructor = getConstructor(clazz);

        int size = list.size();

        if (size > PARALLEL_THRESHOLD) {
            return processBatchParallel(list, size, constructor);
        } else {
            return processSequential(list, size, constructor);
        }
    }

    private static <T> List<T> processBatchParallel(
            List<?> list, 
            int size, 
            Constructor<T> constructor) {
        int threads = Math.min(Runtime.getRuntime().availableProcessors(), (size + BATCH_SIZE - 1) / BATCH_SIZE);
        
        try {
            return COMMON_POOL.submit(() ->
                list.parallelStream()
                    .map(item -> convertSingle(item, constructor))
                    .collect(
                        ArrayList<T>::new,
                        ArrayList::add,
                        ArrayList::addAll)
            ).get();
        } catch (Exception e) {
            throw new IllegalStateException("并行处理失败", e);
        }
    }

    private static <T> List<T> processSequential(
            List<?> list, 
            int size, 
            Constructor<T> constructor) {
        List<T> result = new ArrayList<>(size);
        for (Object item : list) {
            result.add(convertSingle(item, constructor));
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    private static <T> Constructor<T> getConstructor(Class<T> clazz) {
        return (Constructor<T>) CONSTRUCTOR_CACHE.computeIfAbsent(clazz, k -> {
            try {
                Constructor<?> constructor = k.getDeclaredConstructor();
                constructor.setAccessible(true);
                return constructor;
            } catch (NoSuchMethodException e) {
                throw new IllegalArgumentException("无法获取默认构造器: " + k.getName(), e);
            }
        });
    }

    /**
     * 转换单个对象
     *
     * @param item        源对象
     * @param constructor 构造器
     * @return 转换后的对象
     */
    private static <T> T convertSingle(Object item, Constructor<T> constructor) {
        try {
            T target = constructor.newInstance();
            BeanUtils.copyProperties(item, target);
            return target;
        } catch (Exception e) {
            throw new IllegalStateException("对象转换失败: " + e.getMessage(), e);
        }
    }
}