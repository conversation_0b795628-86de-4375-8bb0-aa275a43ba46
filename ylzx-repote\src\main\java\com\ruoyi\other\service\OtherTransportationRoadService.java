package com.ruoyi.other.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.other.domain.OtherTransportationRoad;

import java.util.List;
import java.util.Map;


/**
 * 大件运输Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface OtherTransportationRoadService extends IService<OtherTransportationRoad> {

    /**
     * 根据条件查询大件运输列表
     * @param params
     */
    List<OtherTransportationRoad> findListByParam(Map<String, Object> params);
    /**
     * 根据条件查询大件运输列表
     * @String ID
     *
     */
    List<String> getRoadIdsByTransportationId(String id);

    boolean updateRoadByTransportationId(String transportationId, String deptId, String deptName);

    boolean removeByTransportationId(String transportationId);
}
