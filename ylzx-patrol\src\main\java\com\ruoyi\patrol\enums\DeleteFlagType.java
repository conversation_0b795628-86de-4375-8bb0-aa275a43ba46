package com.ruoyi.patrol.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 删除标志
 */
@Getter
@RequiredArgsConstructor
public enum DeleteFlagType implements IEnum<Integer> {
    NOT_DELETED(0, "未删除"),
    DELETED(1, "已删除");

    private final Integer code;
    private final String description;

    @Override
    public Integer getValue() {
        return this.code;
    }

    @JsonValue
    public Integer getCode() {
        return this.code;
    }
    @JsonCreator
    public static DeleteFlagType fromCode(Integer code) {
        for (DeleteFlagType type : DeleteFlagType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}