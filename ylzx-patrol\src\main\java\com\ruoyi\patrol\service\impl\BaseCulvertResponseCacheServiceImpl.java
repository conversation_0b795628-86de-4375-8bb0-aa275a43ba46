package com.ruoyi.patrol.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseCulvertResponseCache;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.mapper.BaseCulvertResponseCacheMapper;
import com.ruoyi.patrol.service.BaseCulvertResponseCacheService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * base_culvert_response(BaseCulvertResponseCache)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-21 11:33:54
 */
@Service("baseCulvertResponseCacheService")
@Master
public class BaseCulvertResponseCacheServiceImpl extends ServiceImpl<BaseCulvertResponseCacheMapper, BaseCulvertResponseCache>
        implements BaseCulvertResponseCacheService {

    /**
     * 清空表
     */
    @Transactional
    public void clearTable() {
        baseMapper.truncateTable();
    }

    /**
     * 批量新增数据
     */
    public void insertBatch(List<BaseCulvertResponseCache> entities) {
        baseMapper.insertBatch(entities);
    }

    /**
     * 获取request条件下的总数
     * @param request 请求参数
     * @return 总数
     */
    public int countAssetBaseData(AssetBaseDataRequest request){
        return baseMapper.countAssetBaseData(request);
    }

    /**
     * 获取request条件下的数据
     * @param request 请求参数
     * @return 数据
     */
    public List<BaseCulvertResponseCache> selectAssetBaseData(AssetBaseDataRequest request, Long pageNum, Long pageSize){
        return baseMapper.selectAssetBaseData(request,pageNum,pageSize);
    }

}

