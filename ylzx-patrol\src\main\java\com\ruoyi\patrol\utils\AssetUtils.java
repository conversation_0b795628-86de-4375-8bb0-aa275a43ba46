package com.ruoyi.patrol.utils;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月21日 9:50
 */


import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.patrol.enums.AssetType;

import java.util.Collection;

/**
 * 资产工具类
 */
public class AssetUtils {
    /**
     * 设置基础资产缓存
     */

    /**
     * 清空字典缓存
     */
    public static void clearDictCache(AssetType assetType) {
        Collection<String> keys = SpringUtils.getBean(RedisService.class).keys(CacheConstants.SYS_DICT_KEY + "*");
        SpringUtils.getBean(RedisService.class).deleteObject(keys);
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    public static String getCacheKey(AssetType assetType, String configKey)
    {
        return switch (assetType) {
            case BRIDGE -> "base_asset_bridge:" + configKey;
            case CULVERT -> "base_asset_culvert:" + configKey;
            case TUNNEL -> "base_asset_tunnel:" + configKey;
            case DEVICE -> "base_asset_device:" + configKey;
        };
    }
}
