package com.ruoyi.other.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OtherTransportationDeptDTO {
    private static final long serialVersionUID = 1L;

    /** 管理处ID */
    @Excel(name = "管理处ID")
    @ApiModelProperty(value = "管理处ID")
    private String deptId;

    /** 管理处名称 */
    @Excel(name = "管理处名称")
    @ApiModelProperty(value = "管理处名称")
    private String deptName;

}
