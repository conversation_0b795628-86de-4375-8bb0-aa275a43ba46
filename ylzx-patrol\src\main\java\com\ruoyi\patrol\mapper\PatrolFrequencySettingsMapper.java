package com.ruoyi.patrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patrol.domain.PatrolFrequencySettings;

import java.util.List;
import java.util.Map;

/**
 * 巡查频率配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface PatrolFrequencySettingsMapper extends BaseMapper<PatrolFrequencySettings> {

    List<PatrolFrequencySettings> findListByParam(Map<String, Object> params);

    Long saveOrUpdatePlus(PatrolFrequencySettings settings);


}
