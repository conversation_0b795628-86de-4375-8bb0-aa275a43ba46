server:
  port: 9204
spring:
  application:
    # 应用名称
    name: ylzx-repote
  profiles:
    # 环境配置
    active: prod
  cloud:
    inetutils:
      preferred-networks:
        - 172.18.0
        - 192.168.10
    #      ignored-interfaces:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 192.168.101.121:8848
        namespace: 24aaca1b-7113-4947-ba4e-5314fdc2011f
        username: nacos
        password: 'nacos@abc123!'
      config:
        # 配置中心地址
        server-addr: 192.168.101.121:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - common-redis.yml
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        namespace: 24aaca1b-7113-4947-ba4e-5314fdc2011f
        username: nacos
        password: 'nacos@abc123!'


dromara:
  x-file-storage:
    default-platform: minio
    thumbnail-suffix: ".min.jpg" #缩略图后缀，例如【.min.jpg】【.png】
    local-plus:
      - platform: local
        enable-storage: true
        enable-access: true #启用本地访问
        domain: /file/
        base-path: local-plus/ # 基础路径
        path-patterns: /file/** # 访问路径
        storage-path: E:/xxx2/ # 存储路径

---
# spring配置
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        # 主库数据源
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***************************************************************************************************************************************************************
          username: root
          password: 'Ylzx@2000+!#-2'
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            minimum-idle: 5
            maximum-pool-size: 20
            is-auto-commit: true
            idle-timeout: 30000
            max-lifetime: 1800000
            connection-timeout: 30000


# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  #configLocation: classpath:mybatis/mybatis-config.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    enable-sql-runner: true
# swagger配置
swagger:
  title: 智慧养护管理平台-大件运输
  license: Powered By ylzx
  licenseUrl: https://ruoyi.vip
knife4j:
  # 开启增强配置
  enable: true
  # 开启生产环境屏蔽
  production: false
  # basic:
  #   # 是否开启认证
  #   enable: true
  #   # 用户名
  #   username: root
  #   # 密码
  #   password: root