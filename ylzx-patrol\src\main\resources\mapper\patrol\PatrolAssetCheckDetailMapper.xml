<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.PatrolAssetCheckDetailMapper">

    <insert id="insertBatch" parameterType="java.util.List">
        <bind name="detailTableName" value="tableName + '_detail'" />
        INSERT INTO ${detailTableName} (
        id, check_id, parts_type_id, parts_type_name, defect, advice, image, del_flag, remark,
        create_time, update_time, create_by, update_by, des
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.checkId}, #{item.partsTypeId}, #{item.partsTypeName},
            #{item.defect}, #{item.advice}, #{item.image}, #{item.delFlag}, #{item.remark},
            #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy}, #{item.des}
            )
        </foreach>
    </insert>

    <select id="selectByCheckIds" resultType="com.ruoyi.patrol.domain.PatrolAssetCheckDetail">
        <bind name="detailTableName" value="tableName + '_detail'" />
        select * from ${detailTableName}
        where check_id in
        <foreach collection="list" item="checkId" open="(" separator="," close=")">
            #{checkId}
        </foreach>
    </select>

</mapper>