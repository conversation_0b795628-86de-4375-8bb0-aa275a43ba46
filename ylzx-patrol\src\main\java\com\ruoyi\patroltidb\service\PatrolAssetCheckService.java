package com.ruoyi.patroltidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolCheckBase;
import com.ruoyi.patrol.domain.PatrolInspectionLogs;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.domain.dto.BaseDataDomainWithDistance;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.request.BatchAuditRequest;
import com.ruoyi.patrol.domain.request.PatrolAssetCheckRequest;
import com.ruoyi.patrol.domain.vo.AssetCheckStatisticsVO;
import com.ruoyi.patrol.domain.vo.InspectionStatsVO;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.enums.InspectionType;
import org.apache.poi.ss.formula.functions.T;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 资产寻检查主表Service接口
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
public interface PatrolAssetCheckService extends IService<PatrolAssetCheck> {


    /**
     * 设置病害数
     * @param patrolAssetCheck 需要设置的巡查对象
     * @param <T> 巡查对象类型
     *
     */
    <T extends PatrolAssetCheck> T setDiseaseNum(T patrolAssetCheck);



    /**
     * 置巡查记录的签名URL
     * <p>
     * 处理流程:
     * 1. 并行处理操作员和负责人的签名
     * 2. 将签名ID转换为对应的URL
     * 3. 等待所有异步操作完成
     * <p>
     * 优化策略:
     * 1. 使用CompletableFuture实现并行处理提高性能
     * 2. 采用函数式编程处理签名URL
     *
     * @param patrolAssetCheck 需要设置签名URL的巡查记录
     * @return 设置完成的巡查记录
     * <p>
     * 示例:
     * Input: patrolAssetCheck.oprUserSign = "123,456"
     * Output: patrolAssetCheck.oprUserSignUrl = "http://example.com/123"
     */
    <T extends PatrolAssetCheck> T setSignUrl(T patrolAssetCheck);


    /**
     * 批量设置巡查记录的签名URL
     * <p>
     * 处理流程:
     * 1. 收集所有记录的签名ID并去重
     * 2. 批量获取签名URL
     * 3. 将URL设置回对应的记录
     * <p>
     * 优化策略:
     * 1. 批量获取URL减少网络请求
     * 2. 使用Map缓存URL结果
     * 3. 函数式处理简化代码
     *
     * @param patrolAssetCheckList 需要设置签名URL的巡查记录列表
     * @return 设置完成的巡查记录列表
     */
    <T extends PatrolAssetCheck> List<T> setSignUrlBatch(List<T> patrolAssetCheckList);

    /**
     * 设置格式化的桩号
     * @param patrolAssetCheck 巡查对象
     * @return 设置完成的巡查对象
     * @param <T> 巡查对象类型
     */
    <T extends PatrolAssetCheck> T setStack(T patrolAssetCheck);




    /**
     * 设置巡查对象的过期时间和频率
     * @param patrolAssetCheck 巡查对象
     * @return PatrolAssetCheck
     */
    <T extends PatrolCheckBase> T setExpiryAndFrequency(T patrolAssetCheck);

    /**
     * 获取当年经常检查的所有记录
     *
     * @param request 资产ID
     * @return 当年经常检查的所有记录
     */
    List<PatrolAssetCheck> getOftenCheckList(AssetBaseDataRequest request, Long pageNum, Long pageSize, AtomicInteger total);


    /**
     * 设置签名
     * @param patrolAssetCheck 巡查对象
     * @return PatrolAssetCheck
     */
    <T extends PatrolAssetCheck> T setSign(T patrolAssetCheck);


    /**
     * 资产检查基础信息,那个月的日常和经常检查的数量
     * <p>
     *     1. 获取日常检查数量
     *     2. 获取经常检查数量
     *     3. 返回日常和经常检查数量
     *     4. 优化策略: 使用CompletableFuture实现异步并行查询
     *     5. 优化策略: 使用从库查询减轻主库压力
     *     6. 优化策略: 分页/不分页采用不同的查询策略
     * @param request 请求参数
     * @return InspectionStatsVO
     */
    InspectionStatsVO assetCheckBaseInfo(AssetBaseDataRequest request);


    /**
     * 查询列表
     * @param patrolAssetCheck PatrolAssetCheck
     * @return List<PatrolAssetCheck>
     */
    List<PatrolAssetCheck> list(PatrolAssetCheck patrolAssetCheck);

    /**
     * 获取request条件下的总数
     * @param request 请求参数
     * @return 总数
     */
    int countAssetCheckData(AssetBaseDataRequest request);

    /**
     * 获取request条件下的数据
     * @param request 请求参数
     * @return 数据
     */
    List<PatrolAssetCheck> selectAssetCheckData(AssetBaseDataRequest request,Long pageNum,Long pageSize);


    List<PatrolAssetCheck> exportReportCard(AssetBaseDataRequest request,Map<String, String> signUrlMap, Long pageNum, Long pageSize);

    /**
     * 获取request条件下的数据
     * @param request 请求参数
     * @return 数据
     */
    List<PatrolAssetCheck> selectPatrolAssetCheck(AssetBaseDataRequest request,Long pageNum,Long pageSize, AtomicInteger total);


    /**
     * 根据条件查询资产巡检查主表数据列表
     * @param params 查询条件
     */
    List<PatrolAssetCheck> findListByParam(Map<String, Object> params);


    /**
     * 根据资产id查询 PatrolAssetCheckDTO
     * @param patrolAssetCheckRequest 查询条件
     * @return List<PatrolAssetCheckDTO>
     */
    List<PatrolAssetCheck> selectPatrolAssetChecksByAssetIds(PatrolAssetCheckRequest patrolAssetCheckRequest);


    /**
     * selectPatrolAssetChecksByAssetIdsBatch
     * 根据资产id查询 PatrolAssetCheckDTO
     * @param patrolAssetCheckRequestList 查询条件List
     * @return List<PatrolAssetCheckDTO></PatrolAssetCheckDTO>
     */
    List<PatrolAssetCheck> selectPatrolAssetChecksByAssetIdsBatch(
            List<PatrolAssetCheckRequest> patrolAssetCheckRequestList,
            AssetType assetType);

    String generateCheckByCondition(AssetBaseDataRequest assetBaseDataRequest);


    /**
     * 获取处理进度
     * @param processId 进程ID
     * @return Map<String, Object>
     */
    Map<String, Object> getProgress(String processId);


    /**
     * 生成缺少的检查日志
     * @param assetBaseDataRequest 查询条件
     * @param nowDateList 当前日期列表
     * @return List<PatrolInspectionLogs> 缺少的检查日志
     */
    List<PatrolInspectionLogs> generateLogByCondition(AssetBaseDataRequest assetBaseDataRequest, List<LocalDate> nowDateList);

    /**
     * 传入查询条件PatrolAssetCheckRequest，查询PatrolAssetCheckDTO
     */
    void scheduleGenerateCheck(LocalDate nowDate,AssetType assetType,String processId);


    /**
     * 传入查询条件PatrolAssetCheckRequest，查询PatrolAssetCheckDTO
     */
    void scheduleGenerateCheck(YearMonth nowDate, AssetType assetType);


    /**
     * 根据assetId和yearMonth筛选并合并日期区间
     *
     * @param assetId    资产ID
     * @param yearMonth  年月（格式：YYYY-MM）
     * @return 合并后的日期列表
     */
    List<Integer> getMergedDays(InspectionType type,String assetId, String yearMonth);

    // 补全数据
    void completeData();

    /**
     * 老数据补充主表的病害数量
     */
    void completeDiseaseNum();

    /**
     * 老数据桥梁的补充
     */
    void completeBridgeDiseaseNum();


    // 重计算老数据的到期时间
    void recalculateExpiry();

    /**
     * 查询checkTime>expiry
     * ids
     * type
     * tableName
     */
    List<PatrolCheckBase> selectMaxExpiryByCheckTimeGreaterThanExpiry(AssetBaseDataRequest request);

    void updatePropertyUnitName();

    void completeDataByMonth(YearMonth yearMonth,InspectionType type);

    /**
     * 根据创建时间和表名查询病害数大于0的数据
     *
     * @param createTime 创建时间
     * @param tableName 表名
     * @return 巡检记录列表
     */
    List<PatrolAssetCheck> selectByCreateTimeAndDiseaseCount(LocalDateTime createTime, String tableName);
    
    /**
     * 根据ID列表批量更新状态
     *
     * @param request 包含checkIds和status的请求对象
     * @return 更新的记录数
     */
    int updateStatusByIds(AssetBaseDataRequest request);
    
    /**
     * 根据指定的创建时间更新病害数量
     * 处理桥梁、隧道和涵洞三种资产类型
     * 每100条记录批量更新一次
     */
    void updateDiseaseCountByCreateTime();

    /**
     * 批量完成资产审核
     *
     * @param request 批量审核请求参数
     * @return 更新记录数
     */
    int completeAuditInfo(BatchAuditRequest request);

    void generateHistory(AssetBaseDataRequest request);
}
