package com.ruoyi.patrol.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: QD
 * @date: 2024年12月09日 14:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InspectionCountVO {

    @ApiModelProperty("桥梁经常巡查坐次")
    private Integer bridgeCount;

    @ApiModelProperty("隧道经常巡查坐次")
    private Integer tunnelCount;

    @ApiModelProperty("涵洞经常巡查坐次")
    private Integer culvertCount;

    @ApiModelProperty("路基经常巡查里程")
    private String roadbedMileage;

}
