package com.ruoyi.patrol.service.handler.impl;

import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import lombok.extern.slf4j.Slf4j;

import java.awt.Color; // 导入AWT Color
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * "桥梁日常巡查记录表"生成处理器。
 * 继承自抽象处理器以重用通用的Excel生成逻辑。
 */
@Slf4j
public class BridgeDailyReportHandler extends AbstractExcelReportHandler<PatrolAssetCheck> {

    private final List<PatrolAssetCheck> reportDataList;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");

    // --- 此报表的特定样式定义 ---
    // 继承的样式：titleStyle, headerLabelStyle, headerValueStyle, itemCellStyle, itemFirstColStyle
    // 重新定义的样式（确保有边框）: itemHeaderStyle
    private CellStyle itemHeaderStyleWithBg; // 带背景的项目表头样式
    private CellStyle itemContentStyle;       // 检查内容样式 (居中) - 基于itemCellStyle
    private CellStyle itemStatusStyle;        // 状态描述样式 (居中) - 基于itemCellStyle
    private CellStyle itemMaintenanceStyle;   // 保养措施意见样式 (居中) - 基于itemCellStyle
    private CellStyle footerLabelStyle;       // 页脚标签样式 (无背景)
    private CellStyle noBorderStyle;      // 无边框左对齐自动换行样式
    private CellStyle footerLabelStyleWithBg; // 带背景的页脚标签样式
    private CellStyle footerValueStyle;       // 页脚值样式 (需要确保有边框)
    private CellStyle normalBorderStyle;      // 仅用于细边框的样式

    // 继承的：signUrlMap, signImageMap, failedSignIds, log, SIGNATURE_HEIGHT_POINTS

    public BridgeDailyReportHandler(List<PatrolAssetCheck> reportDataList, Map<String, String> signUrlMap) {
        // super(); // 隐式调用超类构造函数
        this.reportDataList = reportDataList != null ? reportDataList : new ArrayList<>();
        if (signUrlMap != null) {
            this.signUrlMap.putAll(signUrlMap); // 填充继承的映射
        }
        // 预加载在beforeSheetCreate中调用
    }

    /**
     * 重写createStyles方法，为此报表类型添加特定样式
     * 并初始化基类中的通用样式。
     */
    @Override
    protected void createStyles(Workbook workbook) {
        super.createStyles(workbook); // 首先初始化通用样式

        // --- 此报表的特定样式 ---

        // --- 通用边框样式 ---
        normalBorderStyle = workbook.createCellStyle();
        setThinBorders(normalBorderStyle); // 使用继承的辅助方法

        // 无边框左对齐自动换行样式
        noBorderStyle = workbook.createCellStyle();
        noBorderStyle.setBorderTop(BorderStyle.NONE);
        noBorderStyle.setBorderBottom(BorderStyle.NONE);
        noBorderStyle.setBorderLeft(BorderStyle.NONE);
        noBorderStyle.setBorderRight(BorderStyle.NONE);
        noBorderStyle.setAlignment(HorizontalAlignment.LEFT);
        noBorderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        noBorderStyle.setWrapText(true);

        // --- 如需要，重新初始化/确保继承样式上的边框 ---
        // 克隆基类样式并应用边框，以确保此报告中的所有内容都有边框
        // 标题标签样式
        CellStyle baseHeaderLabelStyle = super.headerLabelStyle;
        headerLabelStyle = workbook.createCellStyle();
        headerLabelStyle.cloneStyleFrom(baseHeaderLabelStyle);
        setSolidBackground(headerLabelStyle, new java.awt.Color(217, 217, 217));
        copyBorders(normalBorderStyle, headerLabelStyle); // 应用边框

        // 标题值样式
        CellStyle baseHeaderValueStyle = super.headerValueStyle;
        headerValueStyle = workbook.createCellStyle();
        headerValueStyle.cloneStyleFrom(baseHeaderValueStyle);
        copyBorders(normalBorderStyle, headerValueStyle);

        // 项目表头样式 (基础，不带背景)
        CellStyle baseItemHeaderStyle = super.itemHeaderStyle;
        itemHeaderStyle = workbook.createCellStyle();
        itemHeaderStyle.cloneStyleFrom(baseItemHeaderStyle);
        copyBorders(normalBorderStyle, itemHeaderStyle); // 应用边框

        // 项目单元格样式 (通用居中)
        CellStyle baseItemCellStyle = super.itemCellStyle;
        itemCellStyle = workbook.createCellStyle();
        itemCellStyle.cloneStyleFrom(baseItemCellStyle);
        copyBorders(normalBorderStyle, itemCellStyle);

        // 项目第一列样式 (左对齐)
        CellStyle baseItemFirstColStyle = super.itemFirstColStyle;
        itemFirstColStyle = workbook.createCellStyle();
        itemFirstColStyle.cloneStyleFrom(baseItemFirstColStyle);
        copyBorders(normalBorderStyle, itemFirstColStyle); // 应用边框


        // --- 新的/特定样式 ---
        Color lightGray = new Color(217, 217, 217);

        // 带背景的项目表头样式
        itemHeaderStyleWithBg = workbook.createCellStyle();
        itemHeaderStyleWithBg.cloneStyleFrom(itemHeaderStyle); // 继承字体、对齐、边框
        setSolidBackground(itemHeaderStyleWithBg, lightGray); // 设置背景色
        itemHeaderStyleWithBg.setWrapText(true);

        // 检查内容样式 (基于itemCellStyle，已居中带边框)
        itemContentStyle = workbook.createCellStyle();
        itemContentStyle.cloneStyleFrom(itemCellStyle);
        itemContentStyle.setWrapText(true);

        // 状态描述样式 (基于itemCellStyle，已居中带边框)
        itemStatusStyle = workbook.createCellStyle();
        itemStatusStyle.cloneStyleFrom(itemCellStyle);
        itemStatusStyle.setWrapText(true);

        // 保养措施意见样式 (基于itemCellStyle，已居中带边框)
        itemMaintenanceStyle = workbook.createCellStyle();
        itemMaintenanceStyle.cloneStyleFrom(itemCellStyle);
        itemMaintenanceStyle.setWrapText(true);

        // 页脚标签样式 (继承headerLabelStyle对齐方式，应用边框)
        footerLabelStyle = workbook.createCellStyle(); // 先定义无背景的
        footerLabelStyle.cloneStyleFrom(headerLabelStyle); // 继承字体、右对齐、边框
        footerLabelStyle.setWrapText(true);

        // 带背景的页脚标签样式
        footerLabelStyleWithBg = workbook.createCellStyle();
        footerLabelStyleWithBg.cloneStyleFrom(footerLabelStyle); // 继承字体、右对齐、边框
        setSolidBackground(footerLabelStyleWithBg, lightGray); // 设置背景色
        footerLabelStyleWithBg.setWrapText(true);

        // 页脚值样式 (继承headerValueStyle对齐方式，应用边框)
        footerValueStyle = workbook.createCellStyle();
        footerValueStyle.cloneStyleFrom(headerValueStyle); // 继承字体、左对齐、边框
        footerValueStyle.setWrapText(true);
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        super.beforeSheetCreate(writeWorkbookHolder, writeSheetHolder); // 通过createStyles重写创建样式
        // 在生成表格内容前预加载签名
        if (!signUrlMap.isEmpty()) {
            preloadSignImages(); // 调用继承的预加载器
        }
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        // 诊断日志
        log.info("开始生成桥梁日常巡查记录表...");
        if (signUrlMap.isEmpty()) {
            log.warn("签名URL映射为空。签名可能不会出现。");
        } else {
            log.info("签名URL映射包含 {} 条记录。", signUrlMap.size());
            log.info("预加载的签名图像缓存包含 {} 条记录。", signImageMap.size());
            if (!failedSignIds.isEmpty()) {
                log.warn("有 {} 个签名下载失败。ID列表: {}", failedSignIds.size(), failedSignIds);
            }
        }

        int currentRowIndex = 0; // 当前行索引

        // --- 设置列宽 (可以根据需要调整，这里暂时沿用Regular的宽度) ---
        sheet.setColumnWidth(0, 10 * 256);  // A列 (路线/桥梁编码 标签)
        sheet.setColumnWidth(1, 13 * 256);  // B列 (路线/桥梁编码 值)
        sheet.setColumnWidth(2, 14 * 256);  // C列 (路线/桥梁名称 标签)
        sheet.setColumnWidth(3, 16 * 256);  // D列 (路线/桥梁名称 值)
        sheet.setColumnWidth(4, 11 * 256);  // E列 (桩号/养护单位 标签)
        sheet.setColumnWidth(5, 20 * 256);  // F列 (桩号/养护单位 值)

        // 存储列宽信息用于行高调整
        int[] columnWidths = new int[] {10, 13, 14, 16, 11, 20};

        // 调整项目列的宽度以适应内容
        if (reportDataList == null || reportDataList.isEmpty()) {
            log.warn("报告数据列表为空。生成空表格。");
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "桥梁日常巡查记录表 (无数据)", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));
            return; // 没有数据则停止处理
        }

        for (int i = 0; i < reportDataList.size(); i++) {
            PatrolAssetCheck reportData = reportDataList.get(i);
            if (reportData == null) {
                log.warn("跳过索引 {} 处的null报告数据对象", i);
                continue; // 跳过null数据条目
            }

            log.debug("正在为资产编码 {} 生成日常巡查报告部分", reportData.getAssetCode());

            // 1. 标题行
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "桥梁日常巡查记录表", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));
            currentRowIndex++;

            // 2. 管理单位行
            Row agencyRow = sheet.createRow(currentRowIndex);
            // 使用左对齐的值样式来显示标签和值
            String agencyName = reportData.getPropertyUnitName() != null ? reportData.getPropertyUnitName() : reportData.getMaintainUnitName();
            // 创建单元格并应用边框，然后合并
            for (int col = 0; col <= 5; col++) {
                if (col == 0) {
                    // 使用 footerValueStyle (左对齐，带边框)
                    createCell(agencyRow, col, "管理单位：" + Objects.toString(agencyName, ""), footerValueStyle);
                } else {
                    createCell(agencyRow, col, "", footerValueStyle); // 为边框/合并创建空单元格
                }
            }
            applyRowBorder(agencyRow, 0, 5, normalBorderStyle); // 确保边框一致
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));
            setConditionalRowHeight(agencyRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 3. 路线信息行
            Row routeRow = sheet.createRow(currentRowIndex);
            createCell(routeRow, 0, "路线编码", headerLabelStyle);
            createCell(routeRow, 1, Objects.toString(reportData.getRouteCode(), ""), headerValueStyle);
            createCell(routeRow, 2, "路线名称", headerLabelStyle);
            // 路线名称可能在不同的字段，例如 maintenanceSectionName 或 routeName
            String routeName = reportData.getMaintenanceSectionName() != null ? reportData.getMaintenanceSectionName() : reportData.getRouteName();
            createCell(routeRow, 3, Objects.toString(routeName, ""), headerValueStyle);
            createCell(routeRow, 4, "桥位桩号", headerLabelStyle);
            createCell(routeRow, 5, formatStake(reportData.getCenterStake(), reportData.getStakeFormat()), headerValueStyle);
            applyRowBorder(routeRow, 0, 5, normalBorderStyle); // 应用边框
            setConditionalRowHeight(routeRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 4. 桥梁信息行
            Row bridgeRow = sheet.createRow(currentRowIndex);
            createCell(bridgeRow, 0, "桥梁编码", headerLabelStyle);
            createCell(bridgeRow, 1, Objects.toString(reportData.getAssetCode(), ""), headerValueStyle);
            createCell(bridgeRow, 2, "桥梁名称", headerLabelStyle);
            createCell(bridgeRow, 3, Objects.toString(reportData.getAssetName(), ""), headerValueStyle);
            createCell(bridgeRow, 4, "养护单位", headerLabelStyle);
            createCell(bridgeRow, 5, Objects.toString(reportData.getMaintainUnitName(), ""), headerValueStyle);
            applyRowBorder(bridgeRow, 0, 5, normalBorderStyle);
            setConditionalRowHeight(bridgeRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 5. 项目标题行
            Row itemHeaderRow = sheet.createRow(currentRowIndex);
            itemHeaderRow.setHeightInPoints(25);
            // 使用带背景的样式
            createCell(itemHeaderRow, 0, "检查项目", itemHeaderStyleWithBg); // 合并A
            createCell(itemHeaderRow, 1, "检查内容", itemHeaderStyleWithBg);      // B
            createCell(itemHeaderRow, 2, "", itemHeaderStyleWithBg); // 占位符C
            createCell(itemHeaderRow, 3, "状态描述", itemHeaderStyleWithBg); // D
            createCell(itemHeaderRow, 4, "保养措施意见", itemHeaderStyleWithBg); // 合并E、F
            createCell(itemHeaderRow, 5, "", itemHeaderStyleWithBg);      // 占位符F
            // 使用itemHeaderStyleWithBg自身的边框定义
            applyRowBorder(itemHeaderRow, 0, 5, itemHeaderStyleWithBg);
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 2)); // 合并B、C
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 5)); // 合并E、F
            currentRowIndex++;

            // 6. 项目详情行
            List<PatrolAssetCheckDetail> details = reportData.getPatrolCheckDetailList();
            if (details != null && !details.isEmpty()) {
                for (PatrolAssetCheckDetail item : details) {
                    if (item == null) {
                        log.warn("跳过资产编码 {} 的null详情项", reportData.getAssetCode());
                        continue;
                    }
                    Row itemRow = sheet.createRow(currentRowIndex);
                    // 检查项目 (A/B) - 左对齐
                    createCell(itemRow, 0, Objects.toString(item.getPartsTypeName(), ""), itemFirstColStyle);

                    // 检查内容 (B/C) - 居中
                    createCell(itemRow, 1, item.getDes() == null || item.getDes().trim().isEmpty() ? "无" : item.getDes(), itemFirstColStyle);
                    createCell(itemRow, 2, "", itemContentStyle); // 占位符
                    sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 2));

                    // 状态描述 (D) - 居中，带默认值
                    createCell(itemRow, 3, item.getDefect() == null || item.getDefect().trim().isEmpty() ? "未见异常" : item.getDefect(), itemStatusStyle);

                    // 保养措施意见 (E/F) - 居中，带默认值
                    createCell(itemRow, 4, item.getAdvice() == null || item.getAdvice().trim().isEmpty() ? "正常保养" : item.getAdvice(), itemMaintenanceStyle);
                    createCell(itemRow, 5, "", itemMaintenanceStyle); // 占位符
                    sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 5));

                    // 为整行应用一致的细边框
                    applyRowBorder(itemRow, 0, 5, normalBorderStyle);

                    // 在项目行使用条件高度
                    setConditionalRowHeight(itemRow, 0, 5, columnWidths);

                    currentRowIndex++;
                }
            } else {
                // 无详情行
                log.info("资产编码 {} 未找到日常检查详情", reportData.getAssetCode());
                Row emptyRow = sheet.createRow(currentRowIndex);
                createCell(emptyRow, 0, "无检查明细", itemFirstColStyle); // A/B
                createCell(emptyRow, 1, "", itemFirstColStyle);
                createCell(emptyRow, 2, "", itemContentStyle);       // C
                createCell(emptyRow, 3, "", itemStatusStyle);        // D
                createCell(emptyRow, 4, "", itemMaintenanceStyle);   // E/F
                createCell(emptyRow, 5, "", itemMaintenanceStyle);
                sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));
                applyRowBorder(emptyRow, 0, 5, normalBorderStyle); // 应用边框
                setConditionalRowHeight(emptyRow, 0, 5, columnWidths);
                currentRowIndex++;
            }
            // 2. 添加空白行防止签名越界
            Row blankRow1 = sheet.createRow(currentRowIndex);
            createCell(blankRow1, 1, "", noBorderStyle); // 合并创建空单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            blankRow1.setHeightInPoints(5);
            currentRowIndex++;

            // 7. 页脚行 (标签和日期)
            int footerLabelRowIndex = currentRowIndex;
            Row footerLabelRow = sheet.createRow(footerLabelRowIndex);
            footerLabelRow.setHeightInPoints(25); // 标签行高度

            // 使用带背景的标签样式
            createCell(footerLabelRow, 0, "负责人", footerLabelStyleWithBg); // 标签A
            createCell(footerLabelRow, 1, "", footerValueStyle);       // 占位符B (值/签名区)
            createCell(footerLabelRow, 2, "记录人", footerLabelStyleWithBg); // 标签C
            createCell(footerLabelRow, 3, "", footerValueStyle);       // 占位符D (值/签名区)
            createCell(footerLabelRow, 4, "检查日期", footerLabelStyleWithBg); // 标签E
            Date checkDate = reportData.getCheckTime();
            createCell(footerLabelRow, 5, checkDate != null ? dateFormat.format(checkDate) : "", footerValueStyle); // 值F (日期)

            // 8. 页脚签名/姓名区域
            // 签名逻辑与BridgeRegularReportHandler完全相同
            int signatureStartRowIndex = currentRowIndex ; // 从标签行开始放名字，图片从下一行开始放
            List<String> kahunaSignList = reportData.getKahunaSignList();
            List<String> oprUserSignList = reportData.getOprUserSignList();

            log.debug("负责人 ({}) 签名数量: {}", reportData.getKahunaName(), kahunaSignList != null ? kahunaSignList.size() : 0);
            log.debug("记录人 ({}) 签名数量: {}", reportData.getOprUserName(), oprUserSignList != null ? oprUserSignList.size() : 0);

            // 将负责人姓名/签名添加到B列 (索引1)
            int lastRowForKahuna = signatureStartRowIndex;
            if (kahunaSignList != null && !kahunaSignList.isEmpty()) {
                // 如果有签名图片，先在标签行写名字，图片从下一行开始
                lastRowForKahuna = addSignatureImages(sheet, signatureStartRowIndex, 1,1,
                        kahunaSignList, Objects.toString(reportData.getKahunaName(), ""), footerValueStyle, true); // 名字已写，图片下不需要名字
            } else {
                // 没有签名图片，直接在标签行写名字
                createCell(footerLabelRow, 1, Objects.toString(reportData.getKahunaName(), ""), footerValueStyle);
            }


            // 将记录人姓名/签名添加到D列（索引3）
            int lastRowForOpr = signatureStartRowIndex;
            if (oprUserSignList != null && !oprUserSignList.isEmpty()) {
                lastRowForOpr = addSignatureImages(sheet, signatureStartRowIndex, 3,3,
                        oprUserSignList, Objects.toString(reportData.getOprUserName(), ""), footerValueStyle, true); // 名字已写，图片下不需要名字
            } else {
                // 没有签名图片，直接在标签行写名字
                createCell(footerLabelRow, 3, Objects.toString(reportData.getOprUserName(), ""), footerValueStyle);
            }
            // 为标签行应用边框 (需要在写入名字后，避免名字单元格无边框)
            applyRowBorder(footerLabelRow, 0, 5, normalBorderStyle);


            // 确定页脚部分使用的最终行索引
            // 它是标签行、负责人签名结束行、记录人签名结束行中的最大值
            int footerEndRowIndex = Math.max(signatureStartRowIndex + 1, Math.max(lastRowForKahuna, lastRowForOpr));
            currentRowIndex = footerEndRowIndex;

            // --- 当前报告部分完成 ---

            // 在下一个报告部分之前插入分页符（如果不是最后一个）
            if (i < reportDataList.size() - 1) {
                // 在当前部分最后使用的行之后设置分页符
                sheet.setRowBreak(currentRowIndex - 1);
                log.debug("已在行 {} 之后插入分页符", currentRowIndex - 1);
            }
        }
        log.info("已完成生成所有桥梁日常巡查记录表部分。");
    }
}