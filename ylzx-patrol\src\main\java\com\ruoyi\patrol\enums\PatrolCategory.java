package com.ruoyi.patrol.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 *
 */
@Getter
@RequiredArgsConstructor
public enum PatrolCategory implements IEnum<String> {
    REGULAR_PATROL("1", "常规巡查"),
    RAINY_SEASON_PATROL("2", "雨季巡查"),
    SPECIAL_PATROL("3", "专项巡查"),
    OTHER_PATROL("4", "其他巡查");

    private final String code;
    private final String description;

    @Override
    public String getValue() {
        return this.code;
    }

    @JsonValue
    public String getCode() {
        return this.code;
    }

    @JsonCreator
    public static PatrolCategory fromCode(String code) {
        for (PatrolCategory category : PatrolCategory.values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}
