package com.ruoyi.patrol.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 巡查专题通用返回类
 * @author: sfc
 * @date: 2025年03月18日 9:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssetOftenPatrolVO {

    @ApiModelProperty(value = "累计检查次数")
    private Integer diseaseNum;

    @ApiModelProperty(value = "待检查资产（座）")
    private Integer toBeCheckedNum;

    @ApiModelProperty(value = "已检查资产（座）")
    private Integer checkedNum;

    @ApiModelProperty(value = "已检占比")
    private BigDecimal checkedAccountFor;

    @ApiModelProperty(value = "管理处Id")
    private Long deptId;

    @ApiModelProperty(value = "管理处名称")
    private String deptName;


}
