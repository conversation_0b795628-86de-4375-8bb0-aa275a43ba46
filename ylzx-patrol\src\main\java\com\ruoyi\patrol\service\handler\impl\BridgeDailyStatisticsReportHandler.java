package com.ruoyi.patrol.service.handler.impl;

import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.awt.Color;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * "桥梁日常巡查记录表"生成处理器。
 * 继承自抽象处理器以重用通用的Excel生成逻辑。
 */
@Slf4j
public class BridgeDailyStatisticsReportHandler extends AbstractExcelReportHandler<PatrolAssetCheck> {

    private final List<PatrolAssetCheck> reportDataList;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");

    // --- 此报表的特定样式定义 ---
    // 继承的样式：titleStyle, headerLabelStyle, headerValueStyle, itemCellStyle, itemFirstColStyle
    // 重新定义的样式（确保有边框）: itemHeaderStyle
    private CellStyle itemHeaderStyleWithBg; // 带背景的项目表头样式
    private CellStyle itemContentStyle;       // 检查内容样式 (居中) - 基于itemCellStyle
    private CellStyle itemStatusStyle;        // 状态描述样式 (居中) - 基于itemCellStyle
    private CellStyle itemMaintenanceStyle;   // 保养措施意见样式 (居中) - 基于itemCellStyle
    private CellStyle footerLabelStyle;       // 页脚标签样式 (无背景)
    private CellStyle noBorderStyle;      // 无边框左对齐自动换行样式
    private CellStyle footerLabelStyleWithBg; // 带背景的页脚标签样式
    private CellStyle footerValueStyle;       // 页脚值样式 (需要确保有边框)
    private CellStyle normalBorderStyle;      // 仅用于细边框的样式

    // 继承的：signUrlMap, signImageMap, failedSignIds, log, SIGNATURE_HEIGHT_POINTS

    public BridgeDailyStatisticsReportHandler(List<PatrolAssetCheck> reportDataList, Map<String, String> signUrlMap) {
        // super(); // 隐式调用超类构造函数
        this.reportDataList = reportDataList != null ? reportDataList : new ArrayList<>();
        if (signUrlMap != null) {
            this.signUrlMap.putAll(signUrlMap); // 填充继承的映射
        }
        // 预加载在beforeSheetCreate中调用
    }

    /**
     * 重写createStyles方法，为此报表类型添加特定样式
     * 并初始化基类中的通用样式。
     */
    @Override
    protected void createStyles(Workbook workbook) {
        super.createStyles(workbook); // 首先初始化通用样式

        // --- 此报表的特定样式 ---

        // --- 通用边框样式 ---
        normalBorderStyle = workbook.createCellStyle();
        setThinBorders(normalBorderStyle); // 使用继承的辅助方法

        // 无边框左对齐自动换行样式
        noBorderStyle = workbook.createCellStyle();
        noBorderStyle.setBorderTop(BorderStyle.NONE);
        noBorderStyle.setBorderBottom(BorderStyle.NONE);
        noBorderStyle.setBorderLeft(BorderStyle.NONE);
        noBorderStyle.setBorderRight(BorderStyle.NONE);
        noBorderStyle.setAlignment(HorizontalAlignment.LEFT);
        noBorderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        noBorderStyle.setWrapText(true);

        // --- 如需要，重新初始化/确保继承样式上的边框 ---
        // 克隆基类样式并应用边框，以确保此报告中的所有内容都有边框
        // 标题标签样式
        CellStyle baseHeaderLabelStyle = super.headerLabelStyle;
        headerLabelStyle = workbook.createCellStyle();
        headerLabelStyle.cloneStyleFrom(baseHeaderLabelStyle);
        setSolidBackground(headerLabelStyle, new Color(217, 217, 217));
        copyBorders(normalBorderStyle, headerLabelStyle); // 应用边框

        // 标题值样式
        CellStyle baseHeaderValueStyle = super.headerValueStyle;
        headerValueStyle = workbook.createCellStyle();
        headerValueStyle.cloneStyleFrom(baseHeaderValueStyle);
        copyBorders(normalBorderStyle, headerValueStyle);

        // 项目表头样式 (基础，不带背景)
        CellStyle baseItemHeaderStyle = super.itemHeaderStyle;
        itemHeaderStyle = workbook.createCellStyle();
        itemHeaderStyle.cloneStyleFrom(baseItemHeaderStyle);
        copyBorders(normalBorderStyle, itemHeaderStyle); // 应用边框

        // 项目单元格样式 (通用居中)
        CellStyle baseItemCellStyle = super.itemCellStyle;
        itemCellStyle = workbook.createCellStyle();
        itemCellStyle.cloneStyleFrom(baseItemCellStyle);
        copyBorders(normalBorderStyle, itemCellStyle);

        // 项目第一列样式 (左对齐)
        CellStyle baseItemFirstColStyle = super.itemFirstColStyle;
        itemFirstColStyle = workbook.createCellStyle();
        itemFirstColStyle.cloneStyleFrom(baseItemFirstColStyle);
        copyBorders(normalBorderStyle, itemFirstColStyle); // 应用边框


        // --- 新的/特定样式 ---
        Color lightGray = new Color(217, 217, 217);

        // 带背景的项目表头样式
        itemHeaderStyleWithBg = workbook.createCellStyle();
        itemHeaderStyleWithBg.cloneStyleFrom(itemHeaderStyle); // 继承字体、对齐、边框
        setSolidBackground(itemHeaderStyleWithBg, lightGray); // 设置背景色
        itemHeaderStyleWithBg.setWrapText(true);

        // 检查内容样式 (基于itemCellStyle，已居中带边框)
        itemContentStyle = workbook.createCellStyle();
        itemContentStyle.cloneStyleFrom(itemCellStyle);
        itemContentStyle.setWrapText(true);

        // 状态描述样式 (基于itemCellStyle，已居中带边框)
        itemStatusStyle = workbook.createCellStyle();
        itemStatusStyle.cloneStyleFrom(itemCellStyle);
        itemStatusStyle.setWrapText(true);

        // 保养措施意见样式 (基于itemCellStyle，已居中带边框)
        itemMaintenanceStyle = workbook.createCellStyle();
        itemMaintenanceStyle.cloneStyleFrom(itemCellStyle);
        itemMaintenanceStyle.setWrapText(true);

        // 页脚标签样式 (继承headerLabelStyle对齐方式，应用边框)
        footerLabelStyle = workbook.createCellStyle(); // 先定义无背景的
        footerLabelStyle.cloneStyleFrom(headerLabelStyle); // 继承字体、右对齐、边框
        footerLabelStyle.setWrapText(true);

        // 带背景的页脚标签样式
        footerLabelStyleWithBg = workbook.createCellStyle();
        footerLabelStyleWithBg.cloneStyleFrom(footerLabelStyle); // 继承字体、右对齐、边框
        setSolidBackground(footerLabelStyleWithBg, lightGray); // 设置背景色
        footerLabelStyleWithBg.setWrapText(true);

        // 页脚值样式 (继承headerValueStyle对齐方式，应用边框)
        footerValueStyle = workbook.createCellStyle();
        footerValueStyle.cloneStyleFrom(headerValueStyle); // 继承字体、左对齐、边框
        footerValueStyle.setWrapText(true);
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        super.beforeSheetCreate(writeWorkbookHolder, writeSheetHolder); // 通过createStyles重写创建样式
        // 在生成表格内容前预加载签名
        if (!signUrlMap.isEmpty()) {
            preloadSignImages(); // 调用继承的预加载器
        }
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        // 诊断日志
        log.info("开始生成桥梁日常巡查记录表...");
        if (signUrlMap.isEmpty()) {
            log.warn("签名URL映射为空。签名可能不会出现。");
        } else {
            log.info("签名URL映射包含 {} 条记录。", signUrlMap.size());
            log.info("预加载的签名图像缓存包含 {} 条记录。", signImageMap.size());
            if (!failedSignIds.isEmpty()) {
                log.warn("有 {} 个签名下载失败。ID列表: {}", failedSignIds.size(), failedSignIds);
            }
        }

        int currentRowIndex = 0; // 当前行索引

        // 调整项目列的宽度以适应内容
        if (reportDataList == null || reportDataList.isEmpty()) {
            log.warn("报告数据列表为空。生成空表格。");
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, reportDataList.get(0).getMaintenanceSectionName() +"桥梁日常巡查记录表 (无数据)", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 8));
            return; // 没有数据则停止处理
        }


        // 1. 标题
        Row titleRow = sheet.createRow(currentRowIndex);
        titleRow.setHeightInPoints(35);
        createCell(titleRow, 0, reportDataList.get(0).getMaintenanceSectionName() +"桥梁日常巡查记录表", titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 8));
        currentRowIndex++;

        // 2. 字段
        Row agencyRow = sheet.createRow(currentRowIndex);

        // --- 设置列宽 (可以根据需要调整，这里暂时沿用Regular的宽度) ---
        sheet.setColumnWidth(0, 16 * 256);  // A列 (路线/桥梁编码 标签)
        sheet.setColumnWidth(1, 8 * 256);  // B列 (路线/桥梁编码 值)
        sheet.setColumnWidth(2, 8 * 256);  // C列 (路线/桥梁名称 标签)
        sheet.setColumnWidth(3, 8 * 256);  // D列 (路线/桥梁名称 值)
        sheet.setColumnWidth(4, 8 * 256);  // E列 (桩号/养护单位 标签)
        sheet.setColumnWidth(5, 8 * 256);  // F列 (桩号/养护单位 值)
        sheet.setColumnWidth(6, 8 * 256);  // F列 (桩号/养护单位 值)
        sheet.setColumnWidth(7, 8 * 256);  // F列 (桩号/养护单位 值)
        sheet.setColumnWidth(8, 8 * 256);  // F列 (桩号/养护单位 值)

        // 创建单元格并应用边框，然后合并
        createCell(agencyRow, 0, "桥梁编码/桥梁名称", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 1, "桥路线性", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 2, "桥梁安全保护区", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 3, "桥梁整体", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 4, "标志标牌", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 5, "桥路连接处", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 6, "桥面铺装、伸缩缝", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 7, "栏杆或护栏等", headerLabelStyle); // 为边框/合并创建空单元格
        createCell(agencyRow, 8, "其他", headerLabelStyle); // 为边框/合并创建空单元格
        currentRowIndex++;

        for (int i = 0; i < reportDataList.size(); i++) {
            PatrolAssetCheck reportData = reportDataList.get(i);
            if (reportData == null) {
                log.warn("跳过索引 {} 处的null报告数据对象", i);
                continue; // 跳过null数据条目
            }

            log.debug("正在为资产编码 {} 生成日常巡查报告部分", reportData.getAssetCode());

            // 4. 桥梁名称
            Row bridgeRow = sheet.createRow(currentRowIndex);
            createCell(bridgeRow, 0, Objects.toString(reportData.getAssetCode() + "/" + Objects.toString(reportData.getAssetName(), "")), headerValueStyle);

            // 6. 项目详情行
            List<PatrolAssetCheckDetail> details = reportData.getPatrolCheckDetailList();
            if (details != null && !details.isEmpty()) {
                List<String> list = null;

                list = details.stream().filter(item -> item.getPartsTypeName().equals("桥路线性")).map(PatrolAssetCheckDetail::getDes).toList();
                if (list.size() == 1) {
                    createCell(bridgeRow, 1, list.get(0), headerValueStyle);
                }

                list = details.stream().filter(item -> item.getPartsTypeName().equals("桥梁安全保护区")).map(PatrolAssetCheckDetail::getDes).toList();
                if (list.size() == 1) {
                    createCell(bridgeRow, 2, list.get(0), headerValueStyle);
                }

                list = details.stream().filter(item -> item.getPartsTypeName().equals("桥梁整体")).map(PatrolAssetCheckDetail::getDes).toList();
                if (list.size() == 1) {
                    createCell(bridgeRow, 3, list.get(0), headerValueStyle);
                }

                list = details.stream().filter(item -> item.getPartsTypeName().equals("标志标牌")).map(PatrolAssetCheckDetail::getDes).toList();
                if (list.size() == 1) {
                    createCell(bridgeRow, 4, list.get(0), headerValueStyle);
                }

                list = details.stream().filter(item -> item.getPartsTypeName().equals("桥路连接处")).map(PatrolAssetCheckDetail::getDes).toList();
                if (list.size() == 1) {
                    createCell(bridgeRow, 5, list.get(0), headerValueStyle);
                }

                list = details.stream().filter(item -> item.getPartsTypeName().equals("桥面铺装、伸缩缝")).map(PatrolAssetCheckDetail::getDes).toList();
                if (list.size() == 1) {
                    createCell(bridgeRow, 6, list.get(0), headerValueStyle);
                }

                list = details.stream().filter(item -> item.getPartsTypeName().equals("栏杆或护栏等")).map(PatrolAssetCheckDetail::getDes).toList();
                if (list.size() == 1) {
                    createCell(bridgeRow, 7, list.get(0), headerValueStyle);
                }

                list = details.stream().filter(item -> item.getPartsTypeName().equals("其他")).map(PatrolAssetCheckDetail::getDes).toList();
                if (list.size() == 1) {
                    createCell(bridgeRow, 8, list.get(0), headerValueStyle);
                }
            }
            currentRowIndex++;
        }

        // 8. 签名/姓名区域
        Row endRow = sheet.createRow(currentRowIndex);
        endRow.setHeightInPoints(35);

        int signatureStartRowIndex = currentRowIndex ; // 从标签行开始放名字，图片从下一行开始放
//        List<String> kahunaSignList = reportDataList.get(0).getKahunaSignList();
//        List<String> oprUserSignList = reportDataList.get(0).getOprUserSignList();
        List<String> kahunaSignList = reportDataList.stream().map(PatrolAssetCheck::getKahunaSignList).flatMap(List::stream).distinct().collect(Collectors.toList());
        List<String> oprUserSignList = reportDataList.stream().map(PatrolAssetCheck::getOprUserSignList).flatMap(List::stream).distinct().collect(Collectors.toList());
        String kahunaNameStr = reportDataList.stream().map(PatrolAssetCheck::getKahunaName).filter(Objects::nonNull).distinct().collect(Collectors.joining(" "));
        String oprUserNameStr = reportDataList.stream().map(PatrolAssetCheck::getOprUserName).filter(Objects::nonNull).distinct().collect(Collectors.joining(" "));


        // 将负责人姓名/签名添加到B列
        int lastRowForKahuna = currentRowIndex;
        if (kahunaSignList != null && !kahunaSignList.isEmpty()) {
            createCell(endRow, 0, "负责人:", null);
            lastRowForKahuna = addSignatureImages(sheet, signatureStartRowIndex, 1,2,
                    kahunaSignList, "" , footerValueStyle, false); // 名字已写，图片下不需要名字
        } else {
            createCell(endRow, 0, "负责人:" + kahunaNameStr,  null);
        }


        // 将记录人姓名
        int lastRowForOpr = currentRowIndex;
        if (oprUserSignList != null && !oprUserSignList.isEmpty()) {
            createCell(endRow, 3, "记录人:" ,  null);
            lastRowForOpr = addSignatureImages(sheet, signatureStartRowIndex, 4,5,
                    oprUserSignList, "" ,  footerValueStyle, false); // 名字已写，图片下不需要名字
        } else {
            createCell(endRow, 3, "记录人:" +  oprUserNameStr ,  null);
        }

//        createCell(endRow, 0, "负责人:" + kahunaNameStr ,  null);
//        createCell(endRow, 3, "记录人:" +  oprUserNameStr ,  null);
        sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 2));
        sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 3, 5));

        String format = dateFormat.format(reportDataList.get(0).getCheckTime());
        createCell(endRow, 6, "检查时间:"+format, null);
        sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 6, 8));


    }
}