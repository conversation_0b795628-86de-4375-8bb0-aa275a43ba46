package com.ruoyi.patroltidb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheck;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheckDetail;
import com.ruoyi.patroltidb.domain.PatrolDeviceFaultRecord;
import com.ruoyi.patroltidb.domain.excel.DeviceMonthlyReportExport;

import java.util.List;
import java.util.Map;

/**
 * 隧道机电日常巡查Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
public interface PatrolDeviceCheckMapper extends BaseMapper<PatrolDeviceCheck> {

    List<PatrolDeviceCheck> findListByParam(Map<String, Object> params);

    List<PatrolDeviceCheckDetail> findDetailListByParam(Map<String, Object> params);

    List<PatrolDeviceFaultRecord> findFaultListByParam(Map<String, Object> params);

    List<DeviceMonthlyReportExport> listMonthlyReport(Map<String, Object> params);

    List<PatrolDeviceFaultRecord> getByAssetIdFault(Map<String, Object> params);

    Long findFaultListByParamCount(Map<String, Object> params);

    Long findDetailListByParamCount(Map<String, Object> params);


}
