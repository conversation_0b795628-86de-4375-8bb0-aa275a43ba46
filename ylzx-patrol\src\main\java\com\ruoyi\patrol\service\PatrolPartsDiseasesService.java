package com.ruoyi.patrol.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patrol.domain.PatrolPartsDiseases;

import java.util.Map;

/**
 * 部件-病害关系Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface PatrolPartsDiseasesService extends IService<PatrolPartsDiseases> {

    /**
     * 获取部件-病害的映射关系
     * <p>
     * 处理流程：
     * 1. 首先尝试从Redis缓存获取映射关系
     * 2. 如果缓存中不存在，则从数据库重新查询并生成映射
     * 3. 将新生成的映射保存到Redis中，设置24小时过期时间
     * <p>
     * 映射策略：
     * - Key: 病害ID (diseases_id)
     * - Value: 部件ID (parts_id)
     * - 当存在重复的病害ID时，保留第一个对应的部件ID
     * <p>
     * 示例：
     * {
     * "D001": "P001",  // 病害D001对应部件P001
     * "D002": "P002",  // 病害D002对应部件P002
     * }
     *
     * @return 病害ID到部件ID的映射关系
     */
    Map<String, String> getPartsDiseasesMap();

}
