package com.ruoyi.other.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.other.domain.OtherTransportationDept;
import com.ruoyi.other.domain.dto.OtherTransportationDTO;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;
import java.util.Map;


/**
 * 大件运输Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface OtherTransportationDeptMapper extends BaseMapper<OtherTransportationDept> {

    List<OtherTransportationDept> findListByParam(Map<String, Object> params);

    List<String> getManagementDepartmentIdsByTransportationId(String id);

    int deleteByTransportationIdAndDeptId(@Param("transportationId") String transportationId);


    List<String> getTransportationDeptByIds(@Param("list") List<String> transportationList);


}

