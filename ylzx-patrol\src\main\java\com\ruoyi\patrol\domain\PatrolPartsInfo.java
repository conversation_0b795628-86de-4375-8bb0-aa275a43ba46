package com.ruoyi.patrol.domain;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import com.ruoyi.patrol.enums.InspectionType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 检查类型对象 patrol_parts_info
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="检查类型")
@TableName("patrol_parts_info")
@Data
public class PatrolPartsInfo extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 类型 */
    @Excel(name = "类型")
    @ApiModelProperty(value = "类型(1:桥梁日常巡查;2:桥梁经常检查;3:涵洞定期检查;4:涵洞经常检查;5:隧道日常巡查;6:隧道经常检查;)")
    @EnumValue
    private InspectionType partsType;

    /** 名称 */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称")
    private String partsName;

    /** 编码 */
    @Excel(name = "编码")
    @ApiModelProperty(value = "编码")
    private String partsCode;

    /** 描述 */
    @Excel(name = "描述")
    @ApiModelProperty(value = "描述")
    private String des;

    /** 扩展字段（备用） */
    @Excel(name = "扩展字段", readConverterExp = "备=用")
    @ApiModelProperty(value = "扩展字段（备用）")
    private String extend;

    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /** 删除标识 */
    @TableLogic
    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

}
