package com.ruoyi.patroltidb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheckDetail;
import com.ruoyi.patroltidb.mapper.PatrolDeviceCheckDetailMapper;
import com.ruoyi.patroltidb.service.PatrolDeviceCheckDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 隧道机电巡查明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
@Service
@Slave
public class PatrolDeviceCheckDetailServiceImpl extends ServiceImpl<PatrolDeviceCheckDetailMapper, PatrolDeviceCheckDetail> implements PatrolDeviceCheckDetailService {

//    @Autowired
//    private PatrolDeviceCheckDetailMapper patrolDeviceCheckDetailMapper;

    @Override
    public List<PatrolDeviceCheckDetail> findListByParam(Map<String, Object> params) {
        return baseMapper.findListByParam(params);
    }


}
