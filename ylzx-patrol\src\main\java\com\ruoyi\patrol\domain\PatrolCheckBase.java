package com.ruoyi.patrol.domain;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import com.ruoyi.patrol.enums.InspectionType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Description:
 * @author: QD
 * @date: 2024年10月31日 17:07
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="资产巡检查基础表")
@Data
@NoArgsConstructor
public class PatrolCheckBase extends BaseTableEntity {

    /** 资产id 当类型为桥梁检查时，记录桥梁id，为涵洞时，记录涵洞id */
    @ApiModelProperty(value = "资产id 当类型为桥梁检查时，记录桥梁id，为涵洞时，记录涵洞id")
    private String assetId;

    /** 检查类型 1.桥梁经常检查 2.涵洞经常检查 3.涵洞定期检查 */
    @ApiModelProperty(value = "检查类型 1.桥梁经常检查 2.涵洞经常检查 3.涵洞定期检查")
    @EnumValue
    private InspectionType type;

    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expiry;

    @ApiModelProperty(value = "周期(天)")
    private Integer frequency;

    /** 检查时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检查时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "检查时间")
    private Date checkTime;
}
