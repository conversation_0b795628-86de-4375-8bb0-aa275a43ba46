package com.ruoyi.patrol.service.handler.impl;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.manage.api.domain.tunnel.BaseTunnelRequest;
import com.ruoyi.manage.api.domain.tunnel.BaseTunnelResponse;
import com.ruoyi.manage.api.service.BaseTunnelService;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.service.BaseTunnelResponseCacheService;
import com.ruoyi.patrol.service.enrichment.DataEnrichmentService;
import com.ruoyi.patrol.service.handler.AssetDataHandler;
import com.ruoyi.patrol.utils.DomainToCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 隧道数据处理器
 */
@Slf4j
@Component
public class TunnelDataHandler implements AssetDataHandler<BaseTunnelRequest, BaseTunnelResponse, BaseTunnelResponseCache> {

    @Resource
    private BaseTunnelService baseTunnelService;
    
    @Resource
    private BaseTunnelResponseCacheService cacheService;
    
    @Resource
    private DataEnrichmentService dataEnrichmentService;

    @Override
    public AssetType getAssetType() {
        return AssetType.TUNNEL;
    }

    @Override
    public Class<BaseTunnelRequest> getRequestClass() {
        return BaseTunnelRequest.class;
    }

    @Override
    public Class<BaseTunnelResponseCache> getCacheClass() {
        return BaseTunnelResponseCache.class;
    }

    @Override
    public BaseTunnelRequest createRequestInstance() throws Exception {
        return BaseTunnelRequest.class.getDeclaredConstructor().newInstance();
    }

    @Override
    public MTableDataInfo<List<BaseTunnelResponse>> fetchData(BaseTunnelRequest request) {
        return baseTunnelService.getInnerListPage(request, SecurityConstants.INNER);
    }

    /**
     * 将隧道响应对象列表转换为缓存对象列表
     * 直接使用原始类型转换，避免通过BaseDataDomain转换造成的字段丢失
     * 
     * @param responses 隧道响应对象列表
     * @return 隧道缓存对象列表
     */
    @Override
    public List<BaseTunnelResponseCache> convertToCacheList(List<BaseTunnelResponse> responses) {
        log.info("开始转换隧道数据，数量：{}", responses != null ? responses.size() : 0);
        // 使用直接转换方法，保留特定字段信息
        List<BaseTunnelResponseCache> cacheList = DomainToCacheUtils.convertTunnelList(responses);
        log.info("隧道数据转换完成，结果数量：{}", cacheList.size());
        
        if (log.isDebugEnabled() && !cacheList.isEmpty()) {
            BaseTunnelResponseCache sample = cacheList.get(0);
            log.debug("隧道数据转换示例: ID={}, assetId={}, assetName={}",
                    sample.getId(), sample.getAssetId(), sample.getAssetName());
        }
        
        return cacheList;
    }

    @Override
    public void prepareRequest(AssetBaseDataRequest assetRequest, BaseTunnelRequest request, boolean dataRule) {
        BeanUtils.copyProperties(assetRequest, request);
        request.setIfDataRule(dataRule);
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T extends BaseDataCache> void saveCache(List<T> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        try {
            List<BaseTunnelResponseCache> typedList = (List<BaseTunnelResponseCache>) dataList;
            cacheService.clearTable();

            // 分批保存，每批1000条记录
            int batchSize = 1000;
            int totalSize = typedList.size();
            log.info("开始分批保存隧道数据，总记录数: {}，批次大小: {}", totalSize, batchSize);

            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<BaseTunnelResponseCache> batch = typedList.subList(i, endIndex);

                try {
                    cacheService.insertBatch(batch);
                    log.info("隧道数据批次保存成功，批次: {}-{}/{}", i + 1, endIndex, totalSize);
                } catch (Exception e) {
                    log.error("隧道数据批次保存失败，批次: {}-{}", i + 1, endIndex, e);
                    throw e;
                }
            }

            log.info("隧道数据缓存成功，共{}条记录", totalSize);
        } catch (ClassCastException e) {
            log.error("隧道数据类型转换失败", e);
            throw new RuntimeException("隧道数据类型转换失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("隧道数据缓存失败", e);
            throw new RuntimeException("隧道数据缓存失败: " + e.getMessage());
        }
    }
    
    @Override
    public <T extends BaseDataCache> void enrichData(List<T> dataList) {
        dataEnrichmentService.enrichData(dataList);
    }
} 