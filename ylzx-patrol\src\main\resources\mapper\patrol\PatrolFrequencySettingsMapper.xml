<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.PatrolFrequencySettingsMapper">

    <resultMap type="com.ruoyi.patrol.domain.PatrolFrequencySettings" id="PatrolFrequencySettingsResult">
            <result property="id" column="id"/>
            <result property="assetId" column="asset_id"/>
            <result property="type" column="type"/>
            <result property="dayFrequency" column="day_frequency"/>
            <result property="monthFrequency" column="month_frequency"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">
           id, asset_id, `type`, day_frequency, month_frequency,create_by,create_time,update_by,update_time</sql>

    <sql id="where_column">
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="assetId != null and assetId != ''">
            AND asset_id = #{assetId}
        </if>
        <if test="assetIdList != null  and assetIdList.size > 0">
            AND asset_id IN
            <foreach item="item" index="index" collection="assetIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type != ''">
            AND `type` = #{type}
        </if>
        <if test="isSettings != null and isSettings != ''">
            AND day_frequency IS NOT NULL
            AND day_frequency <![CDATA[<>]]> 0
            AND month_frequency IS NOT NULL
            AND month_frequency <![CDATA[<>]]> 0
        </if>
        <if test="dayFrequency != null and dayFrequency != ''">
            AND day_frequency = #{dayFrequency}
        </if>
        <if test="monthFrequency != null and monthFrequency != ''">
            AND month_frequency = #{monthFrequency}
        </if>
    </sql>

    <sql id="set_column">
        <if test="assetId != null">
            asset_id = #{assetId},
        </if>
        <if test="type != null">
            `type` = #{type},
        </if>
        <if test="dayFrequency != null">
            day_frequency = #{dayFrequency},
        </if>
        <if test="monthFrequency != null">
            month_frequency = #{monthFrequency},
        </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="PatrolFrequencySettingsResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_frequency_settings
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="PatrolFrequencySettingsResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_frequency_settings
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

    <!-- 新增或修改（违反唯一约束就修改频率） -->
    <insert id="saveOrUpdatePlus">
        insert into patrol_frequency_settings (
        <if test="assetId != null">asset_id,</if>
        <if test="type != null and type != ''">`type`,</if>
        <if test="dayFrequency != null and dayFrequency != ''">day_frequency,</if>
        <if test="monthFrequency != null and monthFrequency != ''">month_frequency,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="updateBy != null and updateBy != ''">update_by,</if>
        create_time,
        update_time
        )values(
        <if test="assetId != null">#{assetId},</if>
        <if test="type != null and type != ''">#{type},</if>
        <if test="dayFrequency != null and dayFrequency != ''">#{dayFrequency},</if>
        <if test="monthFrequency != null and monthFrequency != ''">#{monthFrequency},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        now(),
        now()
        )
        ON DUPLICATE KEY UPDATE day_frequency = #{dayFrequency}, month_frequency = #{monthFrequency}, update_time = now();
    </insert>

</mapper>