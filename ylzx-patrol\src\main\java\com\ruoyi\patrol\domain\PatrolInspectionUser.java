package com.ruoyi.patrol.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 巡查人员关联对象 patrol_inspection_user
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
@ApiModel(value="巡查人员关联")
@TableName("patrol_inspection_user")
@Data
public class PatrolInspectionUser implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 巡查日志ID */
    @ApiModelProperty(value = "巡查日志ID")
    private String patrolId;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 用户名称 */
    @ApiModelProperty(value = "用户名称")
    private String nickName;

    /** 用户签名 */
    @ApiModelProperty(value = "用户签名")
    private String signId;

}
