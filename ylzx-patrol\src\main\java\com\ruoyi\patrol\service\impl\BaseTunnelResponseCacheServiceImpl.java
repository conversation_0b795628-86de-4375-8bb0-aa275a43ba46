package com.ruoyi.patrol.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.mapper.BaseTunnelResponseCacheMapper;
import com.ruoyi.patrol.service.BaseTunnelResponseCacheService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * base_tunnel_response(BaseTunnelResponseCache)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-21 11:34:19
 */
@Service("baseTunnelResponseCacheService")
@Master
public class BaseTunnelResponseCacheServiceImpl extends ServiceImpl<BaseTunnelResponseCacheMapper, BaseTunnelResponseCache>
        implements BaseTunnelResponseCacheService {

    /**
     * 清空表
     */
    @Transactional
    public void clearTable() {
        baseMapper.truncateTable();
    }

    /**
     * 批量新增数据
     */
    public void insertBatch(List<BaseTunnelResponseCache> entities) {
        baseMapper.insertBatch(entities);
    }

    /**
     * 获取request条件下的总数
     * @param request 请求参数
     * @return 总数
     */
    public int countAssetBaseData(AssetBaseDataRequest request){
        return baseMapper.countAssetBaseData(request);
    }

    /**
     * 获取request条件下的数据
     * @param request 请求参数
     * @return 数据
     */
    public List<BaseTunnelResponseCache> selectAssetBaseData(AssetBaseDataRequest request, Long pageNum, Long pageSize){
        return baseMapper.selectAssetBaseData(request,pageNum,pageSize);
    }
}

