package com.ruoyi.other.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.other.domain.OtherTransportationAttachment;
import com.ruoyi.other.mapper.OtherTransportationAttachmentMapper;
import com.ruoyi.other.service.OtherTransportationAttachmentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 大件运输Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class OtherTransportationAttachmentServiceImpl extends ServiceImpl<OtherTransportationAttachmentMapper, OtherTransportationAttachment> implements OtherTransportationAttachmentService {
    @Resource
    private OtherTransportationAttachmentMapper otherTransportationAttachmentMapper;

    @Override
    public List<OtherTransportationAttachment> findListByParam(Map<String, Object> params) {
        return otherTransportationAttachmentMapper.findListByParam(params);
    }
}