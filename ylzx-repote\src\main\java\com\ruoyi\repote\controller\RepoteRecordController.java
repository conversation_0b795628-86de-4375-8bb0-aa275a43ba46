package com.ruoyi.repote.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.repote.domain.RepoteForm;
import com.ruoyi.repote.service.RepoteFormService;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.repote.domain.RepoteRecord;
import com.ruoyi.repote.service.RepoteRecordService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 填报记录Controller
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Api(tags = "填报记录" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/repoteRecord")
public class RepoteRecordController extends BaseController {
    @Resource
    private RepoteRecordService repoteRecordService;

    @Resource
    private RepoteFormService repoteFormService;


    /**
     * 查询填报记录列表(分页)
     */
    @ApiOperation("查询填报记录列表")
//    //@RequiresPermissions("repote:repoteRecord:list")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> params) {
        // 获取当前的用户
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        // 如果是超级管理员，则不过滤数据
        if (StringUtils.isNotNull(currentUser) && !currentUser.isAdmin()) {
            params.put("userId", currentUser.getUserId());
        }
        // 按照 form_name 升序，然后按照 expiry 升序
        params.put("order", "f.name asc, f.expiry asc");
        startPage();
        List<RepoteRecord> list = repoteRecordService.findListByParam(params);
        return getDataTable(list);
    }

    /**
     * 查询填报记录列表(不分页)
     */
    @ApiOperation("查询填报记录列表(不分页)")
//    //@RequiresPermissions("repote:repoteRecord:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<RepoteRecord> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<RepoteRecord> list = repoteRecordService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询填报记录数据
     */
    @ApiOperation("根据id查询填报记录数据")
//    //@RequiresPermissions("repote:repoteRecord:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            RepoteRecord repoteRecord = repoteRecordService.getById(id);
        if (repoteRecord == null) {
            return error("未查询到【填报记录】记录");
        }
        return success(repoteRecord);
    }

    /**
     * 新增填报记录
     */
    @ApiOperation("新增填报记录")
//    //@RequiresPermissions("repote:repoteRecord:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody RepoteRecord repoteRecord) {
        return toAjax(repoteRecordService.save(repoteRecord));
    }

    /**
     * 修改填报记录
     */
    @ApiOperation("修改填报记录")
//    //@RequiresPermissions("repote:repoteRecord:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody RepoteRecord repoteRecord) {
        boolean isMerge = false;
        // 更新填报记录 status 为 4 时, 为回退操作, 需要将 status 改为 2，并且合并 excel
        if(repoteRecord.getStatus() == 4){
            repoteRecord.setStatus(2);
            isMerge = true;
        }
        boolean update = repoteRecordService.updateById(repoteRecord);
        if(update&&isMerge){
            RepoteForm repoteForm = repoteFormService.getById(repoteRecord.getFormId());
            if(!repoteForm.getOwnerId().isEmpty()){
                try {
                    repoteFormService.mergeExcel(repoteForm);
                }catch (Exception e){
                    throw new ServiceException("合并excel失败");
                }
            }
        }
        return success(update);
    }

    /**
     * 删除填报记录
     */
    @ApiOperation("删除填报记录")
//    //@RequiresPermissions("repote:repoteRecord:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(repoteRecordService.removeById(id));
    }

    /**
     * 导出填报记录列表
     */
    @ApiOperation("导出填报记录列表")
//    //@RequiresPermissions("repote:repoteRecord:export")
    @Log(title = "填报记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<RepoteRecord> list = repoteRecordService.list();
        ExcelUtil<RepoteRecord> util = new ExcelUtil<RepoteRecord>(RepoteRecord.class);
        util.exportExcel(response, list, "填报记录数据");
    }


}
