package com.ruoyi.other.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 大件运输路段对象 other_transportation_road
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@ApiModel(value="大件运输路段")
@TableName("other_transportation_road")
@Data
public class OtherTransportationRoad extends BaseTableEntity {
    private static final long serialVersionUID = 1L;

    /** 大件运输ID */
    @Excel(name = "大件运输ID")
    @ApiModelProperty(value = "大件运输ID")
    private String transportationId;

    /** 养护路段Id */
    @Excel(name = "养护路段Id")
    @ApiModelProperty(value = "养护路段Id")
    private String roadId;

    /** 养护路段名称 */
    @Excel(name = "养护路段名称")
    @ApiModelProperty(value = "养护路段名称")
    private String roadName;

}
