package com.ruoyi.other.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.utils.ExcelUtilService;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 大件运输路段对象 other_transportation_attachment
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@ApiModel(value="大件运输附件")
@TableName("other_transportation_attachment")
@Data
public class OtherTransportationAttachment extends BaseTableEntity {
    private static final long serialVersionUID = 1L;

    /** 大件运输ID */
    @Excel(name = "大件运输ID")
    @ApiModelProperty(value = "大件运输ID")
    private String transportationId;

    /** 附件名称 */
    @Excel(name = "附件名称")
    @ApiModelProperty(value = "附件名称")
    private String name;

    /** 附件类型 */
    @Excel(name = "附件类型")
    @ApiModelProperty(value = "附件类型")
    private String attachmentType;


    /** 上传附件地址  */
    @Excel(name = "上传附件地址")
    @ApiModelProperty(value = "上传附件地址")
    private String url;
}
