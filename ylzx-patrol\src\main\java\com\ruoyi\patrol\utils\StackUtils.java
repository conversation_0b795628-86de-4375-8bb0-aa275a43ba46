package com.ruoyi.patrol.utils;

import java.math.BigDecimal;

public class StackUtils {
    /**
     * 格式化桩号
     *
     * @param stake 桩号
     * @return 格式化后的桩号
     */
    static public String formatStack(BigDecimal stake) {
        if (stake == null) {
            return "";
        }
        try {
            int meters = stake.intValue();
            int kilometers = meters / 1000;
            int remainingMeters = meters % 1000;
            return String.format("K%01d+%03d", kilometers, remainingMeters);
        } catch (Exception e) {
            return "";
        }
    }
}
