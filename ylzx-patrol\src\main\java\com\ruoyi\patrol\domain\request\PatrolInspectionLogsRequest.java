package com.ruoyi.patrol.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 巡查日志查询对象
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@ApiModel(value="巡查日志查询条件")
@Data
public class PatrolInspectionLogsRequest {
    
    @ApiModelProperty(value = "主键ID")
    private String id;
    
    @ApiModelProperty(value = "ID列表")
    private List<String> ids;
    
    @ApiModelProperty(value = "ID列表")
    private List<String> idList;
    
    @ApiModelProperty(value = "排除的ID列表")
    private List<String> idListNot;
    
    @ApiModelProperty(value = "巡查类型（1-日常巡查，2-夜间巡查）")
    private Integer patrolType;
    
    @ApiModelProperty(value = "巡查机构ID")
    private String patrolUnitId;
    
    @ApiModelProperty(value = "巡查机构名称")
    private String patrolUnitName;
    
    @ApiModelProperty(value = "管养单位ID")
    private String maintenanceUnitId;
    
    @ApiModelProperty(value = "养护路段ID")
    private String maintenanceSectionId;
    
    @ApiModelProperty(value = "养护路段名称(模糊查询)")
    private String maintenanceSectionName;
    
    @ApiModelProperty(value = "路线编码(模糊查询)")
    private String routeCode;
    
    @ApiModelProperty(value = "养护路段ID列表")
    private List<String> sectionIdList;
    
    @ApiModelProperty(value = "巡查方向（0-上行，1-下行，2-双向）")
    private Integer direction;
    
    @ApiModelProperty(value = "车牌号")
    private String carNum;
    
    @ApiModelProperty(value = "车牌号(模糊查询)")
    private String carNumLike;
    
    @ApiModelProperty(value = "用户名(模糊查询)")
    private String userNameLike;
    
    @ApiModelProperty(value = "天气")
    private String weather;
    
    @ApiModelProperty(value = "巡查内容")
    private String content;
    
    @ApiModelProperty(value = "巡查开始时间")
    private Date startTime;
    
    @ApiModelProperty(value = "巡查开始日期(开始日期)")
    private Date startTimee;
    
    @ApiModelProperty(value = "巡查开始日期(结束日期)")
    private Date startTimes;
    
    @ApiModelProperty(value = "巡查结束时间")
    private Date endTime;
    
    @ApiModelProperty(value = "上报时间")
    private Date reportedTime;
    
    @ApiModelProperty(value = "采集时间")
    private Date collectTime;
    
    @ApiModelProperty(value = "巡查状态（0-正在巡查，1-完成巡查）")
    private Integer status;
    
    @ApiModelProperty(value = "采集日期(开始日期)")
    private Date collectTimee;
    
    @ApiModelProperty(value = "采集日期(结束日期)")
    private Date collectTimes;
    
    @ApiModelProperty(value = "巡查里程")
    private BigDecimal patrolMileage;
    
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    @ApiModelProperty(value = "排序字段")
    private String order;
} 