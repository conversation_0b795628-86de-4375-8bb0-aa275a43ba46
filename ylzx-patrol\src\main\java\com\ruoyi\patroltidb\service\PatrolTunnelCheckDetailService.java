package com.ruoyi.patroltidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheckDetail;

import java.util.List;
import java.util.Map;

/**
 * 隧道巡检查子Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface PatrolTunnelCheckDetailService extends IService<PatrolTunnelCheckDetail> {

    /**
     * 根据条件查询隧道巡检查子数据列表
     * @param params
     */
    List<PatrolTunnelCheckDetail> findListByParam(Map<String, Object> params);

}
