package com.ruoyi.repote.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.repote.domain.RepotePerson;
import com.ruoyi.repote.service.RepotePersonService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 填报人员Controller
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Api(tags = "填报人员" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/repotePerson")
public class RepotePersonController extends BaseController {
    final private RepotePersonService repotePersonService;


    /**
     * 查询填报人员列表(分页)
     */
    @ApiOperation("查询填报人员列表")
    //@RequiresPermissions("repote:repotePerson:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<RepotePerson> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<RepotePerson> list = repotePersonService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询填报人员列表(不分页)
     */
    @ApiOperation("查询填报人员列表(不分页)")
    //@RequiresPermissions("repote:repotePerson:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<RepotePerson> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<RepotePerson> list = repotePersonService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询填报人员数据
     */
    @ApiOperation("根据id查询填报人员数据")
    //@RequiresPermissions("repote:repotePerson:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            RepotePerson repotePerson = repotePersonService.getById(id);
        if (repotePerson == null) {
            return error("未查询到【填报人员】记录");
        }
        return success(repotePerson);
    }

    /**
     * 新增填报人员
     */
    @ApiOperation("新增填报人员")
    //@RequiresPermissions("repote:repotePerson:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody RepotePerson repotePerson) {
        return toAjax(repotePersonService.save(repotePerson));
    }

    /**
     * 修改填报人员
     */
    @ApiOperation("修改填报人员")
    //@RequiresPermissions("repote:repotePerson:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody RepotePerson repotePerson) {
        return toAjax(repotePersonService.updateById(repotePerson));
    }

    /**
     * 删除填报人员
     */
    @ApiOperation("删除填报人员")
    //@RequiresPermissions("repote:repotePerson:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(repotePersonService.removeById(id));
    }

    /**
     * 导出填报人员列表
     */
    @ApiOperation("导出填报人员列表")
    //@RequiresPermissions("repote:repotePerson:export")
    @Log(title = "填报人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<RepotePerson> list = repotePersonService.list();
        ExcelUtil<RepotePerson> util = new ExcelUtil<RepotePerson>(RepotePerson.class);
        util.exportExcel(response, list, "填报人员数据");
    }


}
