package com.ruoyi.patrol.utils;

import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseCulvertResponseCache;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.mapstruct.CacheToBaseDomainMapper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 * 缓存对象转换为BaseDataDomain的工具类
 *
 * <AUTHOR>
 * @date 2025-04-19
 */
public class CacheToDomainUtils {

    private static final int PARALLEL_THRESHOLD = 1000;
    private static final int BATCH_SIZE = 2000;
    private static final ForkJoinPool COMMON_POOL = ForkJoinPool.commonPool();

    /**
     * 将缓存对象转换为BaseDataDomain
     *
     * @param cache 缓存对象
     * @return 转换后的BaseDataDomain对象
     */
    public static BaseDataDomain convertToDomain(BaseDataCache cache) {
        if (cache == null) {
            return null;
        }
        
        if (cache instanceof BaseBridgeResponseCache) {
            return CacheToBaseDomainMapper.INSTANCE.bridgeToBaseData((BaseBridgeResponseCache) cache);
        } else if (cache instanceof BaseCulvertResponseCache) {
            return CacheToBaseDomainMapper.INSTANCE.culvertToBaseData((BaseCulvertResponseCache) cache);
        } else if (cache instanceof BaseTunnelResponseCache) {
            return CacheToBaseDomainMapper.INSTANCE.tunnelToBaseData((BaseTunnelResponseCache) cache);
        }
        
        throw new IllegalArgumentException("不支持的缓存类型: " + cache.getClass().getName());
    }

    /**
     * 根据资产类型将缓存对象转换为BaseDataDomain
     *
     * @param cache     缓存对象
     * @param assetType 资产类型
     * @return 转换后的BaseDataDomain对象
     */
    public static BaseDataDomain convertToDomain(BaseDataCache cache, AssetType assetType) {
        if (cache == null) {
            return null;
        }
        
        switch (assetType) {
            case BRIDGE:
                return CacheToBaseDomainMapper.INSTANCE.bridgeToBaseData((BaseBridgeResponseCache) cache);
            case CULVERT:
                return CacheToBaseDomainMapper.INSTANCE.culvertToBaseData((BaseCulvertResponseCache) cache);
            case TUNNEL:
                return CacheToBaseDomainMapper.INSTANCE.tunnelToBaseData((BaseTunnelResponseCache) cache);
            default:
                throw new IllegalArgumentException("不支持的资产类型: " + assetType);
        }
    }

    /**
     * 批量转换桥梁缓存列表
     *
     * @param cacheList 桥梁缓存对象列表
     * @return BaseDataDomain对象列表
     */
    public static List<BaseDataDomain> convertBridgeList(List<BaseBridgeResponseCache> cacheList) {
        if (cacheList == null || cacheList.isEmpty()) {
            return Collections.emptyList();
        }

        int size = cacheList.size();
        if (size > PARALLEL_THRESHOLD) {
            return cacheList.parallelStream()
                    .map(cache -> CacheToBaseDomainMapper.INSTANCE.bridgeToBaseData(cache))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            return cacheList.stream()
                    .map(cache -> CacheToBaseDomainMapper.INSTANCE.bridgeToBaseData(cache))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 批量转换涵洞缓存列表
     *
     * @param cacheList 涵洞缓存对象列表
     * @return BaseDataDomain对象列表
     */
    public static List<BaseDataDomain> convertCulvertList(List<BaseCulvertResponseCache> cacheList) {
        if (cacheList == null || cacheList.isEmpty()) {
            return Collections.emptyList();
        }

        int size = cacheList.size();
        if (size > PARALLEL_THRESHOLD) {
            return cacheList.parallelStream()
                    .map(cache -> CacheToBaseDomainMapper.INSTANCE.culvertToBaseData(cache))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            return cacheList.stream()
                    .map(cache -> CacheToBaseDomainMapper.INSTANCE.culvertToBaseData(cache))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 批量转换隧道缓存列表
     *
     * @param cacheList 隧道缓存对象列表
     * @return BaseDataDomain对象列表
     */
    public static List<BaseDataDomain> convertTunnelList(List<BaseTunnelResponseCache> cacheList) {
        if (cacheList == null || cacheList.isEmpty()) {
            return Collections.emptyList();
        }

        int size = cacheList.size();
        if (size > PARALLEL_THRESHOLD) {
            return cacheList.parallelStream()
                    .map(cache -> CacheToBaseDomainMapper.INSTANCE.tunnelToBaseData(cache))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            return cacheList.stream()
                    .map(cache -> CacheToBaseDomainMapper.INSTANCE.tunnelToBaseData(cache))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 批量转换缓存对象列表为BaseDataDomain列表
     *
     * @param list      源列表
     * @param assetType 资产类型
     * @return 转换后的BaseDataDomain列表
     */
    public static List<BaseDataDomain> convertList(List<?> list, AssetType assetType) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        
        int size = list.size();
        if (size > PARALLEL_THRESHOLD) {
            return processBatchParallel(list, assetType);
        } else {
            return processSequential(list, assetType);
        }
    }

    /**
     * 批量转换任意缓存对象列表为BaseDataDomain列表
     *
     * @param list 源列表
     * @return 转换后的BaseDataDomain列表
     */
    public static List<BaseDataDomain> convertList(List<?> list) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        int size = list.size();
        if (size > PARALLEL_THRESHOLD) {
            return processBatchParallel(list);
        } else {
            return processSequential(list);
        }
    }
    
    /**
     * 兼容处理：为了替换原有代码中的BeanUtils.copyProperties方式
     * 此方法直接替代以下代码块:
     * <pre>
     * List<BaseDataDomain> baseDataDomainList = new ArrayList<>();
     * for (Object o : assetResponseList) {
     *     BaseDataDomain baseDataDomain = new BaseDataDomain();
     *     BeanUtils.copyProperties(o, baseDataDomain);
     *     baseDataDomainList.add(baseDataDomain);
     * }
     * </pre>
     * 
     * @param list 源列表（通常是baseCacheService.selectBaseDataResponseByAssetIds的返回值）
     * @return 转换后的BaseDataDomain列表
     */
    public static List<BaseDataDomain> convertListLegacy(List<?> list) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<BaseDataDomain> baseDataDomainList = new ArrayList<>(list.size());
        for (Object o : list) {
            if (o instanceof BaseDataCache) {
                // 如果是BaseDataCache对象，使用常规转换
                BaseDataDomain converted = convertToDomain((BaseDataCache) o);
                if (converted != null) {
                    baseDataDomainList.add(converted);
                }
            } else {
                // 否则使用BeanUtils兼容旧代码
                BaseDataDomain baseDataDomain = new BaseDataDomain();
                BeanUtils.copyProperties(o, baseDataDomain);
                baseDataDomainList.add(baseDataDomain);
            }
        }
        return baseDataDomainList;
    }

    /**
     * 并行处理大批量数据转换（基于资产类型）
     */
    private static List<BaseDataDomain> processBatchParallel(List<?> list, AssetType assetType) {
        int threads = Math.min(Runtime.getRuntime().availableProcessors(), 
                             (list.size() + BATCH_SIZE - 1) / BATCH_SIZE);
        
        try {
            return COMMON_POOL.submit(() ->
                list.parallelStream()
                    .filter(item -> item instanceof BaseDataCache)
                    .map(item -> convertToDomain((BaseDataCache) item, assetType))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList())
            ).get();
        } catch (Exception e) {
            throw new IllegalStateException("并行处理转换失败", e);
        }
    }

    /**
     * 并行处理大批量数据转换（自动判断类型）
     */
    private static List<BaseDataDomain> processBatchParallel(List<?> list) {
        int threads = Math.min(Runtime.getRuntime().availableProcessors(), 
                             (list.size() + BATCH_SIZE - 1) / BATCH_SIZE);
        
        try {
            return COMMON_POOL.submit(() ->
                list.parallelStream()
                    .filter(item -> item instanceof BaseDataCache)
                    .map(item -> convertToDomain((BaseDataCache) item))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList())
            ).get();
        } catch (Exception e) {
            throw new IllegalStateException("并行处理转换失败", e);
        }
    }

    /**
     * 顺序处理小批量数据转换（基于资产类型）
     */
    private static List<BaseDataDomain> processSequential(List<?> list, AssetType assetType) {
        List<BaseDataDomain> result = new ArrayList<>(list.size());
        for (Object item : list) {
            if (item instanceof BaseDataCache) {
                BaseDataDomain converted = convertToDomain((BaseDataCache) item, assetType);
                if (converted != null) {
                    result.add(converted);
                }
            }
        }
        return result;
    }

    /**
     * 顺序处理小批量数据转换（自动判断类型）
     */
    private static List<BaseDataDomain> processSequential(List<?> list) {
        List<BaseDataDomain> result = new ArrayList<>(list.size());
        for (Object item : list) {
            if (item instanceof BaseDataCache) {
                BaseDataDomain converted = convertToDomain((BaseDataCache) item);
                if (converted != null) {
                    result.add(converted);
                }
            }
        }
        return result;
    }
} 