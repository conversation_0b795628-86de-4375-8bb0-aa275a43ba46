package com.ruoyi.patrol.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 阶段(经常检查)0:在期,1:完成,2过期
 */
@Getter
@RequiredArgsConstructor
public enum StageType implements IEnum<Integer> {
    IN_PERIOD(0, "在期"),
    COMPLETED(1, "完成");
//    EXPIRED(2, "过期");

    private final Integer code;
    private final String description;

    @Override
    public Integer getValue() {
        return this.code;
    }

    @JsonValue
    public Integer getCode() {
        return this.code;
    }
    @JsonCreator
    public static StageType fromCode(Integer code) {
        for (StageType type : StageType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}
