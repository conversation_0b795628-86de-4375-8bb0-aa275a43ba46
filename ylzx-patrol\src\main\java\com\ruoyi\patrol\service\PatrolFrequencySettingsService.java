package com.ruoyi.patrol.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.manage.api.domain.bridge.BridgeStaticResponse;
import com.ruoyi.patrol.domain.PatrolFrequencySettings;
import com.ruoyi.patrol.enums.AssetType;

import java.util.List;
import java.util.Map;

/**
 * 巡查频率配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface PatrolFrequencySettingsService extends IService<PatrolFrequencySettings> {

    Long saveOrUpdatePlus(PatrolFrequencySettings settings);


    /**
     * 根据条件查询巡查频率配置数据列表
     * @param params
     */
    List<PatrolFrequencySettings> findListByParam(Map<String, Object> params);

    MTableDataInfo<List<BridgeStaticResponse>> getListMTableDataInfo(Map<String, Object> params);

    /**
     * 根据type查询
     * @param type 类型
     * @return List<PatrolFrequencySettings>
     */
    List<PatrolFrequencySettings> listByType(AssetType type);


    /**
     * 根据type查询
     * @param type 类型
     * @param flag true:dayFrequency(日常检查) false:monthFrequency(经常检查)
     * @return Map<String,Integer> assetId到频率的映射
     */
    Map<String,Integer> assetMapByType(AssetType type, Boolean flag);


    /**
     * 根据实体类查询
     * @param assetId 资产id
     * @param flag true:dayFrequency(日常检查) false:monthFrequency(经常检查)
     * @return Integer 周期时间
     */
    Integer getFrequencyByAssetId(String assetId,Integer type, Boolean flag);


    /**
     * 根据ids查询
     * @param assetIds 资产ids
     * @param type 类型 1-桥梁，2-隧道，3-涵洞
     * @param flag true:dayFrequency(日常检查) false:monthFrequency(经常检查)
     * @return Map<String,Integer> assetId到频率的映射
     */
    Map<String,Integer> assetMapByAssetIds(List<String> assetIds, Integer type, Boolean flag);

}
