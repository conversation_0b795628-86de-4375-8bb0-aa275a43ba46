package com.ruoyi.supervise.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.io.File;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.util.BytePictureUtils;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.apache.commons.io.FileUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.supervise.domain.SuperviseInspectionRecord;
import com.ruoyi.supervise.service.SuperviseInspectionRecordService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 督查记录Controller
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Api(tags = "督查记录")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/superviseRecord")
public class SuperviseInspectionRecordController extends BaseController {
    final private SuperviseInspectionRecordService superviseInspectionRecordService;
    final private RemoteDeptAuthService remoteDeptAuthService;
    final private RemoteUserService remoteUserService;
    final private RemoteFileService remoteFileService;

    /**
     * 查询督查记录列表(分页)
     */
    @ApiOperation("查询督查记录列表")
//    //@RequiresPermissions("supervise:superviseRecord:list")
    @GetMapping("/list")
    public TableDataInfo list(SuperviseInspectionRecord superviseInspectionRecord) {
        QueryWrapper<SuperviseInspectionRecord> qw = new QueryWrapper<>();
        qw.setEntity(superviseInspectionRecord);
        qw.and(superviseInspectionRecord.getRectifierIdOrSupervisorId() != null, q -> q.eq("supervisor_id", superviseInspectionRecord.getRectifierIdOrSupervisorId()).or().eq("rectifier_id", superviseInspectionRecord.getRectifierIdOrSupervisorId()));
        qw.in(superviseInspectionRecord.getStatusList() != null, "status", superviseInspectionRecord.getStatusList());
        qw.ge(superviseInspectionRecord.getInspectionStartTime() != null, "inspection_time", superviseInspectionRecord.getInspectionStartTime());
        qw.le(superviseInspectionRecord.getInspectionEndTime() != null, "inspection_time", superviseInspectionRecord.getInspectionEndTime());
        qw.orderByDesc("create_time");
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (!loginUser.getSysUser().isAdmin()) {
//            Long deptId = loginUser.getSysUser().getDeptId();
//            qw.and( q ->q.eq("inspection_unit_id",deptId).or().eq("inspected_unit_id",deptId));
            R<List<SysDept>> deptList = remoteDeptAuthService.getDeptList();
            List<Long> collect = deptList.getData().stream().map(SysDept::getDeptId).collect(Collectors.toList());
            if (collect.size() == 0) {
                collect.add(999999999999L);
            }
            qw.and(q -> q.in("inspection_unit_id", collect).or().in("inspected_unit_id", collect));
        }
        startPage();
        List<SuperviseInspectionRecord> list = superviseInspectionRecordService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询督查记录列表(不分页)
     */
    @ApiOperation("查询督查记录列表(不分页)")
//    //@RequiresPermissions("supervise:superviseRecord:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll(SuperviseInspectionRecord superviseInspectionRecord) {
        QueryWrapper<SuperviseInspectionRecord> qw = new QueryWrapper<>();
        qw.setEntity(superviseInspectionRecord);
        qw.orderByDesc("create_time");
        qw.and(superviseInspectionRecord.getRectifierIdOrSupervisorId() != null, q -> q.eq("supervisorId", superviseInspectionRecord.getRectifierIdOrSupervisorId()).or().eq("rectifierId", superviseInspectionRecord.getRectifierIdOrSupervisorId()));
        qw.ge(superviseInspectionRecord.getInspectionStartTime() != null, "inspection_time", superviseInspectionRecord.getInspectionStartTime());
        qw.le(superviseInspectionRecord.getInspectionEndTime() != null, "inspection_time", superviseInspectionRecord.getInspectionEndTime());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (!loginUser.getSysUser().isAdmin()) {
//            Long deptId = loginUser.getSysUser().getDeptId();
//            qw.and( q ->q.eq("inspection_unit_id",deptId).or().eq("inspected_unit_id",deptId));
            R<List<SysDept>> deptList = remoteDeptAuthService.getDeptList();
            List<Long> collect = deptList.getData().stream().map(item -> item.getDeptId()).collect(Collectors.toList());
            qw.and(q -> q.in("inspection_unit_id", collect).or().in("inspected_unit_id", collect));

        }
        List<SuperviseInspectionRecord> list = superviseInspectionRecordService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询督查记录数据
     */
    @ApiOperation("根据id查询督查记录数据")
//    //@RequiresPermissions("supervise:superviseRecord:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        SuperviseInspectionRecord superviseInspectionRecord = superviseInspectionRecordService.getById(id);
        if (superviseInspectionRecord == null) {
            return error("未查询到【督查记录】记录");
        }
        return success(superviseInspectionRecord);
    }

    /**
     * 新增督查记录
     */
    @ApiOperation("新增督查记录")
//    //@RequiresPermissions("supervise:superviseRecord:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody SuperviseInspectionRecord superviseInspectionRecord) {
        boolean save = superviseInspectionRecordService.save(superviseInspectionRecord);
        return success(save ? superviseInspectionRecord : null);
    }

    /**
     * 修改督查记录
     */
    @ApiOperation("修改督查记录")
//    //@RequiresPermissions("supervise:superviseRecord:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody SuperviseInspectionRecord superviseInspectionRecord) {
        boolean b = superviseInspectionRecordService.updateById(superviseInspectionRecord);
        return b ? success(superviseInspectionRecord) : error("更新失败");
    }

    /**
     * 删除督查记录
     */
    @ApiOperation("删除督查记录")
//    //@RequiresPermissions("supervise:superviseRecord:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(superviseInspectionRecordService.removeById(id));
    }

    /**
     * 导出督查记录列表
     */
    @ApiOperation("导出督查记录列表")
//    //@RequiresPermissions("supervise:superviseRecord:export")
    @Log(title = "督查记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SuperviseInspectionRecord superviseInspectionRecord) {
//    public void export(HttpServletResponse response,  String[] ids) {
        QueryWrapper<SuperviseInspectionRecord> qw = new QueryWrapper<>();
        qw.setEntity(superviseInspectionRecord);
        qw.in(CollectionUtil.isNotEmpty(superviseInspectionRecord.getIds()), "id", superviseInspectionRecord.getIds());
        qw.orderByDesc("create_time");
        List<SuperviseInspectionRecord> list = superviseInspectionRecordService.list(qw);
        ExcelUtil<SuperviseInspectionRecord> util = new ExcelUtil<SuperviseInspectionRecord>(SuperviseInspectionRecord.class);
        util.exportExcel(response, list, "督查记录数据");
    }


    /**
     * 下载督查记录doc
     */
    @ApiOperation("下载督查记录文档")
//    //@RequiresPermissions("supervise:superviseRecord:export")
    @GetMapping("/download/{id}")
    public void download(@PathVariable String id, HttpServletResponse response) throws IOException {

        SuperviseInspectionRecord superviseInspectionRecord = superviseInspectionRecordService.getById(id);
        if (superviseInspectionRecord == null) {
            return;
        }
        Map<String, Object> data = toMap(superviseInspectionRecord);

        if (StringUtils.isNotBlank(superviseInspectionRecord.getSupervisorId())) {
            R<List<SysUser>> superviseUser = remoteUserService.findListParam(new ArrayList<>() {{
                add(Long.valueOf(superviseInspectionRecord.getSupervisorId()));
            }});
            List<String> superviseIdSign = superviseUser.getData().stream().map(SysUser::getSignId).collect(Collectors.toList());
            R<SysFile> file1 = remoteFileService.getFile(superviseIdSign.get(0));

            if (file1.getData() != null)
                data.put("superviseUser", new PictureRenderData(90, 30, ".png", BytePictureUtils.getUrlBufferedImage(file1.getData().getUrl())));
        }

        if (StringUtils.isNotBlank(superviseInspectionRecord.getRectifierId())) {
            R<List<SysUser>> rectifierUser = remoteUserService.findListParam(new ArrayList<>() {{
                add(Long.valueOf(superviseInspectionRecord.getRectifierId()));
            }});
            List<String> rectifierUserSign = rectifierUser.getData().stream().map(SysUser::getSignId).collect(Collectors.toList());
            R<SysFile> file1 = remoteFileService.getFile(rectifierUserSign.get(0));
            if (file1.getData() != null)
                data.put("rectifierUser", new PictureRenderData(90, 30, ".png", BytePictureUtils.getUrlBufferedImage(file1.getData().getUrl())));

        }


        if (StringUtils.isNotBlank(superviseInspectionRecord.getInspectionUserIds())) {
            R<List<SysUser>> inspectionUser = remoteUserService.findListParam(Arrays.stream(superviseInspectionRecord.getInspectionUserIds().split(",")).map(item -> Long.valueOf(item)).collect(Collectors.toList()));
            List<String> inspectionUserSign = inspectionUser.getData().stream().map(SysUser::getSignId).collect(Collectors.toList());
            List<PictureRenderData> imageList = new ArrayList<>();
            for (String tempSign : inspectionUserSign) {
                R<SysFile> tempFile = remoteFileService.getFile(tempSign);
                if (tempFile.getData() != null)
                    imageList.add(new PictureRenderData(90, 30, ".png", BytePictureUtils.getUrlBufferedImage(tempFile.getData().getUrl())));
            }
            List<Map<String, PictureRenderData>> images = new ArrayList<>();
            for (PictureRenderData image : imageList) {
                Map<String, PictureRenderData> item = new HashMap<>();
                item.put("picture", image);
                images.add(item);
            }
            data.put("images", images);

        }
        String fileName = String.format("运营管理（督查）检查工作记录表（%s）.docx", superviseInspectionRecord.getInspectedUnitName());
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        InputStream stream = getClass().getClassLoader().getResourceAsStream("template/word/运营管理（督查）检查工作记录表.docx");
        File file = new File("tempWord.docx");
        if (stream != null) {
            FileUtils.copyInputStreamToFile(stream, file);
            stream.close();
        }
        String filePath = file.getAbsolutePath();
        XWPFTemplate template = XWPFTemplate.compile(filePath).render(data);
        ServletOutputStream outputStream = response.getOutputStream();
        template.write(outputStream);
        template.close();
        file.delete();
    }


    private static Map<String, Object> toMap(Object obj) {
        Map<String, Object> queryParams = new HashMap<>();
        for (Field field : obj.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                Object value = field.get(obj);
                if (value != null) {
                    queryParams.put(field.getName(), value);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return queryParams;
    }
}
