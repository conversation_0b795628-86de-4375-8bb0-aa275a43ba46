package com.ruoyi.patrol.processor;


import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import com.ruoyi.patrol.domain.PatrolPartsIgnore;
import com.ruoyi.patrol.domain.PatrolPartsInfo;
import com.ruoyi.patrol.enums.DeleteFlagType;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.service.PatrolPartsIgnoreService;
import com.ruoyi.patrol.service.PatrolPartsInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 巡检资产检查处理器
 * 负责处理巡检资产检查列表及其明细的相关操作
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PatrolAssetCheckProcessor {

    private final PatrolPartsInfoService patrolPartsInfoService;
    private final PatrolPartsIgnoreService patrolPartsIgnoreService;

    @Qualifier("taskExecutor")
    private final Executor taskExecutor; // 注入配置好的线程池

    // 触发并行计算的阈值，可以根据实际性能测试调整
    private static final int PARALLEL_THRESHOLD = 100;
    // 并行处理的总超时时间（秒），根据预期的最长处理时间设定
    private static final long PROCESSING_TIMEOUT_SECONDS = 60;
    // 批处理大小，将大量记录分成多少批
    private static final int BATCH_SIZE = 8;

    /**
     * 处理巡检资产检查列表，对每个检查对象的明细列表进行排序和调整。
     * 假设列表中的所有项都属于同一个 conceptual AssetType。
     *
     * @param patrolAssetCheckList 需要处理的 PatrolAssetCheck 列表。
     * @param type                 用于获取标准 PatrolPartsInfo 的资产类型。
     * @param <T>                  继承自 PatrolAssetCheck 的类型。
     */
    public <T extends PatrolAssetCheck> void processPatrolAssetCheckList(List<T> patrolAssetCheckList, InspectionType type) {
        processPatrolAssetCheckList(patrolAssetCheckList, type, true);
    }

    /**
     * 处理巡检资产检查列表，对每个检查对象的明细列表进行排序和调整。
     * 当列表大小超过 PARALLEL_THRESHOLD 时，使用批量并行处理；否则顺序处理。
     * 并行处理有超时保护，超时后取消未完成子任务。
     *
     * @param patrolAssetCheckList 需要处理的 PatrolAssetCheck 列表。
     * @param type                 用于获取标准 PatrolPartsInfo 的资产类型。
     * @param addSortPrefix        是否添加序号前缀到 partsTypeName。
     * @param <T>                  继承自 PatrolAssetCheck 的类型。
     */
    public <T extends PatrolAssetCheck> void processPatrolAssetCheckList(
            List<T> patrolAssetCheckList,
            InspectionType type,
            boolean addSortPrefix) {
        if (patrolAssetCheckList == null || patrolAssetCheckList.isEmpty()) {
            log.debug("输入列表为空，跳过处理");
            return;
        }
        // 1. 预获取共享只读数据
        List<PatrolPartsInfo> sortedPartsInfos = getSortedPatrolPartsInfo(type);
        if (sortedPartsInfos.isEmpty()) {
            log.warn("未加载到类型 {} 的基准检查项，跳过处理", type);
            return;
        }
        Map<String, Set<String>> ignoresByAssetId = getIgnoresByAssetId(patrolAssetCheckList);
        int size = patrolAssetCheckList.size();
        log.info("开始处理 {} 条记录 (type={}, prefix={})", size, type, addSortPrefix);
        if (size > PARALLEL_THRESHOLD) {
            log.info("记录数 {} > 阈值 {}，启用并行批处理 (timeout={}s)", size, PARALLEL_THRESHOLD, PROCESSING_TIMEOUT_SECONDS);
            
            // 计算每批的大小，确保至少有BATCH_SIZE批，但每批不少于10个条目
            int actualBatchSize = Math.max(10, (size + BATCH_SIZE - 1) / BATCH_SIZE);
            int batchCount = (size + actualBatchSize - 1) / actualBatchSize;
            
            log.info("将 {} 条记录分为 {} 批，每批约 {} 条", size, batchCount, actualBatchSize);
            
            // 2. 并行提交批处理任务
            List<CompletableFuture<Void>> futures = new ArrayList<>(batchCount);
            
            for (int i = 0; i < batchCount; i++) {
                final int fromIndex = i * actualBatchSize;
                final int toIndex = Math.min(fromIndex + actualBatchSize, size);
                final List<T> batch = patrolAssetCheckList.subList(fromIndex, toIndex);
                
                futures.add(CompletableFuture.runAsync(() -> {
                    log.debug("开始处理批次 {}/{} (记录 {}-{})", 
                             (fromIndex / actualBatchSize) + 1, batchCount, 
                             fromIndex + 1, toIndex);
                    try {
                        // 批量处理记录
                        for (T item : batch) {
                            try {
                                processSinglePatrolAssetCheckInternal(item, sortedPartsInfos, ignoresByAssetId, addSortPrefix);
                            } catch (Exception e) {
                                log.error("批内处理异常, assetId={}, 已捕获并继续下一条", item.getAssetId(), e);
                            }
                        }
                        log.debug("完成处理批次 {}/{} (记录 {}-{})", 
                                 (fromIndex / actualBatchSize) + 1, batchCount, 
                                 fromIndex + 1, toIndex);
                    } catch (Exception e) {
                        log.error("批次 {}/{} 处理异常", (fromIndex / actualBatchSize) + 1, batchCount, e);
                    }
                }, taskExecutor));
            }
            
            // 3. allOf + orTimeout + exceptionally 做统一超时/异常处理
            CompletableFuture<Void> all = CompletableFuture
                    .allOf(futures.toArray(new CompletableFuture[0]))
                    .orTimeout(PROCESSING_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                    .exceptionally(ex -> {
                        if (ex instanceof TimeoutException) {
                            log.error("并行批处理超时 {}s，尝试取消所有任务", PROCESSING_TIMEOUT_SECONDS, ex);
                        } else {
                            log.error("并行批处理出现异常", ex);
                        }
                        futures.forEach(f -> f.cancel(true));
                        return null;
                    });
            // 等待完成或超时完成
            all.join();
            log.info("并行批处理结束 (共 {} 条, {} 批)", size, batchCount);
        } else {
            log.info("记录数 {} <= 阈值 {}，顺序执行", size, PARALLEL_THRESHOLD);
            // 4. 顺序处理
            for (T item : patrolAssetCheckList) {
                try {
                    processSinglePatrolAssetCheckInternal(item, sortedPartsInfos, ignoresByAssetId, addSortPrefix);
                } catch (Exception e) {
                    log.error("assetId={} 顺序处理异常，已捕获并继续", item.getAssetId(), e);
                }
            }
            log.info("顺序处理结束 (共 {} 条)", size);
        }
    }


    /**
     * 处理单个 PatrolAssetCheck 对象，对其明细列表进行排序和调整。
     * 这是处理单个对象的公共方法。
     *
     * @param patrolAssetCheck 需要处理的对象。
     * @param type             用于获取标准 PatrolPartsInfo 的资产类型。
     * @param <T>              继承自 PatrolAssetCheck 的类型。
     */
    public <T extends PatrolAssetCheck> void processSinglePatrolAssetCheck(T patrolAssetCheck, InspectionType type) {
        processSinglePatrolAssetCheck(patrolAssetCheck, type, true);
    }

    /**
     * 处理单个 PatrolAssetCheck 对象，对其明细列表进行排序和调整。
     * 这是处理单个对象的公共方法。
     *
     * @param patrolAssetCheck 需要处理的对象。
     * @param type             用于获取标准 PatrolPartsInfo 的资产类型。
     * @param addSortPrefix    是否添加序号前缀到 partsTypeName。
     * @param <T>              继承自 PatrolAssetCheck 的类型。
     */
    public <T extends PatrolAssetCheck> void processSinglePatrolAssetCheck(T patrolAssetCheck, InspectionType type, boolean addSortPrefix) {
        if (patrolAssetCheck == null) {
            log.warn("无法处理 null 的 PatrolAssetCheck 对象。");
            return;
        }
        if (patrolAssetCheck.getAssetId() == null) {
            log.warn("无法处理 assetId 为 null 的 PatrolAssetCheck。检查对象 ID (如果可用): {}", patrolAssetCheck.getId());
            // 根据业务，这里可能需要抛出异常或采取其他措施
            return;
        }
        log.info("正在处理单个 PatrolAssetCheck。资产 ID: {}, 类型: {}", patrolAssetCheck.getAssetId(), type);
        // 1. 获取标准的、按 'sort' 排序的检查项信息
        List<PatrolPartsInfo> sortedPartsInfos = getSortedPatrolPartsInfo(type);
        if (sortedPartsInfos.isEmpty()) {
            log.warn("未找到类型 {} 对应的 PatrolPartsInfo。无法处理资产 {} 的明细。", type, patrolAssetCheck.getAssetId());
            // 可选：清空现有明细: patrolAssetCheck.setPatrolCheckDetailList(new ArrayList<>());
            return;
        }
        // 2. 专门为此资产获取不检查项信息
        Map<String, Set<String>> ignoresByAssetId = getIgnoresByAssetId(Collections.singletonList(patrolAssetCheck));
        // 3. 使用内部辅助方法处理单个对象
        processSinglePatrolAssetCheckInternal(patrolAssetCheck, sortedPartsInfos, ignoresByAssetId, addSortPrefix);
        log.info("已完成处理单个 PatrolAssetCheck。资产 ID: {}", patrolAssetCheck.getAssetId());
    }
    
    /**
     * 获取并按 'sort' 字段排序 PatrolPartsInfo。
     * @param type 资产类型
     * @return 排序后的 PatrolPartsInfo 列表，如果查询结果为 null 则返回空列表。
     */
    private List<PatrolPartsInfo> getSortedPatrolPartsInfo(InspectionType type) {
        log.debug("正在为类型 {} 获取 PatrolPartsInfo。", type);
        List<PatrolPartsInfo> patrolPartsInfos = patrolPartsInfoService.selectByType(type);
        if (patrolPartsInfos == null || patrolPartsInfos.isEmpty()) {
            log.debug("未找到类型 {} 的 PatrolPartsInfo 或查询返回 null。", type);
            return Collections.emptyList();
        }
        // 根据 'sort' 字段排序，将 null 排在最后
        patrolPartsInfos.sort(Comparator.comparing(PatrolPartsInfo::getSort, Comparator.nullsLast(Comparator.naturalOrder())));
        log.debug("为类型 {} 找到并排序了 {} 个 PatrolPartsInfo 项目。", type, patrolPartsInfos.size());
        return patrolPartsInfos;
    }
    
    /**
     * 获取不检查项信息，并按资产 ID 分组。
     * @param patrolAssetCheckList 包含需要查询不检查项的资产的列表。
     * @param <T>                  继承自 PatrolAssetCheck 的类型。
     * @return Map，键是 assetId，值是该 assetId 对应的所有不检查项的 partsId 集合。
     */
    private <T extends PatrolAssetCheck> Map<String, Set<String>> getIgnoresByAssetId(List<T> patrolAssetCheckList) {
        // 提取有效且不重复的 assetId
        List<String> assetIds = patrolAssetCheckList.stream()
                .map(PatrolAssetCheck::getAssetId)
                .filter(Objects::nonNull) // 过滤掉 assetId 为 null 的情况
                .distinct()
                .toList(); // Java 17+
        if (assetIds.isEmpty()) {
            log.debug("在输入列表中未找到有效的 assetId。返回空的不检查项映射。");
            return Collections.emptyMap();
        }
        log.debug("正在为 {} 个不同的 assetId 获取 PatrolPartsIgnore。", assetIds.size());
        List<PatrolPartsIgnore> allIgnores = patrolPartsIgnoreService.selectByAssetIds(assetIds);
        if (allIgnores == null || allIgnores.isEmpty()) {
            log.debug("未找到给定 assetId 对应的 PatrolPartsIgnore 或查询返回 null。");
            return Collections.emptyMap();
        }
        // 按 assetId 分组，值为 partsId 的集合
        Map<String, Set<String>> ignoresMap = allIgnores.stream()
                .filter(ignore -> ignore.getAssetId() != null && ignore.getPartsId() != null) // 过滤掉无效数据
                .collect(Collectors.groupingBy(
                        PatrolPartsIgnore::getAssetId,
                        Collectors.mapping(PatrolPartsIgnore::getPartsId, Collectors.toSet())
                ));
        log.debug("已将不检查项分组到包含 {} 个 assetId 键的 Map 中。", ignoresMap.size());
        return ignoresMap;
    }
    
    /**
     * 单个 PatrolAssetCheck 的核心处理逻辑（线程安全，可并行调用）。
     * 将传入对象的 patrolCheckDetailList 全量替换为按 partsInfo 顺序并考虑 ignore 标记后的新列表。
     *
     * @param patrolAssetCheck   需要修改的对象。
     * @param sortedPartsInfos   预先获取并排序的标准检查项列表。
     * @param ignoresByAssetId   预先获取的按 assetId 分组的不检查项 partsId 集合。
     * @param addSortPrefix      是否添加序号前缀到 partsTypeName。
     * @param <T>                继承自 PatrolAssetCheck 的类型。
     */
    private <T extends PatrolAssetCheck> void processSinglePatrolAssetCheckInternal(
            T patrolAssetCheck,
            List<PatrolPartsInfo> sortedPartsInfos,
            Map<String, Set<String>> ignoresByAssetId,
            boolean addSortPrefix) {
        String assetId = patrolAssetCheck.getAssetId();
        if (assetId == null) {
            log.warn("发现 assetId=null，跳过该项");
            return;
        }
        log.trace("[{}] 开始处理", assetId);
        // 已有明细按 partsTypeId 建立 Map
        Map<String, PatrolAssetCheckDetail> existingMap = Optional.ofNullable(patrolAssetCheck.getPatrolCheckDetailList())
                .map(List::stream)
                .orElseGet(Stream::empty)
                .filter(d -> d != null && d.getPartsTypeId() != null)
                .collect(Collectors.toMap(
                        PatrolAssetCheckDetail::getPartsTypeId,
                        Function.identity(),
                        (first, second) -> {
                            log.warn("[{}] 重复 partsTypeId={}，保留首次", assetId, first.getPartsTypeId());
                            return first;
                        }
                ));
        Set<String> ignoredParts = ignoresByAssetId.getOrDefault(assetId, Collections.emptySet());
        List<PatrolAssetCheckDetail> newList = new ArrayList<>(sortedPartsInfos.size());
        // 按基准列表顺序组装新明细
        for (PatrolPartsInfo info : sortedPartsInfos) {
            String pid = info.getId();
            if (pid == null) {
                log.warn("[{}] 跳过 null id 的 partsInfo", assetId);
                continue;
            }
            String name = Optional.ofNullable(info.getPartsName()).orElse("");
            String title = addSortPrefix
                    ? (info.getSort() != null ? info.getSort() : "N/A") + " " + name
                    : name;
            if (ignoredParts.contains(pid)) {
                // 不检查项
                PatrolAssetCheckDetail d = new PatrolAssetCheckDetail();
                d.setCheckId(patrolAssetCheck.getId());
                d.setPartsTypeId(pid);
                d.setPartsTypeName(title);
                d.setDes("/");
                d.setDefect("/");
                d.setAdvice("/");
                d.setDelFlag(DeleteFlagType.NOT_DELETED);
                d.setIgnore(true);
                newList.add(d);
            } else {
                // 标准项：复用 or 新建
                PatrolAssetCheckDetail exist = existingMap.get(pid);
                if (exist != null) {
                    exist.setPartsTypeName(title);
                    if(exist.getDes() == null || exist.getDes().trim().isEmpty()) {
                        if(info.getDes() == null || info.getDes().trim().isEmpty()) {
                            exist.setDes("无");
                        } else {
                            exist.setDes(info.getDes());
                        }
                    }
                    if(exist.getDefect() == null || exist.getDefect().trim().isEmpty()) {
                        exist.setDefect("未见异常");
                    }
                    if(exist.getAdvice() == null || exist.getAdvice().trim().isEmpty()) {
                        exist.setAdvice("正常保养");
                    }
                    exist.setIgnore(false);
                    newList.add(exist);
                } else {
                    PatrolAssetCheckDetail d = new PatrolAssetCheckDetail();
                    d.setCheckId(patrolAssetCheck.getId());
                    d.setPartsTypeId(pid);
                    d.setPartsTypeName(title);
                    if(info.getDes() == null || info.getDes().trim().isEmpty()) {
                        d.setDes("无");
                    } else {
                        d.setDes(info.getDes());
                    }
                    d.setDefect("未见异常");
                    d.setAdvice("正常保养");
                    d.setDelFlag(DeleteFlagType.NOT_DELETED);
                    d.setIgnore(false);
                    newList.add(d);
                }
            }
        }
        // 替换明细
        patrolAssetCheck.setPatrolCheckDetailList(newList);
        log.trace("[{}] 完成替换，共 {} 条", assetId, newList.size());
    }

    /**
     * 对 patrolAssetCheckList 进行处理，分析并设置其中的 oprUserSignList，同时收集 names 与 ids。
     *
     * @param patrolAssetCheckList 包含 PatrolAssetCheck 子类对象的集合
     * @param names                收集姓名的 Set
     * @param ids                  收集 id 的 Set
     * @param <T>                  必须是 PatrolAssetCheck 或其子类型
     */
    public <T extends PatrolAssetCheck> void processAndPopulateOprUserSignList(
            List<T> patrolAssetCheckList, Set<String> names, Set<String> ids) {
        // 1. 输入参数校验
        if (patrolAssetCheckList == null || names == null || ids == null) {
            System.err.println("输入列表或集合不能为 null。");
            // 或者可以抛出 IllegalArgumentException
            // throw new IllegalArgumentException("Input list or sets cannot be null.");
            return;
        }
        for (T check : patrolAssetCheckList) {
            if (check == null) { // 增加对列表中元素自身的 null 检查
                continue;
            }
            String oprUserSign = check.getOprUserSign();
            String oprUserName = check.getOprUserName();
            // 2. 明确创建结果列表
            List<String> resultSignList = new ArrayList<>();
            boolean signIsBlank = (oprUserSign == null || oprUserSign.isBlank());
            boolean nameIsBlank = (oprUserName == null || oprUserName.isBlank());
            // 情况1: 两者都为空
            if (signIsBlank && nameIsBlank) {
                check.setOprUserSignList(resultSignList); // 设置空列表
                continue;
            }
            // 情况2 & 3: 一个为空一个不为空
            if (signIsBlank && !nameIsBlank) { // 只有 name 不为空
                List<String> nameItems = splitAndFilter(oprUserName);
                resultSignList.addAll(nameItems);
                names.addAll(nameItems);
            } else if (!signIsBlank && nameIsBlank) { // 只有 sign 不为空
                List<String> signItems = splitAndFilter(oprUserSign);
                resultSignList.addAll(signItems);
                ids.addAll(signItems);
            } else {
                // 情况4: 两者都不为空
                List<String> signList = Arrays.stream(oprUserSign.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList()); // 使用可变列表更安全
                List<String> nameList = Arrays.stream(oprUserName.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList()); // 使用可变列表更安全
                // 情况4a: size 不等
                if (signList.size() != nameList.size()) {
                    List<String> filteredSignList = splitAndFilter(oprUserSign); // 直接用辅助方法更一致
                    resultSignList.addAll(filteredSignList);
                    ids.addAll(filteredSignList);
                } else {
                    // 情况4b: size 相等
                    for (int i = 0; i < signList.size(); i++) {
                        String signToken = signList.get(i);
                        String nameToken = nameList.get(i);
                        boolean isSignTokenInvalid = (signToken == null || signToken.isBlank() || "null".equalsIgnoreCase(signToken));
                        if (isSignTokenInvalid) {
                            // sign 无效，尝试用 name
                            // 检查 name 是否有效（非 blank/null/"null" - 根据需求，name "null" 字面量也可能需要过滤）
                            if (nameToken != null && !nameToken.isBlank() && !"null".equalsIgnoreCase(nameToken)) {
                                resultSignList.add(nameToken);
                                names.add(nameToken);
                            }
                            // 如果 name 也无效，则此项忽略，不添加到 resultSignList
                        } else {
                            // sign 有效
                            resultSignList.add(signToken);
                            ids.add(signToken);
                        }
                    }
                }
            }
            // 设置最终处理好的列表
            check.setOprUserSignList(resultSignList);
        }
    }

    /**
     * 按逗号分割字符串，去除首尾空格，并过滤掉 null、空串或"null"（忽略大小写）的项
     *
     * @param input 待分割的字符串
     * @return 处理后的有效字符串 List
     */
    public static List<String> splitAndFilter(String input) {
        if (input == null || input.isBlank()) {
            return Collections.emptyList(); // 返回不可变空列表
        }
        return Arrays.stream(input.split(","))
                .map(String::trim) // 去除首尾空格
                .filter(s -> s != null && !s.isBlank() && !"null".equalsIgnoreCase(s)) // 过滤 null, 空白, "null"(忽略大小写)
                .collect(Collectors.toCollection(ArrayList::new)); // 收集到 ArrayList
    }

    /**
     * 处理巡检资产中的负责人签名信息，分析并设置其中的 kahunaSignList，同时收集 names 与 ids。
     * 
     * @param patrolAssetCheckList 包含 PatrolAssetCheck 子类对象的集合
     * @param names                收集负责人姓名的 Set
     * @param ids                  收集负责人 id 的 Set
     * @param <T>                  必须是 PatrolAssetCheck 或其子类型
     */
    public <T extends PatrolAssetCheck> void processAndPopulateKahunaSignLists(
            List<T> patrolAssetCheckList, Set<String> names, Set<String> ids) {
        // 1. 输入参数校验
        if (patrolAssetCheckList == null || names == null || ids == null) {
            log.error("处理负责人签名信息失败：输入列表或集合不能为 null。");
            return;
        }
        
        for (T check : patrolAssetCheckList) {
            if (check == null) { // 增加对列表中元素自身的 null 检查
                continue;
            }
            
            String kahunaSign = check.getKahunaSign();
            String kahunaName = check.getKahunaName();
            
            // 2. 明确创建结果列表
            List<String> resultSignList = new ArrayList<>();
            boolean signIsBlank = (kahunaSign == null || kahunaSign.isBlank());
            boolean nameIsBlank = (kahunaName == null || kahunaName.isBlank());
            
            // 情况1: 两者都为空
            if (signIsBlank && nameIsBlank) {
                check.setKahunaSignList(resultSignList); // 设置空列表
                continue;
            }
            
            // 情况2 & 3: 一个为空一个不为空
            if (signIsBlank && !nameIsBlank) { // 只有 name 不为空
                List<String> nameItems = splitAndFilter(kahunaName);
                resultSignList.addAll(nameItems);
                names.addAll(nameItems);
            } else if (!signIsBlank && nameIsBlank) { // 只有 sign 不为空
                List<String> signItems = splitAndFilter(kahunaSign);
                resultSignList.addAll(signItems);
                ids.addAll(signItems);
            } else {
                // 情况4: 两者都不为空
                List<String> signList = Arrays.stream(kahunaSign.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList()); // 使用可变列表更安全
                List<String> nameList = Arrays.stream(kahunaName.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList()); // 使用可变列表更安全
                
                // 情况4a: size 不等
                if (signList.size() != nameList.size()) {
                    List<String> filteredSignList = splitAndFilter(kahunaSign); // 直接用辅助方法更一致
                    resultSignList.addAll(filteredSignList);
                    ids.addAll(filteredSignList);
                } else {
                    // 情况4b: size 相等
                    for (int i = 0; i < signList.size(); i++) {
                        String signToken = signList.get(i);
                        String nameToken = nameList.get(i);
                        boolean isSignTokenInvalid = (signToken == null || signToken.isBlank() || "null".equalsIgnoreCase(signToken));
                        
                        if (isSignTokenInvalid) {
                            // sign 无效，尝试用 name
                            // 检查 name 是否有效（非 blank/null/"null"）
                            if (nameToken != null && !nameToken.isBlank() && !"null".equalsIgnoreCase(nameToken)) {
                                resultSignList.add(nameToken);
                                names.add(nameToken);
                            }
                            // 如果 name 也无效，则此项忽略，不添加到 resultSignList
                        } else {
                            // sign 有效
                            resultSignList.add(signToken);
                            ids.add(signToken);
                        }
                    }
                }
            }
            
            // 设置最终处理好的列表
            check.setKahunaSignList(resultSignList);
        }
    }
} 