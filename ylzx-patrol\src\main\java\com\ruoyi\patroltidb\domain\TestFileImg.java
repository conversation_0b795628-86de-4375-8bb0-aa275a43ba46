package com.ruoyi.patroltidb.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
//@Accessors(chain = true)
@TableName("system_group_t_userimg_v_0")
public class TestFileImg extends BaseTableEntity {

    @TableId(value = "id0")
    private Long id0;

    @ApiModelProperty(value = "数据ID")
    @TableId(value = "data_id")
    private String dataId;

    @ApiModelProperty(value = "数据类型（）")
    @TableId(value = "work_type")
    private String workType;

    @ApiModelProperty(value = "图片地址")
    @TableId(value = "pic_path")
    private String imgPath;

    @ApiModelProperty(value = "图片地址(逗号拼接)")
    @TableField(exist = false)
    private String imgPathStr;

}
