package com.ruoyi.patrol.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.patrol.domain.PatrolPartsIgnore;
import com.ruoyi.patrol.mapper.PatrolPartsIgnoreMapper;
import com.ruoyi.patrol.service.PatrolPartsIgnoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 检查项排除Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@Service
@Master
public class PatrolPartsIgnoreServiceImpl extends ServiceImpl<PatrolPartsIgnoreMapper, PatrolPartsIgnore> implements PatrolPartsIgnoreService {

    @Autowired
    private PatrolPartsIgnoreMapper patrolPartsIgnoreMapper;


    /**
     * 查询列表
     * @param patrolPartsIgnore
     * @return
     */
    @Override
    public List<PatrolPartsIgnore> list(PatrolPartsIgnore patrolPartsIgnore) {
        QueryWrapper<PatrolPartsIgnore> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        qw.setEntity(patrolPartsIgnore);
        return list(qw);
    }

    /**
     * 根据资源ID列表查询检查项排除记录
     *
     * @param assetIds 资源ID列表
     * @return 检查项排除记录列表
     */
    @Override
    public List<PatrolPartsIgnore> selectByAssetIds(List<String> assetIds) {
        return patrolPartsIgnoreMapper.selectByAssetIds(assetIds);
    }


}
