<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patroltidb.mapper.PatrolCulvertCheckMapper">

    <resultMap type="com.ruoyi.patroltidb.domain.PatrolCulvertCheck" id="PatrolCulvertCheckResult">
            <result property="type" column="type"/>
            <result property="category" column="category"/>
            <result property="kahunaId" column="kahuna_id"/>
            <result property="kahunaName" column="kahuna_name"/>
            <result property="kahunaSign" column="kahuna_sign"/>
            <result property="oprUserId" column="opr_user_id"/>
            <result property="oprUserName" column="opr_user_name"/>
            <result property="oprUserSign" column="opr_user_sign"/>
            <result property="checkTime" column="check_time"/>
            <result property="status" column="status"/>
            <result property="auditTime" column="audit_time"/>
            <result property="image" column="image"/>
            <result property="remark" column="remark"/>
            <result property="delFlag" column="del_flag"/>
            <result property="assetId" column="asset_id"/>
            <result property="assetName" column="asset_name"/>
            <result property="assetCode" column="asset_code"/>
            <result property="routeId" column="route_id"/>
            <result property="routeName" column="route_name"/>
            <result property="routeCode" column="route_code"/>
            <result property="propertyUnitId" column="property_unit_id"/>
            <result property="propertyUnitName" column="property_unit_name"/>
            <result property="maintainUnitId" column="maintain_unit_id"/>
            <result property="maintainUnitName" column="maintain_unit_name"/>
            <result property="maintenanceSectionId" column="maintenance_section_id"/>
            <result property="maintenanceSectionName" column="maintenance_section_name"/>
            <result property="centerStake" column="center_stake"/>
            <result property="weather" column="weather"/>
        <result property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">id, create_by, create_time, update_by, update_time,
 type, category, kahuna_id, kahuna_name, kahuna_sign, opr_user_id, opr_user_name, opr_user_sign, check_time, status, audit_time, image, remark, del_flag, asset_id, asset_name, asset_code, route_id, route_name, route_code, property_unit_id, property_unit_name, maintain_unit_id, maintain_unit_name, maintenance_section_id, maintenance_section_name, center_stake, weather    </sql>

    <sql id="where_column">
        <if test="ids != null and ids.size() > 0 ">
            AND id IN
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        <if test="typeLike != null and typeLike != ''">
            AND type like CONCAT('%', #{typeLike}, '%')
        </if>
        <if test="category != null and category != ''">
            AND category = #{category}
        </if>
        <if test="kahunaId != null and kahunaId != ''">
            AND kahuna_id = #{kahunaId}
        </if>
        <if test="kahunaName != null and kahunaName != ''">
            AND kahuna_name = #{kahunaName}
        </if>
        <if test="kahunaSign != null and kahunaSign != ''">
            AND kahuna_sign = #{kahunaSign}
        </if>
        <if test="oprUserId != null and oprUserId != ''">
            AND opr_user_id = #{oprUserId}
        </if>
        <if test="oprUserName != null and oprUserName != ''">
            AND opr_user_name = #{oprUserName}
        </if>
        <if test="oprUserSign != null and oprUserSign != ''">
            AND opr_user_sign = #{oprUserSign}
        </if>
        <if test="checkTime != null and checkTime != ''">
            AND check_time = #{checkTime}
        </if>
        <if test="checkTimes != null and checkTimes != ''">
            AND date_format(check_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(#{checkTimes}, '%Y-%m-%d')
        </if>
        <if test="checkTimee != null and checkTimee != ''">
            AND date_format(check_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(#{checkTimee}, '%Y-%m-%d')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="auditTime != null and auditTime != ''">
            AND audit_time = #{auditTime}
        </if>
        <if test="image != null and image != ''">
            AND image = #{image}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag = #{delFlag}
        </if>
        <if test="assetId != null and assetId != ''">
            AND asset_id = #{assetId}
        </if>
        <if test="assetName != null and assetName != ''">
            AND asset_name = #{assetName}
        </if>
        <if test="assetCode != null and assetCode != ''">
            AND asset_code = #{assetCode}
        </if>
        <if test="routeId != null and routeId != ''">
            AND route_id = #{routeId}
        </if>
        <if test="routeName != null and routeName != ''">
            AND route_name = #{routeName}
        </if>
        <if test="routeCode != null and routeCode != ''">
            AND route_code = #{routeCode}
        </if>
        <if test="propertyUnitId != null and propertyUnitId != ''">
            AND property_unit_id = #{propertyUnitId}
        </if>
        <if test="propertyUnitName != null and propertyUnitName != ''">
            AND property_unit_name = #{propertyUnitName}
        </if>
        <if test="maintainUnitId != null and maintainUnitId != ''">
            AND maintain_unit_id = #{maintainUnitId}
        </if>
        <if test="maintainUnitName != null and maintainUnitName != ''">
            AND maintain_unit_name = #{maintainUnitName}
        </if>
        <if test="maintenanceSectionId != null and maintenanceSectionId != ''">
            AND maintenance_section_id = #{maintenanceSectionId}
        </if>
        <if test="maintenanceSectionName != null and maintenanceSectionName != ''">
            AND maintenance_section_name = #{maintenanceSectionName}
        </if>
        <if test="centerStake != null and centerStake != ''">
            AND center_stake = #{centerStake}
        </if>
        <if test="weather != null and weather != ''">
            AND weather = #{weather}
        </if>
    </sql>

    <sql id="set_column">
            <if test="type != null">
                type = #{type},
            </if>
            <if test="category != null">
                category = #{category},
            </if>
            <if test="kahunaId != null">
                kahuna_id = #{kahunaId},
            </if>
            <if test="kahunaName != null">
                kahuna_name = #{kahunaName},
            </if>
            <if test="kahunaSign != null">
                kahuna_sign = #{kahunaSign},
            </if>
            <if test="oprUserId != null">
                opr_user_id = #{oprUserId},
            </if>
            <if test="oprUserName != null">
                opr_user_name = #{oprUserName},
            </if>
            <if test="oprUserSign != null">
                opr_user_sign = #{oprUserSign},
            </if>
            <if test="checkTime != null">
                check_time = #{checkTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime},
            </if>
            <if test="image != null">
                image = #{image},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="assetId != null">
                asset_id = #{assetId},
            </if>
            <if test="assetName != null">
                asset_name = #{assetName},
            </if>
            <if test="assetCode != null">
                asset_code = #{assetCode},
            </if>
            <if test="routeId != null">
                route_id = #{routeId},
            </if>
            <if test="routeName != null">
                route_name = #{routeName},
            </if>
            <if test="routeCode != null">
                route_code = #{routeCode},
            </if>
            <if test="propertyUnitId != null">
                property_unit_id = #{propertyUnitId},
            </if>
            <if test="propertyUnitName != null">
                property_unit_name = #{propertyUnitName},
            </if>
            <if test="maintainUnitId != null">
                maintain_unit_id = #{maintainUnitId},
            </if>
            <if test="maintainUnitName != null">
                maintain_unit_name = #{maintainUnitName},
            </if>
            <if test="maintenanceSectionId != null">
                maintenance_section_id = #{maintenanceSectionId},
            </if>
            <if test="maintenanceSectionName != null">
                maintenance_section_name = #{maintenanceSectionName},
            </if>
            <if test="centerStake != null">
                center_stake = #{centerStake},
            </if>
            <if test="weather != null">
                weather = #{weather},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="PatrolCulvertCheckResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_culvert_check
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="PatrolCulvertCheckResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_culvert_check
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

</mapper>