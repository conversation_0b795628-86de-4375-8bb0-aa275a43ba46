package com.ruoyi.patroltidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheck;
import com.ruoyi.patroltidb.domain.PatrolCulvertCheck;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.patroltidb.service.PatrolCulvertCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * 涵洞巡检查记录Controller
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Api(tags = "涵洞巡检查记录" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/patrolCulvertCheck")
public class PatrolCulvertCheckController extends BaseController {
    final private PatrolCulvertCheckService patrolCulvertCheckService;


    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;


    /**
     * 查询涵洞巡检查记录列表(分页)
     */
    @ApiOperation("分页查询所有数据")
    //@RequiresPermissions("patrol:assetCheck:list")
    @RequestMapping(value = "/selectAll", method = RequestMethod.POST)
    public AjaxResult selectAll(Page<PatrolCulvertCheck> page, @RequestBody PatrolCulvertCheck patrolCulvertCheck,Integer pageNum,Integer pageSize) {
        if (pageNum != null && pageSize != null) {
            page.setCurrent(pageNum);
            page.setSize(pageSize);
        }
        return success(this.patrolCulvertCheckService.page(page, new QueryWrapper<>(patrolCulvertCheck) {{
            this.in(patrolCulvertCheck.getStatusList() != null, "status", patrolCulvertCheck.getStatusList()).
                    ge(patrolCulvertCheck.getCheckStartTime() != null, "check_time", patrolCulvertCheck.getCheckStartTime()).
                    le(patrolCulvertCheck.getCheckEndTime() != null, "check_time", patrolCulvertCheck.getCheckEndTime()).orderByDesc("check_time");
        }}));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @RequestMapping(value = "/selectOne/{id}", method = RequestMethod.GET)
    public AjaxResult selectOne(@PathVariable Serializable id) {
        return success(this.patrolCulvertCheckService.getById(id));
    }

    @ApiOperation("通过主键查询单条数据")
    @RequestMapping(value = "/selectRequestOne", method = RequestMethod.GET)
    public AjaxResult selectRequestOne(@RequestParam String id) {
        PatrolCulvertCheck patrolCulvertCheck = this.patrolCulvertCheckService.getPatrolCulverCheckById(id);
        try{
            patrolAssetCheckService.setStack(patrolCulvertCheck);
            patrolAssetCheckService.setSignUrl(patrolCulvertCheck);
        }catch (Exception e){
            log.error("获取签名图片失败",e);
        }
        return success(patrolCulvertCheck);
    }

    /**
     * 新增数据
     *
     * @param patrolCulvertCheck 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public AjaxResult insert(@RequestBody PatrolCulvertCheck patrolCulvertCheck) {
        if(patrolCulvertCheck.getAssetId() == null){
            return error("资产ID不能为空");
        }
        return success(this.patrolCulvertCheckService.save(patrolCulvertCheck));
    }

    /**
     * 修改数据
     *
     * @param patrolCulvertCheck 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public AjaxResult update(@RequestBody PatrolCulvertCheck patrolCulvertCheck) {
        return success(this.patrolCulvertCheckService.updateById(patrolCulvertCheck));
    }

    /**
     * 删除数据
     *
     * @param idList 主键集合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public AjaxResult delete(@RequestBody List<String> idList) {
        return success(this.patrolCulvertCheckService.removeByIds(idList));
    }

    /**
     * 查询数据列表
     *
     * @param patrolCulvertCheck 查询实体
     * @return 数据列表
     */
    @ApiOperation("查询数据列表")
    @RequestMapping(value = "/getListByEntity", method = RequestMethod.POST)
    public AjaxResult getListByEntity(@RequestBody PatrolCulvertCheck patrolCulvertCheck) {
        return success(this.patrolCulvertCheckService.list(new QueryWrapper<>(patrolCulvertCheck)));
    }


    /**
     * 导出涵洞巡检查记录列表
     */
    @ApiOperation("导出涵洞巡检查记录列表")
    //@RequiresPermissions("patrol:culvertCheck:export")
    @Log(title = "涵洞巡检查记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolCulvertCheck> list = patrolCulvertCheckService.list();
        ExcelUtil<PatrolCulvertCheck> util = new ExcelUtil<PatrolCulvertCheck>(PatrolCulvertCheck.class);
        util.exportExcel(response, list, "涵洞巡检查记录数据");
    }


}
