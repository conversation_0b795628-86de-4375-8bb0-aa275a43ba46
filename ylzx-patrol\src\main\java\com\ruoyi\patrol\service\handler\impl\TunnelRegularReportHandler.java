package com.ruoyi.patrol.service.handler.impl;

import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * "隧道经常检查记录表"生成处理器。
 * 继承自抽象处理器以重用通用的Excel生成逻辑。
 */
@Slf4j
public class TunnelRegularReportHandler extends AbstractExcelReportHandler<PatrolAssetCheck> {

    private final List<PatrolAssetCheck> reportDataList;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");

    // --- 此报表的特定样式定义 ---
    // 继承的样式：titleStyle, headerLabelStyle, headerValueStyle, itemHeaderStyle, itemCellStyle, itemFirstColStyle
    private CellStyle itemContentStyle;    // 检查内容样式 (左对齐) - 特定
    private CellStyle footerLabelStyle;    // 页脚标签样式 (基于headerLabelStyle, 右对齐)
    private CellStyle footerValueStyle;    // 页脚值/签名样式 (基于headerValueStyle, 左对齐)
    private CellStyle normalBorderStyle;   // 仅用于细边框的特定样式
    private CellStyle shadedHeaderLabelStyle; // 带背景的标签样式 (基于headerLabelStyle)
    private CellStyle headerLabelStyleWithBorder; // 带边框的头标签样式

    private CellStyle noBorderStyle;      // 无边框左对齐自动换行样式

    // 继承的：signUrlMap, signImageMap, failedSignIds, log, SIGNATURE_HEIGHT_POINTS, httpClient

    public TunnelRegularReportHandler(List<PatrolAssetCheck> reportDataList, Map<String, String> signUrlMap) {
        // super(); // 隐式调用超类构造函数
        this.reportDataList = reportDataList != null ? reportDataList : new ArrayList<>();
        if (signUrlMap != null) {
            this.signUrlMap.putAll(signUrlMap); // 填充继承的映射
        }
        // 预加载现在在beforeSheetCreate中调用
    }

    /**
     * 重写createStyles方法，为此报表类型添加特定样式
     * 并初始化基类中的通用样式，同时确保所有样式都有边框。
     */
    @Override
    protected void createStyles(Workbook workbook) {
        super.createStyles(workbook); // 首先初始化通用样式

        // --- 通用边框样式 ---
        normalBorderStyle = workbook.createCellStyle();
        setThinBorders(normalBorderStyle); // 使用继承的辅助方法

        // 无边框左对齐自动换行样式
        noBorderStyle = workbook.createCellStyle();
        noBorderStyle.setBorderTop(BorderStyle.NONE);
        noBorderStyle.setBorderBottom(BorderStyle.NONE);
        noBorderStyle.setBorderLeft(BorderStyle.NONE);
        noBorderStyle.setBorderRight(BorderStyle.NONE);
        noBorderStyle.setAlignment(HorizontalAlignment.LEFT);
        noBorderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        noBorderStyle.setWrapText(true);

        // --- 克隆并应用边框到所有继承的样式 ---
        // 确保所有基础样式都有边框，因为隧道报表似乎到处都有边框

        headerLabelStyle = workbook.createCellStyle();
        headerLabelStyle.cloneStyleFrom(super.headerLabelStyle);
        copyBorders(normalBorderStyle, headerLabelStyle); // 确保边框

        headerValueStyle = workbook.createCellStyle();
        headerValueStyle.cloneStyleFrom(super.headerValueStyle);
        copyBorders(normalBorderStyle, headerValueStyle);

        itemHeaderStyle = workbook.createCellStyle();
        itemHeaderStyle.cloneStyleFrom(super.itemHeaderStyle);
        copyBorders(normalBorderStyle, itemHeaderStyle);

        itemCellStyle = workbook.createCellStyle();
        itemCellStyle.cloneStyleFrom(super.itemCellStyle); // 居中，带边框
        copyBorders(normalBorderStyle, itemCellStyle);

        itemFirstColStyle = workbook.createCellStyle();
        itemFirstColStyle.cloneStyleFrom(super.itemFirstColStyle); // 左对齐，带边框
        copyBorders(normalBorderStyle, itemFirstColStyle);

        // --- 新的/特定样式 ---
        // 检查内容样式 (左对齐，带边框) - 继承自itemCellStyle（现在有边框）
        itemContentStyle = workbook.createCellStyle();
        itemContentStyle.cloneStyleFrom(itemCellStyle); // 继承字体、文本换行、边框
        itemContentStyle.setAlignment(HorizontalAlignment.LEFT); // 覆盖对齐方式为左对齐
        itemContentStyle.setWrapText(true);

        // 页脚值样式 (左对齐，带边框) - 用于签名占位符或文本名称
        footerValueStyle = workbook.createCellStyle();
        footerValueStyle.cloneStyleFrom(headerValueStyle); // 继承字体、对齐方式、边框
        footerValueStyle.setAlignment(HorizontalAlignment.CENTER);
        footerValueStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        footerValueStyle.setWrapText(true);
        
        headerLabelStyleWithBorder = workbook.createCellStyle();
        headerLabelStyleWithBorder.cloneStyleFrom(super.headerLabelStyle); // 从基本样式开始
        copyBorders(normalBorderStyle, headerLabelStyleWithBorder); // 确保边框
        headerLabelStyleWithBorder.setWrapText(true);

        shadedHeaderLabelStyle = workbook.createCellStyle(); // 带背景的标签
        headerLabelStyle.cloneStyleFrom(shadedHeaderLabelStyle);
        setSolidBackground(shadedHeaderLabelStyle, new java.awt.Color(217, 217, 217));
        copyBorders(normalBorderStyle, shadedHeaderLabelStyle); // 确保边框
        Font headerLabelFont = workbook.createFont();
        headerLabelFont.setBold(true);
        headerLabelFont.setFontHeightInPoints((short) 10);
        shadedHeaderLabelStyle.setFont(headerLabelFont);
        shadedHeaderLabelStyle.setAlignment(HorizontalAlignment.CENTER);
        shadedHeaderLabelStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        shadedHeaderLabelStyle.setWrapText(true);

        // 页脚标签样式 (右对齐，带边框) - 基于headerLabelStyleWithBorder
        footerLabelStyle = workbook.createCellStyle();
        footerLabelStyle.cloneStyleFrom(headerLabelStyleWithBorder); // 继承字体、对齐方式、边框
        footerLabelStyle.setAlignment(HorizontalAlignment.CENTER); // 图片中标签居中
        footerLabelStyle.setWrapText(true);
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        super.beforeSheetCreate(writeWorkbookHolder, writeSheetHolder); // 通过createStyles重写创建样式
        // 在生成表格内容前预加载签名
        if (!signUrlMap.isEmpty()) {
            preloadSignImages(); // 调用继承的预加载器
        } else {
            log.warn("签名URL映射为空，将无法加载签名图片。");
        }
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();

        // 诊断日志记录
        log.info("隧道报表生成开始。签名URL数量: {}, 预加载图片数量: {}", signUrlMap.size(), signImageMap.size());
        if (!failedSignIds.isEmpty()) {
            log.warn("签名下载失败 {} 个。 IDs: {}", failedSignIds.size(), failedSignIds);
        }

        int currentRowIndex = 0; // 当前行索引

        // --- 设置列宽 (基于隧道报表布局，6列 A-F) ---
        sheet.setColumnWidth(0, 10 * 256);  // A列 (项目名称 / 检查人标签)
        sheet.setColumnWidth(1, 13 * 256);  // B列 (检查内容 Part1 / 检查人签名区)
        sheet.setColumnWidth(2, 14 * 256);  // C列 (检查内容 Part2 / 检查人签名区)
        sheet.setColumnWidth(3, 16 * 256);  // D列 (状态描述 / 记录人标签)
        sheet.setColumnWidth(4, 11 * 256);  // E列 (判断结论 Part1 / 记录人签名区)
        sheet.setColumnWidth(5, 20 * 256);  // F列 (判断结论 Part2 / 记录人签名区)

        // 存储列宽信息用于行高调整
        int[] columnWidths = new int[] {10, 13, 14, 16, 11, 20};

        if (reportDataList == null || reportDataList.isEmpty()) {
            log.warn("隧道报告数据列表为空。生成空表格。");
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "隧道经常检查记录表 (无数据)", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));
            // 应用边框到合并后的单元格
            applyCellBorder(titleRow.getCell(0), normalBorderStyle);
            applyCellBorder(titleRow.getCell(5), normalBorderStyle); // 合并区域右下角也需要边框
            return; // 如果没有数据则停止处理
        }

        for (int i = 0; i < reportDataList.size(); i++) {
            PatrolAssetCheck reportData = reportDataList.get(i);
            if (reportData == null) {
                log.warn("跳过索引 {} 处的null报告数据对象", i);
                continue; // 跳过null数据条目
            }

            // === 开始为 reportData 绘制报告部分 ===
            log.debug("正在为资产编码 {} 生成隧道报告部分", reportData.getAssetCode());

            // 1. 标题行
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "隧道经常检查记录表", titleStyle); // 使用继承的createCell和titleStyle
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并 A-F
            // 可以在合并后为合并区域的左上角单元格应用边框，或者使用 applyRowBorder 确保所有单元格创建时带边框
            // applyRowBorder(titleRow, 0, 5, normalBorderStyle); // 应用边框到所有基础单元格
            currentRowIndex++;

            // 2. 管理单位行
            Row mgmtUnitRow = sheet.createRow(currentRowIndex);
            String mgmtUnitName = reportData.getPropertyUnitName() != null ? reportData.getPropertyUnitName() : reportData.getMaintainUnitName();
            createCell(mgmtUnitRow, 0, "管理单位：" + Objects.toString(mgmtUnitName, ""), headerValueStyle); // 使用左对齐的值样式
            for (int col = 1; col <= 5; col++) {
                createCell(mgmtUnitRow, col, "", headerValueStyle); // 创建空单元格以应用样式和边框
            }
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并 A-F
            applyRowBorder(mgmtUnitRow, 0, 5, normalBorderStyle); // 确保边框
            setConditionalRowHeight(mgmtUnitRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 3. 隧道信息行
            Row tunnelInfoRow = sheet.createRow(currentRowIndex);
            createCell(tunnelInfoRow, 0, "隧道名称", shadedHeaderLabelStyle); // A
            createCell(tunnelInfoRow, 1, Objects.toString(reportData.getAssetName(), ""), headerValueStyle); // B
            createCell(tunnelInfoRow, 2, "隧道编码", shadedHeaderLabelStyle); // C
            createCell(tunnelInfoRow, 3, Objects.toString(reportData.getAssetCode(), ""), headerValueStyle); // D
            createCell(tunnelInfoRow, 4, "中心桩号", shadedHeaderLabelStyle); // E
            createCell(tunnelInfoRow, 5, formatStake(reportData.getCenterStake(), reportData.getStakeFormat()), headerValueStyle); // F
            applyRowBorder(tunnelInfoRow, 0, 5, normalBorderStyle);
            setConditionalRowHeight(tunnelInfoRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 4. 路线信息行
            Row routeInfoRow = sheet.createRow(currentRowIndex);
            createCell(routeInfoRow, 0, "路线编码", shadedHeaderLabelStyle); // A
            createCell(routeInfoRow, 1, Objects.toString(reportData.getRouteCode(), ""), headerValueStyle); // B
            createCell(routeInfoRow, 2, "路线名称", shadedHeaderLabelStyle); // C
            createCell(routeInfoRow, 3, Objects.toString(reportData.getMaintenanceSectionName(), ""), headerValueStyle); // D
            createCell(routeInfoRow, 4, "养护单位", shadedHeaderLabelStyle); // E
            createCell(routeInfoRow, 5, Objects.toString(reportData.getMaintainUnitName(), ""), headerValueStyle); // F
            applyRowBorder(routeInfoRow, 0, 5, normalBorderStyle);
            setConditionalRowHeight(routeInfoRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 5. 检查信息行
            Row checkInfoRow = sheet.createRow(currentRowIndex);
            createCell(checkInfoRow, 0, "检查日期", shadedHeaderLabelStyle); // A
            Date checkDate = reportData.getCheckTime();
            createCell(checkInfoRow, 1, checkDate != null ? dateFormat.format(checkDate) : "", headerValueStyle); // B
            createCell(checkInfoRow, 2, "天气", shadedHeaderLabelStyle); // C
            createCell(checkInfoRow, 3, Objects.toString(reportData.getWeather(), ""), headerValueStyle); // D
            createCell(checkInfoRow, 4, "检查类型", shadedHeaderLabelStyle); // E
            createCell(checkInfoRow, 5, reportData.getCategory() != null ? Objects.toString(reportData.getCategory().getDescription(), "常规巡查") : "常规巡查", headerValueStyle); // F
            applyRowBorder(checkInfoRow, 0, 5, normalBorderStyle);
            setConditionalRowHeight(checkInfoRow, 0, 5, columnWidths, 20);
            currentRowIndex++;

            // 6. 项目标题行
            Row itemHeaderRow = sheet.createRow(currentRowIndex);
            itemHeaderRow.setHeightInPoints(25);
            createCell(itemHeaderRow, 0, "里程桩号", shadedHeaderLabelStyle); // A
            createCell(itemHeaderRow, 1, "项目名称", shadedHeaderLabelStyle); // B
            createCell(itemHeaderRow, 2, "检查内容", shadedHeaderLabelStyle);         // C (合并 B, C)
            createCell(itemHeaderRow, 3, "", shadedHeaderLabelStyle); // D (占位符)
            createCell(itemHeaderRow, 4, "状态描述", shadedHeaderLabelStyle); // E
            createCell(itemHeaderRow, 5, "判断结论", shadedHeaderLabelStyle);         // F
            applyRowBorder(itemHeaderRow, 0, 5, itemHeaderStyle); // 应用边框
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 2, 3)); // 合并 C, D
            currentRowIndex++;

            // 7. 项目详情行
            List<PatrolAssetCheckDetail> details = reportData.getPatrolCheckDetailList();
            if (details != null && !details.isEmpty()) {
                for (PatrolAssetCheckDetail item : details) {
                    if (item == null) {
                        log.warn("跳过资产编码 {} 的null详情项", reportData.getAssetCode());
                        continue;
                    }
                    Row itemRow = sheet.createRow(currentRowIndex);
                    // itemRow.setHeightInPoints(18); // 自动高度

                    // 里程桩号 (A) - 左对齐
                    createCell(itemRow, 0, "", itemFirstColStyle);

                    // 项目名称 (B) - 左对齐
                    createCell(itemRow, 1, Objects.toString(item.getPartsTypeName(), ""), itemFirstColStyle);

                    // 检查内容 (C) - 左对齐
                    createCell(itemRow, 2, item.getDes() == null || item.getDes().trim().isEmpty() ? "无" : item.getDes(), itemContentStyle);
                    createCell(itemRow, 3, "", itemContentStyle); // 占位符
                    sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 2, 3));

                    // 状态描述 (D) - 居中
                    // 假设状态描述对应 'defect' 字段
                    createCell(itemRow, 4, item.getDefect() == null || item.getDefect().trim().isEmpty() ? "未见异常" : item.getDefect(), itemCellStyle);

                    // 判断结论 ( F) - 居中
                    createCell(itemRow, 5, item.getAdvice() == null || item.getAdvice().trim().isEmpty() ? "正常保养" : item.getAdvice(), itemCellStyle);

                    // 为整行应用边框
                    applyRowBorder(itemRow, 0, 5, normalBorderStyle);
                    // 使用条件高度而不是固定高度
                    setConditionalRowHeight(itemRow, 0, 5, columnWidths);
                    currentRowIndex++;
                }
            } else {
                // 无详情行
                log.info("资产编码 {} 未找到检查详情", reportData.getAssetCode());
                Row emptyRow = sheet.createRow(currentRowIndex);
                createCell(emptyRow, 0, "无检查明细", itemFirstColStyle); // A
                createCell(emptyRow, 1, "", itemContentStyle);         // B
                createCell(emptyRow, 2, "", itemContentStyle);         // C
                createCell(emptyRow, 3, "", itemCellStyle);           // D
                createCell(emptyRow, 4, "", itemCellStyle);           // E
                createCell(emptyRow, 5, "", itemCellStyle);           // F
                sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5)); // 合并 A-F
                applyRowBorder(emptyRow, 0, 5, normalBorderStyle); // 应用边框
                // 使用条件高度而不是固定高度
                setConditionalRowHeight(emptyRow, 0, 5, columnWidths);
                currentRowIndex++;
            }

            // 2. 添加空白行防止签名越界
            Row blankRow1 = sheet.createRow(currentRowIndex);
            createCell(blankRow1, 1, "", noBorderStyle); // 合并创建空单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            blankRow1.setHeightInPoints(5);
            currentRowIndex++;

            // 8. 页脚行 (检查人、记录人标签和签名/名称区域)
            // 采用与 Bridge 类似的布局：标签在一行，签名/名称从该行开始垂直向下排列
            int footerLabelRowIndex = currentRowIndex;
            Row footerLabelRow = sheet.createRow(footerLabelRowIndex);
            footerLabelRow.setHeightInPoints(25); // 为标签行设置高度

            createCell(footerLabelRow, 0, "检查人", shadedHeaderLabelStyle); // 标签 A (右对齐)
            createCell(footerLabelRow, 1, "", footerValueStyle);       // 占位符 B (签名/名称区域开始)
            createCell(footerLabelRow, 2, "", footerValueStyle);       // 占位符 C
            createCell(footerLabelRow, 3, "记录人", shadedHeaderLabelStyle); // 标签 D (右对齐)
            createCell(footerLabelRow, 4, "", footerValueStyle);       // 占位符 E (签名/名称区域开始)
            createCell(footerLabelRow, 5, "", footerValueStyle);       // 占位符 F

            // 为标签行应用边框
            applyRowBorder(footerLabelRow, 0, 5, normalBorderStyle);

            // 9. 添加签名/姓名
            // 签名区域开始于标签行的下一行，或者如果无签名则直接在标签行写入名字
            int signatureStartRowIndex = footerLabelRowIndex; // 从标签行开始放置内容

            // 检查人 (Kahuna) - 签名/名称放在 B 列 (索引 1)
            List<String> inspectorSignList = reportData.getKahunaSignList(); // 使用检查人对应的字段
            String inspectorName = reportData.getKahunaName(); // 使用检查人姓名对应的字段
            int lastRowForInspector = signatureStartRowIndex;
            log.debug("处理检查人: Name={}, Signs={}", inspectorName, inspectorSignList != null ? inspectorSignList.size() : 0);
            if (inspectorSignList != null && !inspectorSignList.isEmpty() && !signImageMap.isEmpty()) {
                lastRowForInspector = addSignatureImages(sheet, signatureStartRowIndex, 1,1, // 列 B
                        inspectorSignList, inspectorName, footerValueStyle, true);
            } else {
                // 没有签名或签名加载失败，直接写入名字到 B 列
                log.debug("在 B{} 写入检查人姓名: {}", signatureStartRowIndex + 1, inspectorName);
                createCell(footerLabelRow, 1, Objects.toString(inspectorName, ""), footerValueStyle);
            }


            // 记录人 (OprUser) - 签名/名称放在 E 列 (索引 4)
            List<String> recorderSignList = reportData.getOprUserSignList(); // 使用记录人对应的字段
            String recorderName = reportData.getOprUserName(); // 使用记录人姓名对应的字段
            int lastRowForRecorder = signatureStartRowIndex;
            log.debug("处理记录人: Name={}, Signs={}", recorderName, recorderSignList != null ? recorderSignList.size() : 0);
            if (recorderSignList != null && !recorderSignList.isEmpty() && !signImageMap.isEmpty()) {
                lastRowForRecorder = addSignatureImages(sheet, signatureStartRowIndex, 4,4, // 列 E
                        recorderSignList, recorderName, footerValueStyle, true);
            } else {
                // 没有签名或签名加载失败，直接写入名字到 E 列
                log.debug("在 E{} 写入记录人姓名: {}", signatureStartRowIndex + 1, recorderName);
                createCell(footerLabelRow, 4, Objects.toString(recorderName, ""), footerValueStyle);
            }

            // 确定页脚部分使用的最终行索引
            int footerEndRowIndex = Math.max(lastRowForInspector, lastRowForRecorder);

            currentRowIndex = footerEndRowIndex; // 更新当前行索引到页脚之后

            // 确保页脚最后一行（可能是标签行或最后一个签名行）的所有单元格都有边框
            Row lastFooterRow = sheet.getRow(footerEndRowIndex - 1);
            if (lastFooterRow != null) {
                applyRowBorder(lastFooterRow, 0, 5, normalBorderStyle);
            }


            // === 当前报告部分完成 ===

            // 在下一个报告部分之前插入分页符（如果不是最后一个）
            if (i < reportDataList.size() - 1) {
                sheet.setRowBreak(currentRowIndex - 1); // 在当前部分的最后一行之后设置分页符
                log.debug("已在行 {} 之后插入分页符", currentRowIndex - 1);
            } else {
                log.debug("到达最后一个报告部分，不插入分页符。");
            }
            log.info("完成资产编码 {} 的隧道报告部分生成，当前行号: {}", reportData.getAssetCode(), currentRowIndex);
        }
        log.info("已完成生成所有隧道报告部分。");
    }
}
