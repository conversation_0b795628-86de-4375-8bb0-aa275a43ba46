package com.ruoyi.repote.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.repote.domain.RepotePerson;

/**
 * 填报人员Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
public interface RepotePersonService extends IService<RepotePerson> {

    /**
     * 根据条件查询填报人员数据列表
     * @param params
     */
    List<RepotePerson> findListByParam(Map<String, Object> params);

}
