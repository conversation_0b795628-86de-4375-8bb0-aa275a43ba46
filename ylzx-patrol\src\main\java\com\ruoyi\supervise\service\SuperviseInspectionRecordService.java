package com.ruoyi.supervise.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.supervise.domain.SuperviseInspectionRecord;

/**
 * 督查记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface SuperviseInspectionRecordService extends IService<SuperviseInspectionRecord> {

    /**
     * 根据条件查询督查记录数据列表
     * @param params
     */
    List<SuperviseInspectionRecord> findListByParam(Map<String, Object> params);

}
