package com.ruoyi.repote.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.map.Paramap;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.repote.domain.RepoteForm;
import com.ruoyi.repote.domain.RepoteMission;
import com.ruoyi.repote.domain.RepotePerson;
import com.ruoyi.repote.domain.RepoteRecord;
import com.ruoyi.repote.service.RepoteFormService;
import com.ruoyi.repote.service.RepoteMissionService;
import com.ruoyi.repote.service.RepotePersonService;
import com.ruoyi.repote.service.RepoteRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 填报任务Controller
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Api(tags = "填报任务")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/repoteMission")
public class RepoteMissionController extends BaseController {
    final private RepoteMissionService repoteMissionService;
    @Resource
    private RepotePersonService repotePersonService;
    @Resource
    private RepoteFormService formService;
    @Resource
    private RepoteRecordService recordService;
    @Resource
    private RepoteMissionService missionService;


    /**
     * 查询填报任务列表(分页)
     */
    @ApiOperation("查询填报任务列表")
//    //@RequiresPermissions("repote:repoteMission:list")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> params) {
        params.put("order", "create_time desc");
        startPage();
        List<RepoteMission> list = repoteMissionService.findListByParam(params);
        QueryWrapper<RepoteForm> qw;
        for (RepoteMission mission : list) {
            qw = new QueryWrapper<>();
            qw.eq("mission_id", mission.getId());
            mission.setTotalNum(formService.count(qw));
//            qw.eq("form_status", 1);//已完成数量
            mission.setFinishNum(missionService.getCountByReportMission(mission.getId()));
        }
        return getDataTable(list);
    }

    /**
     * 查询填报任务列表(不分页)
     */
    @ApiOperation("查询填报任务列表(不分页)")
//    //@RequiresPermissions("repote:repoteMission:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<RepoteMission> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<RepoteMission> list = repoteMissionService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询填报任务数据
     */
    @ApiOperation("根据id查询填报任务数据")
//    //@RequiresPermissions("repote:repoteMission:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        RepoteMission repoteMission = repoteMissionService.getById(id);
        if (repoteMission == null) {
            return error("未查询到【填报任务】记录");
        }
        List<RepotePerson> personList = repotePersonService.listByMap(Paramap.create().put("mission_id", id));
        repoteMission.setPersonList(personList);
        return success(repoteMission);
    }

    /**
     * 新增填报任务
     */
    @ApiOperation("新增填报任务")
//    //@RequiresPermissions("repote:repoteMission:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody RepoteMission repoteMission) {
        repoteMissionService.save(repoteMission);
        if (repoteMission.getPersonList() != null && repoteMission.getPersonList().size() > 0){
            repoteMission.getPersonList().forEach(i -> i.setMissionId(repoteMission.getId()));
            repotePersonService.saveBatch(repoteMission.getPersonList());
        }
        return toAjax(true);
    }

    /**
     * 修改填报任务
     */
    @ApiOperation("修改填报任务")
//    //@RequiresPermissions("repote:repoteMission:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody RepoteMission repoteMission) {
        if (repoteMission.getPersonList() != null && repoteMission.getPersonList().size() > 0){
            repotePersonService.removeByMap(Paramap.create().put("mission_id", repoteMission.getId()));
            repoteMission.getPersonList().forEach(i -> i.setMissionId(repoteMission.getId()));
            repotePersonService.saveBatch(repoteMission.getPersonList());
        }
        //下发任务
        if ("2".equals(repoteMission.getStatus())) {
//            RepoteMission mission = repoteMissionService.getById(repoteMission.getId());
            List<RepoteForm> formList = formService.listByMap(Paramap.create().put("mission_id", repoteMission.getId()));
            //查询填报人
            List<RepotePerson> personList = repotePersonService.listByMap(Paramap.create().put("mission_id", repoteMission.getId()));
            List<RepoteRecord> list = new ArrayList<>();
            for (RepoteForm form : formList) {
                for (RepotePerson person : personList) {
                    RepoteRecord record = new RepoteRecord();
                    record.setFormId(form.getId());
                    record.setFormName(form.getName());
                    record.setUserId(person.getUserId());
                    record.setUserName(person.getUserName());
                    record.setStatus(1);//默认待填写
                    record.setMissionId(repoteMission.getId());
                    record.setMissionName(repoteMission.getName());
                    list.add(record);
                }
            }
            if (list.size() > 0) {//生成填报记录
                recordService.saveBatch(list);
            }
        }

        return toAjax(repoteMissionService.updateById(repoteMission));
    }

    /**
     * 删除填报任务
     */
    @ApiOperation("删除填报任务")
//    //@RequiresPermissions("repote:repoteMission:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        repotePersonService.removeByMap(Paramap.create().put("mission_id", id));
        formService.removeByMap(Paramap.create().put("mission_id", id));
        recordService.removeByMap(Paramap.create().put("mission_id", id));
        return toAjax(repoteMissionService.removeById(id));
    }

    /**
     * 导出填报任务列表
     */
    @ApiOperation("导出填报任务列表")
//    //@RequiresPermissions("repote:repoteMission:export")
    @Log(title = "填报任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<RepoteMission> list = repoteMissionService.list();
        ExcelUtil<RepoteMission> util = new ExcelUtil<RepoteMission>(RepoteMission.class);
        util.exportExcel(response, list, "填报任务数据");
    }


}
