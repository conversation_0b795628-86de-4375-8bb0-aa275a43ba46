package com.ruoyi.patroltidb.service.impl;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.manage.api.domain.RemoteRoadDiseaseResponse;
import com.ruoyi.manage.api.service.RemoteRoadDiseaseService;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import com.ruoyi.patrol.domain.PatrolPartsInfo;
import com.ruoyi.patrol.domain.dto.PatrolDiseaseDTO;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.enums.DeleteFlagType;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.mapper.PatrolAssetCheckDetailMapper;
import com.ruoyi.patrol.service.PatrolPartsDiseasesService;
import com.ruoyi.patrol.service.PatrolPartsInfoService;
import com.ruoyi.patroltidb.service.PatrolAssetCheckDetailService;

import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import lombok.extern.slf4j.Slf4j;

/**
 * 资产寻检查子表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@Service
@Slave
@Slf4j
public class PatrolAssetCheckDetailServiceImpl extends ServiceImpl<PatrolAssetCheckDetailMapper, PatrolAssetCheckDetail> implements PatrolAssetCheckDetailService {

    @Resource
    private PatrolPartsInfoService patrolPartsInfoService;

    @Resource
    private PatrolPartsDiseasesService patrolPartsDiseasesService;

    @Resource
    private RemoteRoadDiseaseService remoteRoadDiseaseService;

    @Resource
    private RedisService redisService;

    @Resource
    private CacheManager cacheManager;

    private static final Cache<String, Map<String, Map<String, Map<String, PatrolAssetCheckDetail>>>> localCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)  // 设置缓存过期时间为10分钟
            .maximumSize(1000)                       // 设置缓存项最大数量
            .build();

    private static final Cache<String, List<PatrolPartsInfo>> partsInfoCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)  // 设置缓存过期时间为10分钟
            .maximumSize(100)                        // 设置缓存项最大数量
            .build();

    /**
     * 查询列表
     *
     * @param patrolAssetCheckDetail PatrolAssetCheckDetail
     * @return List<PatrolAssetCheckDetail>
     */
    @Override
    public List<PatrolAssetCheckDetail> list(PatrolAssetCheckDetail patrolAssetCheckDetail) {
        QueryWrapper<PatrolAssetCheckDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        qw.setEntity(patrolAssetCheckDetail);
        return this.list(qw);
    }

    /**
     * 获取病害和检查类型映射
     */
    public Map<String, PatrolAssetCheckDetail> getDiseaseTypeMapping(String logId, String typeCode, String assetId) {
        // 构建缓存键
        String cacheKey = "disease:type:mapping:" + logId;

        // 首先尝试从本地缓存获取
        Map<String, Map<String, Map<String, PatrolAssetCheckDetail>>> localCacheResult = localCache.getIfPresent(cacheKey);
        if (localCacheResult != null) {
            Map<String, Map<String, PatrolAssetCheckDetail>> typeCodeMap = localCacheResult.get(typeCode);
            if (typeCodeMap != null) {
                return typeCodeMap.get(assetId);
            }
        }

        // 尝试从Redis获取
        Map<String, Object> rawMap = redisService.getCacheMap(cacheKey);

        if (rawMap != null && !rawMap.isEmpty()) {
            try {
                Map<String, Map<String, Map<String, PatrolAssetCheckDetail>>> resultMap = convertRedisData(rawMap);
                if (resultMap != null) {
                    // 将结果存入本地缓存
                    localCache.put(cacheKey, resultMap);
                    Map<String, Map<String, PatrolAssetCheckDetail>> typeCodeMap = resultMap.get(typeCode);
                    return typeCodeMap != null ? typeCodeMap.get(assetId) : null;
                }
            } catch (Exception e) {
                log.error("Redis数据转换失败", e);
            }
        }

        // Redis中不存在或转换失败，重新生成映射
        Map<String, Map<String, Map<String, PatrolAssetCheckDetail>>> newResultMap = generateNewMapping(logId);
        if (newResultMap != null) {
            // 将新生成的结果存入本地缓存
            localCache.put(cacheKey, newResultMap);
            // 将结果存入Redis
            redisService.setCacheMap(cacheKey, newResultMap);
            redisService.expire(cacheKey, 24, TimeUnit.HOURS);

            Map<String, Map<String, PatrolAssetCheckDetail>> typeCodeMap = newResultMap.get(typeCode);
            return typeCodeMap != null ? typeCodeMap.get(assetId) : null;
        }
        return null;
    }

    private Map<String, Map<String, Map<String, PatrolAssetCheckDetail>>> generateNewMapping(String logId) {
        R<List<RemoteRoadDiseaseResponse>> remoteRoadDiseaseResponseR = remoteRoadDiseaseService.getRoadDiseaseList(logId);
        if (remoteRoadDiseaseResponseR.getCode() != 200) {
            return null;
        }

        List<RemoteRoadDiseaseResponse> remoteRoadDiseaseResponseList = remoteRoadDiseaseResponseR.getData();
        Map<String, String> partsDiseasesMap = patrolPartsDiseasesService.getPartsDiseasesMap();
        Map<String, Map<String, Map<String, PatrolAssetCheckDetail>>> resultMap = new HashMap<>();

        for (RemoteRoadDiseaseResponse disease : remoteRoadDiseaseResponseList) {
            AssetType assetType = AssetType.fromSysAssetType(disease.getAssetType());
            if (assetType == null) {
                continue;
            }

            resultMap.computeIfAbsent(assetType.getCode().toString(), k -> new HashMap<>())
                    .computeIfAbsent(disease.getAssetId(), k -> new HashMap<>())
                    .compute(partsDiseasesMap.get(String.valueOf(disease.getDiseaseType())), (k, v) -> {
                        PatrolAssetCheckDetail detail = v == null ? new PatrolAssetCheckDetail() : v;
                        updateDetail(detail, disease);
                        return detail;
                    });
        }

        return resultMap;
    }

    private void updateDetail(PatrolAssetCheckDetail detail, RemoteRoadDiseaseResponse disease) {
        if (detail.getDefect() == null) {
            detail.setDefect(disease.getDiseaseName());
            detail.setAdvice(disease.getDiseaseDesc());
            detail.setImage(disease.getDisPicPath());
            detail.setIgnore(false);
        } else {
            detail.setDefect(detail.getDefect() + "," + disease.getDiseaseName());
            detail.setAdvice(detail.getAdvice() + "," + disease.getDiseaseDesc());
            detail.setImage(detail.getImage() + "," + disease.getDisPicPath());
        }
    }

    private Map<String, Map<String, Map<String, PatrolAssetCheckDetail>>> convertRedisData(Map<String, Object> rawMap) {
        Map<String, Map<String, Map<String, PatrolAssetCheckDetail>>> resultMap = new HashMap<>();

        try {
            for (Map.Entry<String, Object> entry : rawMap.entrySet()) {
                // 添加调试日志
                log.debug("处理key: {}, value类型: {}", entry.getKey(), entry.getValue().getClass().getName());

                // 如果值是字符串，尝试解析JSON
                if (entry.getValue() instanceof String) {
                    // 这里可能需要添加JSON解析逻辑
                    continue;
                }

                if (!(entry.getValue() instanceof Map)) {
                    continue;
                }

                try {
                    Map<?, ?> firstLevel = (Map<?, ?>) entry.getValue();
                    Map<String, Map<String, PatrolAssetCheckDetail>> secondLevel = new HashMap<>();

                    for (Map.Entry<?, ?> firstEntry : firstLevel.entrySet()) {
                        if (!(firstEntry.getValue() instanceof Map)) {
                            continue;
                        }

                        Map<?, ?> secondLevelMap = (Map<?, ?>) firstEntry.getValue();
                        Map<String, PatrolAssetCheckDetail> thirdLevel = new HashMap<>();

                        for (Map.Entry<?, ?> secondEntry : secondLevelMap.entrySet()) {
                            if (!(secondEntry.getValue() instanceof Map)) {
                                continue;
                            }

                            @SuppressWarnings("unchecked")
                            Map<String, Object> valueMap = (Map<String, Object>) secondEntry.getValue();
                            PatrolAssetCheckDetail detail = new PatrolAssetCheckDetail();

                            // 设置字段值
                            detail.setDefect(toString(valueMap.get("defect")));
                            detail.setAdvice(toString(valueMap.get("advice")));
                            detail.setImage(toString(valueMap.get("image")));

                            thirdLevel.put(toString(secondEntry.getKey()), detail);
                        }

                        secondLevel.put(toString(firstEntry.getKey()), thirdLevel);
                    }

                    resultMap.put(entry.getKey(), secondLevel);
                } catch (Exception e) {
                    log.warn("处理key: {} 时出现异常: {}", entry.getKey(), e.getMessage());
                }
            }

            return resultMap;
        } catch (Exception e) {
            log.error("转换Redis数据失败", e);
            return null;
        }
    }

    private String toString(Object obj) {
        return obj != null ? obj.toString() : null;
    }

    /**
     * 根据需要的List PatrolPartsInfo创建一个新的list PatrolAssetCheckDetail,用于经常检查
     *
     * @param inspectionType InspectionType
     * @return List<PatrolAssetCheckDetail>
     */
    @Override
    public List<PatrolAssetCheckDetail> generatePatrolAssetCheckDetailList(InspectionType inspectionType, String checkId) {
        // 从redis中获取patrolPartsInfoList
        List<PatrolPartsInfo> patrolPartsInfoList =
                patrolPartsInfoService.selectByType(inspectionType);
        // 使用并行流来处理patrolPartsInfoList
        // 返回需要新增的patrolAssetCheckDetailList
        return patrolPartsInfoList.parallelStream()
                .map(patrolPartsInfo -> generatePatrolAssetCheckDetail(null, patrolPartsInfo, checkId, false, null))
                .toList();
    }

    /**
     * 根据已经存在的List PatrolAssetCheckDetail和需要的List PatrolPartsInfo创建一个新的list PatrolAssetCheckDetail
     *
     * @param patrolAssetCheck PatrolAssetCheck
     * @param logId            String
     * @return List<PatrolAssetCheckDetail>
     */
    @Override
    public List<PatrolAssetCheckDetail> generatePatrolAssetCheckDetailList(
            PatrolAssetCheck patrolAssetCheck, String logId, String userName) {
        List<PatrolAssetCheckDetail> patrolAssetCheckDetailList = patrolAssetCheck.getPatrolCheckDetailList();
        InspectionType inspectionType = patrolAssetCheck.getType();
        String checkId = patrolAssetCheck.getId();
        String assetId = patrolAssetCheck.getAssetId();

        // 判断patrolAssetCheckDetailList是否为空或者size为0
        checkId = Optional.ofNullable(checkId)
                .orElseGet(() -> patrolAssetCheckDetailList == null || patrolAssetCheckDetailList.isEmpty()
                        ? null
                        : patrolAssetCheckDetailList.get(0).getCheckId());

        // 获取patrolAssetCheckDetailList中所有的partsTypeId，并存入Set中
        Set<String> existingPartsTypeIds = Optional.ofNullable(patrolAssetCheckDetailList)
                .orElse(Collections.emptyList())
                .stream()
                .map(PatrolAssetCheckDetail::getPartsTypeId)
                .collect(Collectors.toSet());
        // 从redis中获取patrolPartsInfoList
        String cacheKey = "parts:info:list:" + inspectionType.getCode();
        List<PatrolPartsInfo> patrolPartsInfoList = partsInfoCache.getIfPresent(cacheKey);
        if (patrolPartsInfoList == null) {
            // 如果缓存中没有，从服务中获取
            patrolPartsInfoList = patrolPartsInfoService.selectByType(inspectionType);
            // 存入缓存
            if (patrolPartsInfoList != null && !patrolPartsInfoList.isEmpty()) {
                partsInfoCache.put(cacheKey, patrolPartsInfoList);
            }
        }
        Map<String, PatrolAssetCheckDetail> diseaseTypeMapping =
                getDiseaseTypeMapping(logId, inspectionType.getAssetType().getCode().toString(), assetId);
        // 使用并行流来处理patrolPartsInfoList
        String finalCheckId = checkId;
        // 返回需要新增的patrolAssetCheckDetailList
        return Optional.ofNullable(patrolPartsInfoList).orElse(Collections.emptyList()).parallelStream()
                .filter(patrolPartsInfo -> !existingPartsTypeIds.contains(patrolPartsInfo.getId()))
                .map(patrolPartsInfo -> generatePatrolAssetCheckDetail(
                        diseaseTypeMapping == null ? null : diseaseTypeMapping.get(patrolPartsInfo.getId()),
                        patrolPartsInfo, finalCheckId, true, userName))
                .collect(Collectors.toList());
    }

    /**
     * 根据PatrolPartsInfo创建一个新的PatrolAssetCheckDetail
     *
     * @param patrolPartsInfo PatrolPartsInfo
     * @param checkId         String
     * @param flag            true:(日常检查) false:(经常检查)
     * @return PatrolAssetCheckDetail
     */
    private PatrolAssetCheckDetail generatePatrolAssetCheckDetail(
            PatrolAssetCheckDetail patrolAssetCheckDetail, PatrolPartsInfo patrolPartsInfo,
            String checkId, Boolean flag, String userName) {
        if (patrolAssetCheckDetail == null) {
            patrolAssetCheckDetail = new PatrolAssetCheckDetail();
        }
        // id
        patrolAssetCheckDetail.setId(IdWorker.getIdStr());
        //资产检查id(主表)
        patrolAssetCheckDetail.setCheckId(checkId);

        //检查明细类型id
        patrolAssetCheckDetail.setPartsTypeId(patrolPartsInfo.getId());
        //检查明细类型名称
        patrolAssetCheckDetail.setPartsTypeName(patrolPartsInfo.getPartsName());
        if (patrolAssetCheckDetail.getDefect() == null) {
            patrolAssetCheckDetail.setDefect(flag ? "未见异常" : null);
        }
        if (patrolAssetCheckDetail.getAdvice() == null) {
            patrolAssetCheckDetail.setAdvice(flag ? "正常保养" : null);
        }
        //图形
//        patrolAssetCheckDetail.setImage(null);
        //删除标识
        patrolAssetCheckDetail.setDelFlag(DeleteFlagType.NOT_DELETED);
        //备注
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日-HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        String formattedDateTime = LocalDateTime.now().format(formatter);
        patrolAssetCheckDetail.setRemark("未填写，由系统生成。生成时间：" + formattedDateTime);
        // 描述 检查内容 缺损类型
        patrolAssetCheckDetail.setDes(patrolPartsInfo.getDes());

        //create_time
        patrolAssetCheckDetail.setCreateTime(now);
        //update_time
        patrolAssetCheckDetail.setUpdateTime(now);
        String userBy = userName == null ? "system" : userName;
        //create_by
        patrolAssetCheckDetail.setCreateBy(userBy);
        //update_by
        patrolAssetCheckDetail.setUpdateBy(userBy);

        return patrolAssetCheckDetail;
    }

    /**
     * 保存saveAll
     *
     * @param patrolAssetCheckDetailList List<PatrolAssetCheckDetail>
     * @param tableName                  String
     * @return Boolean
     */
    @Override
    public Boolean saveAll(List<PatrolAssetCheckDetail> patrolAssetCheckDetailList, String tableName) {
        DynamicDataSourceContextHolder.push("slave");
        Boolean result = baseMapper.insertBatch(patrolAssetCheckDetailList, tableName);
        DynamicDataSourceContextHolder.clear();
        return result;
    }

    /**
     * 批量查询
     *
     * @param checkId
     * @return List<PatrolAssetCheckDetail>
     */
    @Override
    public List<PatrolAssetCheckDetail> selectByCheckId(List<String> checkId, String tableName) {
        if (checkId == null || checkId.isEmpty()) {
            return null;
        }
        return baseMapper.selectByCheckIds(checkId, tableName);
    }

    /**
     * 传入patrolDetailList，返回List<PatrolDiseaseDTO>
     *
     * @param patrolDetailList List<PatrolAssetCheckDetail>
     * @return List<PatrolDiseaseDTO>
     */
    @Override
    public List<PatrolDiseaseDTO> diseaseNumber(List<PatrolAssetCheckDetail> patrolDetailList) {
        if (patrolDetailList == null || patrolDetailList.isEmpty()) {
            return Collections.emptyList();
        }

        // 按checkId分组，统计每组中不正常的数量
        Map<String, Long> diseaseCountMap = patrolDetailList.stream()
                .filter(detail -> !isNormal(detail.getDefect(),detail.getAdvice(),detail.getDes()))
                .collect(Collectors.groupingBy(
                        PatrolAssetCheckDetail::getCheckId,
                        Collectors.counting()
                ));

        // 转换为PatrolDiseaseDTO列表，只返回异常数量大于1的记录
        return diseaseCountMap.entrySet().stream()
                .map(entry -> new PatrolDiseaseDTO(
                        entry.getKey(),
                        entry.getValue().intValue()
                ))
                .collect(Collectors.toList());
    }

    private static final List<String> NORMAL_ADVICES = Arrays.asList(
            "正常保养","/","情况正常","-","无","未见异常","正常","定期检查","空","情","否","S","\\","经常性检查","无·", "无正常", "未无", "见异常未", "未", "无无", "0无",
            "无无/", "无否", "无无无", "/无", "无明缺损",
            "无异常", "无缺失", "/无异常", "基无异常", "无常异",
            "无加强巡查", "无下班后", "无语", "无明显缺陷",
            "无！", "无缺损", "无？", "无与", "桥无", "无常",
            "无异常v", "异常无", "无''", "我无", "玩无", "yi无",
            "无/", "去无"
    );

    private static final List<String> LIKE_NORMAL_ADVICES = Arrays.asList(
            "未发现病",
            "未发现异",
            "未见",
            "无异",
            "无破",
            "空无明显缺损",
            "定期检查"
    );

    private static final List<String> SPECIAL_PATTERN_ADVICES = Arrays.asList(
            "无破损，伸",
            "无破损伸"
    );

    private boolean isNormal(String defect,String advice,String des) {
        if (advice == null || advice.isEmpty()) {
            return true;
        }

        // 预处理：去除首尾空格和换行符
        String trimmedAdvice = advice.trim();

        // 精确匹配检查
        if (NORMAL_ADVICES.contains(trimmedAdvice)) {
            return true;
        }

        // 模糊匹配检查
        if (LIKE_NORMAL_ADVICES.stream()
            .anyMatch(trimmedAdvice::contains)) {
            return true;
        }

        // 特殊模式匹配检查
        if(SPECIAL_PATTERN_ADVICES.stream()
                .anyMatch(trimmedAdvice::startsWith)){
            return true;
        }else {
            return defect.equals("未见异常") || des.equals("未见异常");
        }

    }
}
