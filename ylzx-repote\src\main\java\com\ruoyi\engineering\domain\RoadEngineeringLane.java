package com.ruoyi.engineering.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;

/**
 * 涉路工程车道对象 road_engineering_lane
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@ApiModel(value="涉路工程车道")
@TableName("road_engineering_lane")
@Data
public class RoadEngineeringLane extends BaseTableEntity{
    private static final long serialVersionUID = 1L;


    /** 涉路工程ID */
    @Excel(name = "涉路工程ID")
    @ApiModelProperty(value = "涉路工程ID")
    private String roadEngineeringId;

    /** 车道信息 */
    @Excel(name = "车道信息")
    @ApiModelProperty(value = "车道信息")
    private String lane;


}
