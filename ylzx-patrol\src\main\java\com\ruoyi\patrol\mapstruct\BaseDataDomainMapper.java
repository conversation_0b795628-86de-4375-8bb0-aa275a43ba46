package com.ruoyi.patrol.mapstruct;

import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseCulvertResponseCache;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface BaseDataDomainMapper {
    BaseDataDomainMapper INSTANCE = Mappers.getMapper(BaseDataDomainMapper.class);
    
    /**
     * 桥梁基础数据缓存转为BaseDataDomain
     */
    BaseDataDomain bridgeToBaseData(BaseBridgeResponseCache baseBridgeResponseCache);

    /**
     * 涵洞基础数据缓存转为BaseDataDomain
     */
    BaseDataDomain culvertToBaseData(BaseCulvertResponseCache baseCulvertResponseCache);

    /**
     * 隧道基础数据缓存转为BaseDataDomain
     */
    BaseDataDomain tunnelToBaseData(BaseTunnelResponseCache baseTunnelResponseCache);

}
