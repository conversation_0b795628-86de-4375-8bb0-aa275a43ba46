package com.ruoyi.other.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.other.domain.OtherTransportationAttachment;
import com.ruoyi.other.service.OtherTransportationAttachmentService;
import com.ruoyi.other.domain.OtherTransportation;
import com.ruoyi.repote.domain.RepoteForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api(tags = "大件运输上传文件")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/transportationAttachment")

public class OtherTransportationAttachmentController extends BaseController {
    @Resource
    private OtherTransportationAttachmentService otherTransportationAttachmentService;

    /**
     * 查询大件运输上传文件(分页)
     */
    @ApiOperation("查询大件运输上传文件")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> params) {
        params.put("order", "create_time desc");
        startPage();
        QueryWrapper<OtherTransportationAttachment> queryWrapper = new QueryWrapper<>();

        if (params.get("transportationId") != null) {
            queryWrapper.eq("transportation_id", params.get("transportationId"));
        }
        List<OtherTransportationAttachment> list = otherTransportationAttachmentService.findListByParam(params);
        return getDataTable(list);

    }


    /**
     * 新增大件运输附件
     */
    @ApiOperation("新增大件运输附件")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody OtherTransportationAttachment otherTransportationAttachment) {
//        otherTransportationAttachment.setStatus(0);
        System.out.println("ListADD: " + otherTransportationAttachment);
        otherTransportationAttachmentService.save(otherTransportationAttachment);
        return toAjax(true);
    }

    /**
     * 修改大件运输
     */
    @ApiOperation("修改大件运输附件")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody OtherTransportationAttachment otherTransportationAttachment) {
        return toAjax(otherTransportationAttachmentService.updateById(otherTransportationAttachment));
    }


    /**
     * 删除大件运输
     */
    @ApiOperation("删除大件运输附件")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        otherTransportationAttachmentService.removeById(id);
        return toAjax(true);
    }

    /**
     * 根据id查询大件运输附件
     */
    @ApiOperation("根据id查询大件运输附件")
//    //@RequiresPermissions("repote:repoteForm:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        OtherTransportationAttachment otherTransportationAttachment = otherTransportationAttachmentService.getById(id);
        if (otherTransportationAttachment == null) {
            return error("未查询到【大件运输附件】记录");
        }
        return success(otherTransportationAttachment);
    }

}
