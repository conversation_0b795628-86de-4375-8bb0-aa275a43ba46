package com.ruoyi.repote.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 填报格规范对象 repote_form
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="填报格规范")
@TableName("repote_form")
@Data
public class RepoteForm extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    @Excel(name = "任务ID")
    @ApiModelProperty(value = "任务ID")
    private String missionId;

    /** 填报表格名称 */
    @Excel(name = "填报表格名称")
    @ApiModelProperty(value = "填报表格名称")
    private String name;

    /** 填报表格要求 */
    @Excel(name = "填报表格要求")
    @ApiModelProperty(value = "填报表格要求")
    private String request;

    /** 填报模板地址 */
    @Excel(name = "填报模板地址")
    @ApiModelProperty(value = "填报模板地址")
    private String url;

    /** 填报表格备注 */
    @Excel(name = "填报表格备注")
    @ApiModelProperty(value = "填报表格备注")
    private String remark;

    /** 表头所在行数 */
    @Excel(name = "表头所在行数")
    @ApiModelProperty(value = "表头所在行数")
    private Integer headRow;

    /** 模板填报完成状态（1-已完成，0-未完成） */
    @Excel(name = "模板填报完成状态（1-已完成，0-未完成）")
    @ApiModelProperty(value = "模板填报完成状态（1-已完成，0-未完成）")
    private Integer formStatus;

    @ApiModelProperty(value = "报表已上传数量")
    @TableField(exist = false)
    private Long finishNum;

    @ApiModelProperty(value = "报表总数")
    @TableField(exist = false)
    private Long totalNum;

    @ApiModelProperty(value = "合并后的文件ID")
    private String ownerId;

    @ApiModelProperty(value = "截止时间")
    private LocalDateTime expiry;

}
