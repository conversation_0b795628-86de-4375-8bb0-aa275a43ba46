package com.ruoyi.repote.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileStorageService;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("file")
public class FileInfoController {
    final FileStorageService fileStorageService;

    @PostMapping("/uploadLocal")
    public FileInfo uploadLocal(MultipartFile file) {
        return fileStorageService.of(file).setPlatform("local").upload();
    }

}
