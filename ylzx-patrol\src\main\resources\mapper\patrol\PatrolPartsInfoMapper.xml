<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.PatrolPartsInfoMapper">

    <resultMap type="com.ruoyi.patrol.domain.PatrolPartsInfo" id="PatrolPartsInfoResult">
        <result property="partsType" column="parts_type"/>
        <result property="partsName" column="parts_name"/>
        <result property="partsCode" column="parts_code"/>
        <result property="des" column="des"/>
        <result property="extend" column="extend"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="base_column"> id, parts_type, parts_name, parts_code, des, extend, del_flag    </sql>



</mapper>