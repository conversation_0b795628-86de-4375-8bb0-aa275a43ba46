package com.ruoyi.patrol.controller;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.patrol.domain.PatrolAssetDiseases;
import com.ruoyi.patrol.domain.PatrolDiseaseAdvices;
import com.ruoyi.patrol.service.PatrolAssetDiseasesService;
import com.ruoyi.patrol.service.PatrolDiseaseAdvicesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 病害处置Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Api(tags = "病害处置" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/diseaseAdvices")
public class PatrolDiseaseAdvicesController extends BaseController {
    final private PatrolDiseaseAdvicesService patrolDiseaseAdvicesService;
    final private PatrolAssetDiseasesService patrolAssetDiseasesService;


    /**
     * 查询病害处置列表(分页)
     */
    @ApiOperation("查询病害处置列表")
    //@RequiresPermissions("patrol:diseaseAdvices:list")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> params) {
        QueryWrapper<PatrolDiseaseAdvices> qw = new QueryWrapper<>();
        qw.orderByAsc("sort");
        qw.orderByDesc("create_time");
        qw.eq(StringUtils.isNotEmpty(MapUtil.getStr(params,"diseaseId")),"disease_id",MapUtil.getStr(params,"diseaseId"));
        startPage();
        List<PatrolDiseaseAdvices> list = patrolDiseaseAdvicesService.list(qw);
        return getDataTable(fillDiseaseName(list));
    }

    /**
     * 查询病害处置列表(不分页)
     */
    @ApiOperation("查询病害处置列表(不分页)")
    //@RequiresPermissions("patrol:diseaseAdvices:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolDiseaseAdvices> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolDiseaseAdvices> list = patrolDiseaseAdvicesService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询病害处置数据
     */
    @ApiOperation("根据id查询病害处置数据")
    //@RequiresPermissions("patrol:diseaseAdvices:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            PatrolDiseaseAdvices patrolDiseaseAdvices = patrolDiseaseAdvicesService.getById(id);
        if (patrolDiseaseAdvices == null) {
            return error("未查询到【病害处置】记录");
        }
        return success(patrolDiseaseAdvices);
    }

    /**
     * 新增病害处置
     */
    @ApiOperation("新增病害处置")
    //@RequiresPermissions("patrol:diseaseAdvices:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolDiseaseAdvices patrolDiseaseAdvices) {
        return toAjax(patrolDiseaseAdvicesService.save(patrolDiseaseAdvices));
    }

    /**
     * 修改病害处置
     */
    @ApiOperation("修改病害处置")
    //@RequiresPermissions("patrol:diseaseAdvices:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolDiseaseAdvices patrolDiseaseAdvices) {
        return toAjax(patrolDiseaseAdvicesService.updateById(patrolDiseaseAdvices));
    }

    /**
     * 删除病害处置
     */
    @ApiOperation("删除病害处置")
    //@RequiresPermissions("patrol:diseaseAdvices:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolDiseaseAdvicesService.removeById(id));
    }

    /**
     * 导出病害处置列表
     */
    @ApiOperation("导出病害处置列表")
    //@RequiresPermissions("patrol:diseaseAdvices:export")
    @Log(title = "病害处置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolDiseaseAdvices> list = patrolDiseaseAdvicesService.list();
        ExcelUtil<PatrolDiseaseAdvices> util = new ExcelUtil<PatrolDiseaseAdvices>(PatrolDiseaseAdvices.class);
        util.exportExcel(response, list, "病害处置数据");
    }


    private List<PatrolDiseaseAdvices>  fillDiseaseName(List<PatrolDiseaseAdvices> patrolDiseaseAdvices){
        List<PatrolAssetDiseases> Diseaselist = patrolAssetDiseasesService.list();
        for(PatrolDiseaseAdvices diseaseAdvice : patrolDiseaseAdvices){
            String diseaseId = diseaseAdvice.getDiseaseId();
            List<PatrolAssetDiseases> collect = Diseaselist.stream().filter(item ->  diseaseId.equals(item.getId())).toList();
            if (collect.size() == 1){
                String diseaseName = collect.get(0).getDiseaseName();
                diseaseAdvice.setDiseaseName(diseaseName);
            }
        }
        return patrolDiseaseAdvices;
    }

}
