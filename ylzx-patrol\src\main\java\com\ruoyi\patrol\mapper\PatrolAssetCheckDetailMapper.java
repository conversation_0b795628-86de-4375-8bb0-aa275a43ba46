package com.ruoyi.patrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产寻检查子表Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
@Mapper
public interface PatrolAssetCheckDetailMapper extends BaseMapper<PatrolAssetCheckDetail> {

    Boolean insertBatch(@Param("list") List<PatrolAssetCheckDetail> patrolAssetCheckDetailList,
                        @Param("tableName") String tableName);

    List<PatrolAssetCheckDetail> selectByCheckIds(@Param("list") List<String> list, @Param("tableName") String tableName);

}
