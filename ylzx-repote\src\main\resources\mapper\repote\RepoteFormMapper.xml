<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repote.mapper.RepoteFormMapper">

    <resultMap type="com.ruoyi.repote.domain.RepoteForm" id="RepoteFormResult">
        <result property="id" column="id"/>
        <result property="missionId" column="mission_id"/>
        <result property="name" column="name"/>
        <result property="request" column="request"/>
        <result property="remark" column="remark"/>
        <result property="url" column="url"/>
        <result property="headRow" column="head_row"/>
        <result property="ownerId" column="owner_id"/>
        <result property="formStatus" column="form_status"/>
        <result property="expiry" column="expiry"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">
        id, mission_id, name, request, remark, url, head_row, owner_id, form_status, expiry, create_by,create_time,update_by,update_time    </sql>

    <sql id="where_column">
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="missionId != null and missionId != ''">
            AND mission_id = #{missionId}
        </if>
        <if test="missionIdLike != null and missionIdLike != ''">
            AND mission_id like CONCAT('%', #{missionIdLike}, '%')
        </if>
        <if test="name != null and name != ''">
            AND name = #{name}
        </if>
        <if test="request != null and request != ''">
            AND request = #{request}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}
        </if>
        <if test="url != null and url != ''">
            AND url = #{url}
        </if>
        <if test="headRow != null and headRow != ''">
            AND head_row = #{headRow}
        </if>
        <if test="formStatus != null and formStatus != ''">
            AND form_status = #{formStatus}
        </if><if test="ownerId != null and ownerId != ''">
            AND owner_id = #{ownerId}
        </if>
        <if test="expiry != null and expiry != ''">
            AND expiry = #{expiry}
        </if>
    </sql>

    <sql id="set_column">
        <if test="missionId != null">
            mission_id = #{missionId},
        </if>
        <if test="name != null">
            name = #{name},
        </if>
        <if test="request != null">
            request = #{request},
        </if>
        <if test="remark != null">
            remark = #{remark},
        </if>
        <if test="url != null">
            url = #{url},
        </if>
        <if test="headRow != null">
            head_row = #{headRow},
        </if>
        <if test="formStatus != null">
            form_status = #{formStatus},
        </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="RepoteFormResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_form
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="RepoteFormResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_form
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

</mapper>