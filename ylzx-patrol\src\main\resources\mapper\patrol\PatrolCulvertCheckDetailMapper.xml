<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patroltidb.mapper.PatrolCulvertCheckDetailMapper">

    <resultMap type="com.ruoyi.patroltidb.domain.PatrolCulvertCheckDetail" id="PatrolCulvertCheckDetailResult">
            <result property="checkId" column="check_id"/>
            <result property="partsTypeId" column="parts_type_id"/>
            <result property="defect" column="defect"/>
            <result property="advice" column="advice"/>
            <result property="image" column="image"/>
            <result property="delFlag" column="del_flag"/>
            <result property="partsTypeName" column="parts_type_name"/>
            <result property="remark" column="remark"/>
        <result property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">id, create_by, create_time, update_by, update_time,
 check_id, parts_type_id, defect, advice, image, del_flag, parts_type_name, remark    </sql>

    <sql id="where_column">
        <if test="ids != null and ids.size() > 0 ">
            AND id IN
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="checkId != null and checkId != ''">
            AND check_id = #{checkId}
        </if>
        <if test="checkIdLike != null and checkIdLike != ''">
            AND check_id like CONCAT('%', #{checkIdLike}, '%')
        </if>
        <if test="partsTypeId != null and partsTypeId != ''">
            AND parts_type_id = #{partsTypeId}
        </if>
        <if test="defect != null and defect != ''">
            AND defect = #{defect}
        </if>
        <if test="advice != null and advice != ''">
            AND advice = #{advice}
        </if>
        <if test="image != null and image != ''">
            AND image = #{image}
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag = #{delFlag}
        </if>
        <if test="partsTypeName != null and partsTypeName != ''">
            AND parts_type_name = #{partsTypeName}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}
        </if>
    </sql>

    <sql id="set_column">
            <if test="checkId != null">
                check_id = #{checkId},
            </if>
            <if test="partsTypeId != null">
                parts_type_id = #{partsTypeId},
            </if>
            <if test="defect != null">
                defect = #{defect},
            </if>
            <if test="advice != null">
                advice = #{advice},
            </if>
            <if test="image != null">
                image = #{image},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="partsTypeName != null">
                parts_type_name = #{partsTypeName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="PatrolCulvertCheckDetailResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_culvert_check_detail
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="PatrolCulvertCheckDetailResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_culvert_check_detail
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

</mapper>