package com.ruoyi.patroltidb.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 涵洞巡检查记录对象 patrol_culvert_check
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="涵洞巡检查记录")
@TableName("patrol_culvert_check")
@Data
public class PatrolCulvertCheck extends PatrolAssetCheck {
    @Serial
    private static final long serialVersionUID = 1L;


    /** 状态list */
    @TableField(exist = false)
    List<String> statusList;


    /** 隧道巡检查详情 */
    @TableField(exist = false)
    @JsonProperty("details")
    List<PatrolCulvertCheckDetail> patrolCulvertCheckDetailList;

}

