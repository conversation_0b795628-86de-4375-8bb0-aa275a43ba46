server:
  port: 9301
spring:
  application:
    # 应用名称
    name: ylzx-patrol
  profiles:
    # 环境配置
    active: dev
  cloud:
    inetutils:
      preferred-networks:
        - 172.18.0
        - 192.168.10
    #      ignored-interfaces:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 192.168.101.121:8848
        username: nacos
        password: 'nacos@abc123!'
      config:
        # 配置中心地址
        server-addr: 192.168.101.121:8848
        username: nacos
        password: 'nacos@abc123!'
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - common-redis.yml
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

---
spring:
  jvm:
    opts: -XX:ReservedCodeCacheSize=1024m -XX:InitialCodeCacheSize=512m
  datasource:
    dynamic:
      primary: master
      datasource:
        # 主库数据源
        master:
          # driver-class-name: com.mysql.cj.jdbc.Driver
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: **************************************************************************************************************************************************************************************************************
          username: root
          password: 'Ylzx@2000+!#-2'
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            minimum-idle: 5
            maximum-pool-size: 200
            is-auto-commit: true
            idle-timeout: 60000
            max-lifetime: 300000
            connection-timeout: 60000
            validation-timeout: 60000
            connection-test-query: SELECT 1
            keepalive-time: 30000
        slave:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          url: ****************************************************************************************************************************************************************************************************************************************
          username: root
          password: 'Ylzx@2008-*2#3+'
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            minimum-idle: 5
            maximum-pool-size: 100
            is-auto-commit: true
            idle-timeout: 60000
            max-lifetime: 300000
            connection-timeout: 60000
            validation-timeout: 60000
            connection-test-query: SELECT 1
            keepalive-time: 30000

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  #configLocation: classpath:mybatis/mybatis-config.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    enable-sql-runner: true
# swagger配置
swagger:
  title: 智慧养护管理平台-巡检查接口
  license: Powered By ylzx
  licenseUrl: https://ruoyi.vip
knife4j:
  # 开启增强配置
  enable: true
  # 开启生产环境屏蔽
  production: false
  # basic:
  #   # 是否开启认证
  #   enable: true
  #   # 用户名
  #   username: root
  #   # 密码
  #   password: root