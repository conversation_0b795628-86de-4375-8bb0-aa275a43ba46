package com.ruoyi.patroltidb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheckDetail;
import com.ruoyi.patroltidb.domain.TestFileImg;
import com.ruoyi.patroltidb.mapper.PatrolBridgeCheckDetailMapper;
import com.ruoyi.patroltidb.service.PatrolBridgeCheckDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 桥梁巡检查子Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Service
@Slave
public class PatrolBridgeCheckDetailServiceImpl extends ServiceImpl<PatrolBridgeCheckDetailMapper, PatrolBridgeCheckDetail> implements PatrolBridgeCheckDetailService {

//    @Autowired
//    private PatrolBridgeCheckDetailMapper patrolBridgeCheckDetailMapper;

    @Override
    public List<PatrolBridgeCheckDetail> findListByParam(Map<String, Object> params) {
        return baseMapper.findListByParam(params);
    }

    @Override
    public List<TestFileImg> findFileImgList(Map<String, Object> params) {
        return baseMapper.findFileImgList(params);
    }

    @Override
    public Long batchUpdate(Map<String, Object> params) {
        return baseMapper.batchUpdate(params);
    }





}
