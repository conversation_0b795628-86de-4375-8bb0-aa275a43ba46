package com.ruoyi.other.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.other.domain.OtherTransportationDept;
import com.ruoyi.other.domain.OtherTransportationRoad;
import com.ruoyi.other.mapper.OtherTransportationRoadMapper;
import com.ruoyi.other.service.OtherTransportationRoadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 大件运输Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class OtherTransportationRoadServiceImpl extends ServiceImpl<OtherTransportationRoadMapper, OtherTransportationRoad> implements OtherTransportationRoadService {
    @Resource
    private OtherTransportationRoadMapper otherTransportationRoadMapper;

    @Override
    public List<OtherTransportationRoad> findListByParam(Map<String, Object> params) {
        return otherTransportationRoadMapper.findListByParam(params);
    }

    @Override
    public List<String> getRoadIdsByTransportationId(String id) {
        return otherTransportationRoadMapper.getRoadIdsByTransportationId(id);
    }


    @Override
    public boolean updateRoadByTransportationId(String transportationId, String roadId, String roadName) {
        // 创建要更新的对象，并设置要更新的字段
        OtherTransportationRoad updateDept = new OtherTransportationRoad();
        updateDept.setRoadId(roadId);
        updateDept.setRoadName(roadName);

        // 构建查询条件，匹配 transportationId
        QueryWrapper<OtherTransportationRoad> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("transportation_id", transportationId);

        // 执行更新操作
        int result = otherTransportationRoadMapper.update(updateDept, queryWrapper);

        // 返回更新操作是否成功
        return result > 0;
    }
    public boolean removeByTransportationId(String transportationId) {

        return otherTransportationRoadMapper.deleteByTransportationIdAndRoadId(transportationId) > 0;
    }
}
