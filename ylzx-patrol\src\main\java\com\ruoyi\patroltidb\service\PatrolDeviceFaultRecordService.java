package com.ruoyi.patroltidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patroltidb.domain.PatrolDeviceFaultRecord;

import java.util.List;
import java.util.Map;

/**
 * 机电故障上报Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-29
 */
public interface PatrolDeviceFaultRecordService extends IService<PatrolDeviceFaultRecord> {

    /**
     * 根据条件查询机电故障上报数据列表
     * @param params
     */
    List<PatrolDeviceFaultRecord> findListByParam(Map<String, Object> params);

}
