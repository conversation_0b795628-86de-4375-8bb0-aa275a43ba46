package com.ruoyi.engineering.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.engineering.domain.RoadEngineeringAttachment;
import com.ruoyi.engineering.domain.RoadEngineeringLane;

import java.util.List;
import java.util.Map;

/**
 * 涉路工程Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
public interface RoadEngineeringAttachmentService extends IService<RoadEngineeringAttachment> {

    /**
     * 根据条件查询涉路工程数据列表
     * @param params
     */
    List<RoadEngineeringAttachment> findListByParam(Map<String, Object> params);

}
