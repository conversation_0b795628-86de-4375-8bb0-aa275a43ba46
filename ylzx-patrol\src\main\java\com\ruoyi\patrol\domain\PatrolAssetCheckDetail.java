package com.ruoyi.patrol.domain;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import com.ruoyi.patrol.enums.DeleteFlagType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 资产寻检查子对象 patrol_asset_check_detail
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="资产寻检查子")
@TableName("patrol_asset_check_detail")
@Data
public class PatrolAssetCheckDetail extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 资产检查id MPS_T_D_AssetCheck */
    @Excel(name = "资产检查id MPS_T_D_AssetCheck")
    @ApiModelProperty(value = "资产检查id MPS_T_D_AssetCheck")
    private String checkId;

    /** 检查明细类型id MPS_T_S_PartsType */
    @Excel(name = "检查明细类型id MPS_T_S_PartsType")
    @ApiModelProperty(value = "检查明细类型id MPS_T_S_PartsType")
    private String partsTypeId;

    /** 检查明细类型名称 */
    @Excel(name = "检查明细类型名称")
    @ApiModelProperty(value = "检查明细类型名称")
    private String partsTypeName;

    /** 缺损类型, 状态描述 */
    @Excel(name = "缺损类型, 状态描述")
    @ApiModelProperty(value = "缺损类型, 状态描述")
    private String defect = "未见异常";

    /** 缺损范围 保养措施建议 */
    @Excel(name = "缺损范围 保养措施建议")
    @ApiModelProperty(value = "缺损范围 保养措施建议")
    private String advice = "正常保养";

    /** 图像 */
    @Excel(name = "图像")
    @ApiModelProperty(value = "图像")
    private String image;

    /** 删除标识 */
    @ApiModelProperty(value = "删除标识")
    @EnumValue
    private DeleteFlagType delFlag = DeleteFlagType.NOT_DELETED;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**描述 检查内容 缺损类型**/
    @Excel(name = "描述 检查内容 缺损类型")
    @ApiModelProperty(value = "描述 检查内容 缺损类型")
    private String des;

    /** 是否为不检查项 */
    @TableField(exist = false)
    private Boolean ignore = false;
}
