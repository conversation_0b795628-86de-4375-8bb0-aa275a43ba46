package com.ruoyi.patrol.domain.cache;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月29日 12:44
 */
@Data
public class BaseDataCache implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String id;

    private String assetId;

    private Double longitude;


    private Double latitude;

    private String managementMaintenanceId;

    private String managementMaintenanceName;

    private String managementMaintenanceBranchId;

    private String managementMaintenanceBranchName;

    private String maintenanceSectionId;

    private String maintenanceSectionName;

    private Boolean isCheck;

    private String isCheckStr;

    private String dateStr;

    private String type;

    private String routeId;

    private String routeName;

    private String routeCode;

    private Double centerStake;
}
