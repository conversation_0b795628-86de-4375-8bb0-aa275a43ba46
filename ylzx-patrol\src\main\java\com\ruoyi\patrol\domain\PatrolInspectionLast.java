package com.ruoyi.patrol.domain;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.patrol.enums.AssetType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * (PatrolInspectionLast)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-17 15:01:09
 */

@Data
@ToString
@ApiModel(value="日常巡查最近生成时间", description="日常巡查最近生成时间")
@TableName("patrol_inspection_last")
public class PatrolInspectionLast implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "资产编码")
    @TableField(value = "asset_id")
    private String assetId;


    @ApiModelProperty(value = "日常巡查最近生成时间")
    @TableField(value = "generated_at")
    private LocalDateTime generatedAt;

    @ApiModelProperty(value = "经常巡查最近生成时间")
    @TableField(value = "generated_by")
    private LocalDateTime generatedBy;

    @ApiModelProperty(value = "资产类型（1-桥梁，2-隧道，3-涵洞）")
    @TableField(value = "type")
    @EnumValue
    private AssetType type;

}

