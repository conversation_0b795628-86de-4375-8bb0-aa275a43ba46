package com.ruoyi.patrol.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 巡查车辆实时坐标（15分钟过期）
 * @author: sfc
 * @date: 2025年03月17日 15:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RealCoordinateLogVO {

    @ApiModelProperty(value = "巡检日志id")
    private String logId;

    @ApiModelProperty(value = "管理处ID")
    private Long deptId;

    @ApiModelProperty(value = "管理处")
    private String deptName;

    @ApiModelProperty(value = "养护路段")
    private String maintenanceSectionName;

    @ApiModelProperty(value = "车牌号")
    private String carNum;

    @ApiModelProperty(value = "开始巡查时间")
    private Date startTime;

    @ApiModelProperty(value = "巡查状态（0-正在巡查，1-完成巡查）")
    private Integer status;

    @ApiModelProperty(value = "巡查人员(逗号分割)")
    private String personnelName;

    @ApiModelProperty(value = "巡查里程（km）")
    private BigDecimal patrolMileage;

    @ApiModelProperty(value = "巡查发现的养护事件数量")
    private Integer diseaseNum;

    @ApiModelProperty(value = "巡查车类型（1-普通巡查车，2-AI巡查车）")
    private Integer catType;

    @ApiModelProperty(value = "地理空间坐标（WKT）")
    private String lastPoint;

    @ApiModelProperty(value = "联系电话")
    private String phoneNumber;

    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

}
