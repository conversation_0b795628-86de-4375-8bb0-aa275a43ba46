package com.ruoyi.engineering.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 涉路工程车道对象 road_engineering_lane
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@ApiModel(value="涉路工程车道")
@TableName("road_engineering_lane")
@Data
public class RoadEngineeringAttachment extends BaseTableEntity{
    private static final long serialVersionUID = 1L;


    /** 涉路工程ID */
    @Excel(name = "涉路工程ID")
    @ApiModelProperty(value = "涉路工程ID")
    private String roadEngineeringId;


    /** 附件名称 */
    @Excel(name = "附件名称")
    @ApiModelProperty(value = "附件名称")
    private String name;

    /** 附件类型 */
    @Excel(name = "附件类型")
    @ApiModelProperty(value = "附件类型")
    private String attachmentType;


    /** 上传附件地址  */
    @Excel(name = "上传附件地址")
    @ApiModelProperty(value = "上传附件地址")
    private String url;

}
