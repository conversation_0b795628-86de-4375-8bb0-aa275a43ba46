package com.ruoyi.patroltidb.controller;

import cn.hutool.core.map.MapUtil;
import cn.idev.excel.EasyExcel;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.context.SecurityContextHolder;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.common.core.utils.map.Paramap;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.patrol.service.handler.impl.DeviceDailyReportHandler;
import com.ruoyi.patrol.service.handler.impl.DeviceFaultRecordHandler;
import com.ruoyi.patrol.service.handler.impl.DeviceOftenReportHandler;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheck;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheckDetail;
import com.ruoyi.patroltidb.domain.PatrolDeviceFaultRecord;
import com.ruoyi.patroltidb.domain.excel.DeviceCheckDailyExport;
import com.ruoyi.patroltidb.domain.excel.DeviceCheckOftenExport;
import com.ruoyi.patroltidb.domain.excel.DeviceMonthlyReportExport;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.patroltidb.service.PatrolDeviceCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolDeviceCheckService;
import com.ruoyi.patroltidb.service.PatrolDeviceFaultRecordService;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static com.ruoyi.patrol.enums.InspectionType.DEVICE_DAILY_INSPECTION;
import static com.ruoyi.patrol.enums.InspectionType.DEVICE_REGULAR_INSPECTION;

/**
 * 隧道机电日常巡查Controller
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
@Api(tags = "隧道机电日常巡查" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/deviceCheck")
public class PatrolDeviceCheckController extends BaseController {
    final private PatrolDeviceCheckService patrolDeviceCheckService;
    @Resource
    private PatrolDeviceCheckDetailService patrolDeviceCheckDetailService;
    @Resource
    private PatrolDeviceFaultRecordService patrolDeviceFaultRecordService;
    @Resource
    private RemoteDeptAuthService deptAuthService;
    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;
    final private RemoteUserService remoteUserService;

    /**
     * 查询隧道机电日常巡查列表(分页)
     */
    @ApiOperation("查询隧道机电日常巡查列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assetId", value = "隧道id", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "recordType", value = "隧道机电检查类型，’1‘：机电经常检查，’2‘：机电日常巡查", paramType = "query", dataType = "string")
    })
    //@RequiresPermissions("patrol:deviceCheck:list")
    @GetMapping("/list")
    public TableDataInfo list(@ApiIgnore  @RequestParam Map<String, Object> params) {
        Integer recordType = MapUtil.getInt(params, "recordType");
        Long userId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(userId)) {
            R<List<String>> userTunnelIds = deptAuthService.findUserTunnelIds(userId);
            if (userTunnelIds.getCode() == 200 && !userTunnelIds.getData().isEmpty()) {
                params.put("tunnelIdList", userTunnelIds.getData());
            } else {
                return getDataTable(new ArrayList<>());
            }
        }
        List list;
        startPage();
        if (null != recordType && recordType == 3) {//故障上报
            list = patrolDeviceCheckService.findFaultListByParam(params);
        } else {
            list = patrolDeviceCheckService.findDetailListByParam(params);
        }
        return getDataTable(list);
    }

    /**
     * 根据条件获取数量
     */
    @ApiOperation("根据条件获取数量")
    //@RequiresPermissions("patrol:assetCheck:getTotalCount")
    @GetMapping("/getAssetTotalCount")
    public AjaxResult getAssetTotalCount(@RequestParam Map<String, Object> params) {
        Integer recordType = MapUtil.getInt(params, "recordType");
        Long userId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(userId)) {
            R<List<String>> userTunnelIds = deptAuthService.findUserTunnelIds(userId);
            if (userTunnelIds.getCode() == 200 && !userTunnelIds.getData().isEmpty()) {
                params.put("tunnelIdList", userTunnelIds.getData());
            } else {
                return success(0);
            }
        }
        if (MapUtil.getStr(params, "ids") != null) {
            params.put("ids", List.of(MapUtil.getStr(params, "ids").split(",")));
        }
        Long count;
        if (null != recordType && recordType == 3) {//故障上报
            count = patrolDeviceCheckService.findFaultListByParamCount(params);
        } else {
            count = patrolDeviceCheckService.findDetailListByParamCount(params);
        }
        return success(count);
    }

     /**
     * APP查询隧道机电日常巡查列表(分页)
     */
    @ApiOperation("APP查询隧道机电日常巡查列表")
    //@RequiresPermissions("patrol:deviceCheck:list")
    @GetMapping("/appList")
    public TableDataInfo appList(@RequestParam Map<String, Object> params) {
        List<PatrolDeviceCheck> list = patrolDeviceCheckService.findListByParam(params);
        return getDataTable(list);
    }

    /**
     * 故障月报查询
     */
    @ApiOperation("故障月报查询")
    //@RequiresPermissions("patrol:deviceCheck:list")
    @GetMapping("/listMonthlyReport")
    public TableDataInfo listMonthlyReport(@RequestParam Map<String, Object> params) {
        Long userId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(userId)) {
            R<List<String>> userTunnelIds = deptAuthService.findUserTunnelIds(userId);
            if (userTunnelIds.getCode() == 200 && !userTunnelIds.getData().isEmpty()) {
                params.put("tunnelIdList", userTunnelIds.getData());
            } else {
                return getDataTable(new ArrayList<>());
            }
        }
        startPage();
        List<DeviceMonthlyReportExport> list = patrolDeviceCheckService.listMonthlyReport(params);
        return getDataTable(list);
    }

    /**
     * 根据资产ID查询故障月报主表
     */
    @ApiOperation("根据资产ID查询故障月报主表")
    //@RequiresPermissions("patrol:deviceCheck:list")
    @GetMapping("/getByAssetId")
    public AjaxResult getByAssetId(@RequestParam(name = "assetId", defaultValue = "1") String assetId,
                                   @RequestParam(name = "year", defaultValue = "2025") Integer year,
                                   @RequestParam(name = "month", defaultValue = "1") Integer month) {
        List<DeviceMonthlyReportExport> list = patrolDeviceCheckService.listMonthlyReport(Paramap.create().put("assetId", assetId).put("year", year).put("month", month));
        return success(list.isEmpty()?null:list.get(0));
    }

    /**
     * 根据资产ID时间查询故障月报子表
     */
    @ApiOperation("根据资产ID查询故障月报子表")
    //@RequiresPermissions("patrol:deviceCheck:list")
    @GetMapping("/getByAssetIdFault")
    public AjaxResult getByAssetIdFault(@RequestParam(name = "assetId", defaultValue = "1") String assetId,
                                   @RequestParam(name = "year", defaultValue = "2025") Integer year,
                                   @RequestParam(name = "month", defaultValue = "1") Integer month) {
        List<PatrolDeviceFaultRecord> list = patrolDeviceCheckService.getByAssetIdFault(Paramap.create().put("assetId", assetId).put("year", year).put("month", month));
        return success(list.isEmpty()?null:list);
    }

    /**
     * 查询隧道机电日常巡查列表(不分页)
     */
    @ApiOperation("查询隧道机电日常巡查列表(不分页)")
    //@RequiresPermissions("patrol:deviceCheck:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolDeviceCheck> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolDeviceCheck> list = patrolDeviceCheckService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询隧道机电日常巡查数据
     */
    @ApiOperation("根据id查询隧道机电日常巡查数据")
    //@RequiresPermissions("patrol:deviceCheck:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        PatrolDeviceCheckDetail deviceCheckDetail = patrolDeviceCheckDetailService.getById(id);
        PatrolDeviceCheck deviceCheck = patrolDeviceCheckService.getById(deviceCheckDetail.getCheckId());
        if (deviceCheck == null) {
            return error("未查询到【隧道机电日常巡查】记录");
        }
        deviceCheck.setDomains(List.of(deviceCheckDetail));
        R<SysDept> r = deptAuthService.getById(deviceCheck.getDomainId());
        if (r.getCode() == 200) {
            deviceCheck.setDeptName(r.getData().getDeptName());
        }
        return success(deviceCheck);
    }

    /**
     * 根据id查询隧道机电日常巡查数据(App查询详情)
     */
    @ApiOperation("App根据id查询隧道机电日常巡查数据")
    //@RequiresPermissions("patrol:deviceCheck:query")
    @GetMapping(value = "/getByCheckId/{id}")
    public AjaxResult getByCheckId(@PathVariable String id) {
        PatrolDeviceCheck deviceCheck = patrolDeviceCheckService.getById(id);
        List<PatrolDeviceCheckDetail> checkDetailList = patrolDeviceCheckDetailService.findListByParam(Paramap.create()
                .put("checkId", id));
        if (deviceCheck == null) {
            return error("未查询到【隧道机电日常巡查】记录");
        }
        deviceCheck.setDomains(checkDetailList);
        return success(deviceCheck);
    }

    /**
     * 新增隧道机电日常巡查
     */
    @ApiOperation("新增隧道机电日常巡查")
    //@RequiresPermissions("patrol:deviceCheck:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolDeviceCheck patrolDeviceCheck) {
        final Integer recordType = patrolDeviceCheck.getRecordType();
        if (recordType != 3) {
            patrolDeviceCheck.setType(recordType ==1?DEVICE_REGULAR_INSPECTION:DEVICE_DAILY_INSPECTION);
            patrolAssetCheckService.setExpiryAndFrequency(patrolDeviceCheck);
        }
        patrolDeviceCheckService.save(patrolDeviceCheck);
        if (!patrolDeviceCheck.getDomains().isEmpty() && recordType != 3) {
            patrolDeviceCheck.getDomains().forEach(item -> item.setCheckId(patrolDeviceCheck.getId()));
            patrolDeviceCheckDetailService.saveBatch(patrolDeviceCheck.getDomains());
        }
        if (!patrolDeviceCheck.getFaultRecordList().isEmpty() && recordType == 3) {
            for (PatrolDeviceFaultRecord record : patrolDeviceCheck.getFaultRecordList()) {
                record.setCheckId(patrolDeviceCheck.getId());
                record.setReportTime(new Date());
                //修复时间暂时不填
//                record.setRepairTime(new Date());
                record.setRepairFlag(0);
            }
            patrolDeviceFaultRecordService.saveBatch(patrolDeviceCheck.getFaultRecordList());
        }
        return toAjax(true);
    }

    /**
     * 批量新增隧道机电日常巡查
     */
    @ApiOperation("批量新增隧道机电日常巡查")
    //@RequiresPermissions("patrol:deviceCheck:add")
    @PostMapping("/addAll")
    public AjaxResult addAll(@Validated @RequestBody PatrolDeviceCheck patrolDeviceCheck) {
        patrolDeviceCheckService.batchAddAll(patrolDeviceCheck);
        return toAjax(true);
    }

    /**
     * 修改隧道机电日常巡查
     */
    @ApiOperation("修改隧道机电日常巡查")
    //@RequiresPermissions("patrol:deviceCheck:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolDeviceCheck patrolDeviceCheck) {
        final Integer recordType = patrolDeviceCheck.getRecordType();
        if (recordType == 3) {
            if (!patrolDeviceCheck.getFaultRecordList().isEmpty()) {
                for (PatrolDeviceFaultRecord record : patrolDeviceCheck.getFaultRecordList()) {
                    patrolDeviceFaultRecordService.updateById(record);
                }
            }
        } else {
            if (!patrolDeviceCheck.getDomains().isEmpty()) {
                for (PatrolDeviceCheckDetail checkDetail : patrolDeviceCheck.getDomains()) {
                    patrolDeviceCheckDetailService.updateById(checkDetail);
                }
            }
        }
        return toAjax(patrolDeviceCheckService.updateById(patrolDeviceCheck));
    }

    /**
     * 删除数据
     *
     * @param idList 主键集合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public AjaxResult delete(@RequestBody List<String> idList) {

        return success(this.patrolDeviceCheckService.removeByIds(idList));
    }

    /**
     * 导出隧道机电日常巡查列表
     */
    @ApiOperation("导出隧道机电日常巡查列表")
    //@RequiresPermissions("patrol:deviceCheck:export")
    @Log(title = "导出隧道机电日常巡查列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestParam Map<String, Object> params, HttpServletResponse response) {
        Integer recordType = MapUtil.getInt(params, "recordType");
        String ids = MapUtil.getStr(params, "ids");
        if (StringUtils.isNotBlank(ids)) {
            params.put("ids", List.of(ids.split(",")));
        } else {
            Long userId = SecurityUtils.getUserId();
            if (!SecurityUtils.isAdmin(userId)) {
                R<List<String>> userTunnelIds = deptAuthService.findUserTunnelIds(userId);
                if (userTunnelIds.getCode() == 200 && !userTunnelIds.getData().isEmpty()) {
                    params.put("tunnelIdList", userTunnelIds.getData());
                } else {
                    throw new ServiceException("用户隧道资产权限不足!");
                }
            }
        }
        if (null != recordType) {
            switch (recordType) {
                case 1:
                    processAndExport(patrolDeviceCheckService.findDetailListByParam(params), DeviceCheckOftenExport.class, response, "隧道机电经常检查数据");
                    break;
                case 2:
                    processAndExport(patrolDeviceCheckService.findDetailListByParam(params), DeviceCheckDailyExport.class, response, "隧道机电日常巡查数据");
                    break;
                case 3:
                    List<PatrolDeviceFaultRecord> faultList = patrolDeviceCheckService.findFaultListByParam(params);
                    int i = 1;
                    for (PatrolDeviceFaultRecord item : faultList) item.setOrderNumber(i++);
                    ExcelUtil<PatrolDeviceFaultRecord> faultUtil = new ExcelUtil<>(PatrolDeviceFaultRecord.class);
                    faultUtil.exportExcel(response, faultList, "隧道机电故障上报数据");
                    break;
            }
        }
    }

    private <T> void processAndExport(List<PatrolDeviceCheckDetail> sourceList, Class<T> targetClass, HttpServletResponse response, String title) {
        List<T> targetList = new ArrayList<>();
        int i = 1;
        try {
            for (PatrolDeviceCheckDetail source : sourceList) {
                source.setOrderNumber(i++);
                T target = targetClass.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(source, target);
                targetList.add(target);
            }
            ExcelUtil<T> util = new ExcelUtil<>(targetClass);
            util.exportExcel(response, targetList, title);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }
    }

    @ApiOperation("积木报表分页查询")
    @GetMapping("/listByJm")
    //@RequiresPermissions("patrol:deviceCheck:listByJm")
    public JSONObject listByJm(
            @RequestParam(name = "recordType", defaultValue = "1") Integer recordType,
            @RequestParam(name = "oprTimes", required = false) String oprTimes,
            @RequestParam(name = "oprTimee", required = false) String oprTimee,
            @RequestParam(name = "assetName", required = false) String assetName,
            @RequestParam(name = "domainIds", required = false) String domainIds,
            @RequestParam(name = "cids", required = false) String cids,
            @RequestParam(name = "pageNo", defaultValue = "1") Long pageNo,
            @RequestParam(name = "pageSize", defaultValue = "100000") Long pageSize,
            HttpServletRequest response) {
        log.error("/deviceCheck/listByJm,  传参： recordType={}，oprTimes={}， oprTimee={}，assetName={}，domainIds={}，cids={}，pageNo={}，pageSize={}"
                , recordType, oprTimes, oprTimee, assetName, domainIds, cids, pageNo, pageSize);
        LoginUser loginUser = SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        Long userId = null;
        if (loginUser == null) {
            String userIdHeader = response.getHeader("userid");
            if (StringUtils.isNotBlank(userIdHeader)) {
                try {
                    userId = Long.valueOf(userIdHeader);
                    SysUser sysUser = remoteUserService.findByUserId(userId).getData();
                    if (sysUser != null) {
                        loginUser = remoteUserService.getUserInfo(sysUser.getUserName(), SecurityConstants.INNER).getData();
                        SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的用户ID头部值: {}", userIdHeader);
                }
            }
        } else {
            userId = loginUser.getUserid();
        }
        log.error("用户ID获取: {}", userId);
        // 如果还是没有获取到用户ID，使用默认值
        if (userId == null) {
            userId = 1L;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("recordType", recordType);
        if (StringUtils.isNotBlank(cids)) {
            params.put("ids", List.of(cids.split(",")));
        } else {
            params.put("oprTimes", oprTimes);
            params.put("oprTimee", oprTimee);
            params.put("assetName", assetName);
            params.put("domainIds", domainIds);
        }
        R<List<String>> userTunnelIds = deptAuthService.findUserTunnelIds(userId);
        if (userTunnelIds.getCode() == 200 && !userTunnelIds.getData().isEmpty()) {
            params.put("tunnelIdList", userTunnelIds.getData());
        }
        // 使用PageHelper进行分页，直接设置pageNo和pageSize
        PageHelper.startPage(pageNo.intValue(), pageSize.intValue());
        List list;
        if (null != recordType && recordType == 3) {//故障上报
            list = patrolDeviceCheckService.findFaultListByParam(params);
        } else {
            list = patrolDeviceCheckService.findDetailListByParam(params);
        }
        PageInfo<?> pageInfo = new PageInfo(list);
        // 构建返回结果
        JSONObject object = new JSONObject();
        object.put("data", list);
        object.put("total", pageInfo.getPages());  // 总页数
        object.put("count", list.size());          // 当前页记录数
        object.put("totalCount", pageInfo.getTotal()); // 总记录数
        log.error("结果返回: {}", object);
        return object;
    }


    /**
     * 导出隧道机电检查记录表卡片
     */
    @ApiOperation("导出隧道机电检查记录表卡片")
    @Log(title = "导出隧道机电检查记录表卡片", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAssetReportCard")
    public void exportAssetReportCard(@RequestParam Map<String, Object> params, HttpServletResponse response) {
        try {
            // 设置 HTTP 请求头（Servlet 示例）
            response.setContentType("text/html; charset=UTF-8");
            response.setCharacterEncoding("UTF-8");

            String ids = MapUtil.getStr(params, "ids");
            if (StringUtils.isNotBlank(ids)) {
                params.put("ids", List.of(ids.split(",")));
            } else {
                Long userId = SecurityUtils.getUserId();
                if (!SecurityUtils.isAdmin(userId)) {
                    R<List<String>> userTunnelIds = deptAuthService.findUserTunnelIds(userId);
                    if (userTunnelIds.getCode() == 200 && !userTunnelIds.getData().isEmpty()) {
                        params.put("tunnelIdList", userTunnelIds.getData());
                    } else {
                        throw new ServiceException("用户隧道资产权限不足!");
                    }
                }
            }
            // 记录类型（1经常检查，2日常巡查，3故障记录，4故障月报）
            Integer recordType = MapUtil.getInt(params, "recordType");
            if(recordType == null){
                throw new IllegalArgumentException("type不能为空或无效");
            }
            Map<String, String> signUrlMap = new HashMap<>();
            // 获取桥梁检查记录数据
            List reportData;
            if (recordType == 3) {//故障上报
                reportData = patrolDeviceCheckService.findFaultListByParam(params, signUrlMap);
            } else {
                reportData = patrolDeviceCheckService.findDetailListByParam(params, signUrlMap);
            }

            if (reportData == null || reportData.isEmpty()) {
                throw new RuntimeException("未找到符合条件的检查记录");
            }
            String fileName = MapUtil.getStr(params, "fileName");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            // 使用EasyExcel导出
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                // 从查询结果中获取签名URL映射，已经在PatrolAssetCheckServiceImpl.exportReportCard中处理
                // 创建ExcelWriter
                switch (recordType) {
                    case 1 -> EasyExcel.write(outputStream)
                            .registerWriteHandler(new DeviceOftenReportHandler(reportData, signUrlMap))
                            .sheet("隧道机电设施经常性（定期）检修记录表")
                            .doWrite(new ArrayList<>()); // 空列表，因为BridgeDailyReportHandler会处理数据绘制
                    case 2 -> EasyExcel.write(outputStream)
                            .registerWriteHandler(new DeviceDailyReportHandler(reportData, signUrlMap))
                            .sheet("隧道机电设施日常巡查记录表")
                            .doWrite(new ArrayList<>()); // 空列表，因为BridgeDailyReportHandler会处理数据绘制
                    case 3 -> EasyExcel.write(outputStream)
                            .registerWriteHandler(new DeviceFaultRecordHandler(reportData, signUrlMap))
                            .sheet("隧道机电设施故障记录表")
                            .doWrite(new ArrayList<>()); // 空列表，因为BridgeRegularReportHandler会处理数据绘制
                    default -> throw new IllegalArgumentException("不支持的检查类型: " + recordType);
                }
            } catch (Exception e) {
                log.error("导出巡检查检查记录表失败", e);
                throw new RuntimeException("导出失败: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("导出巡检查检查记录表失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }



}
