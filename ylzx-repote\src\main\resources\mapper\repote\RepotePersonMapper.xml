<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repote.mapper.RepotePersonMapper">

    <resultMap type="com.ruoyi.repote.domain.RepotePerson" id="RepotePersonResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="missionId" column="mission_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">id,
        user_id, user_name, mission_id,create_by,create_time,update_by,update_time     </sql>

    <sql id="where_column">
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="userIdLike != null and userIdLike != ''">
            AND user_id like CONCAT('%', #{userIdLike}, '%')
        </if>
        <if test="userName != null and userName != ''">
            AND user_name = #{userName}
        </if>
        <if test="missionId != null and missionId != ''">
            AND mission_id = #{missionId}
        </if>
    </sql>

    <sql id="set_column">
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="userName != null">
                user_name = #{userName},
            </if>
            <if test="missionId != null">
                mission_id = #{missionId},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="RepotePersonResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_person
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="RepotePersonResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_person
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

</mapper>