package com.ruoyi.repote.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.repote.domain.RepoteAnnex;
import com.ruoyi.repote.service.RepoteAnnexService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 上传附件Controller
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Api(tags = "上传附件" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/annex")
public class RepoteAnnexController extends BaseController {
    final private RepoteAnnexService repoteAnnexService;


    /**
     * 查询上传附件列表(分页)
     */
    @ApiOperation("查询上传附件列表")
    //@RequiresPermissions("repote:annex:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<RepoteAnnex> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<RepoteAnnex> list = repoteAnnexService.list(qw);
        return getDataTable(list);
    }

}
