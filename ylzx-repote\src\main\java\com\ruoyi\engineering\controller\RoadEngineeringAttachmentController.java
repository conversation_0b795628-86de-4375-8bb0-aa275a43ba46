package com.ruoyi.engineering.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.engineering.domain.RoadEngineeringAttachment;
import com.ruoyi.engineering.service.RoadEngineeringAttachmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 涉路工程Controller
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@Api(tags = "涉路工程附件" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/engineeringAttachment")
public class RoadEngineeringAttachmentController extends BaseController {
    final private RoadEngineeringAttachmentService roadEngineeringAttachmentService;


    /**
     * 查询涉路工程列表(分页)
     */
    @ApiOperation("查询涉路工程上传文件")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> params) {
        params.put("order", "create_time desc");
        startPage();
        QueryWrapper<RoadEngineeringAttachment> queryWrapper = new QueryWrapper<>();

        if (params.get("roadEngineeringId") != null) {
            queryWrapper.eq("road_engineering_id", params.get("roadEngineeringId"));
        }
        List<RoadEngineeringAttachment> list = roadEngineeringAttachmentService.findListByParam(params);
        return getDataTable(list);

    }

    /**
     * 查询涉路工程列表(不分页)
     */
    @ApiOperation("查询涉路工程列表(不分页)")
    @RequiresPermissions("repote:engineering:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<RoadEngineeringAttachment> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<RoadEngineeringAttachment> list = roadEngineeringAttachmentService.list(qw);
        return success(list);
    }

    /**
     * 新增涉路工程附件
     */
    @ApiOperation("新增涉路工程附件")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody RoadEngineeringAttachment roadEngineeringAttachment) {
//        otherTransportationAttachment.setStatus(0);
        System.out.println("ListADD: " + roadEngineeringAttachment);
        roadEngineeringAttachmentService.save(roadEngineeringAttachment);
        return toAjax(true);
    }

    /**
     * 修改涉路工程
     */
    @ApiOperation("修改涉路工程附件")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody RoadEngineeringAttachment roadEngineeringAttachment) {
        return toAjax(roadEngineeringAttachmentService.updateById(roadEngineeringAttachment));
    }

    /**
     * 删除大件运输
     */
    @ApiOperation("删除大件运输附件")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        roadEngineeringAttachmentService.removeById(id);
        return toAjax(true);
    }

    /**
     * 根据id查询大件运输附件
     */
    @ApiOperation("根据id查询大件运输附件")
//    //@RequiresPermissions("repote:repoteForm:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        RoadEngineeringAttachment roadEngineeringAttachment = roadEngineeringAttachmentService.getById(id);
        if (roadEngineeringAttachment == null) {
            return error("未查询到【大件运输附件】记录");
        }
        return success(roadEngineeringAttachment);
    }
}
