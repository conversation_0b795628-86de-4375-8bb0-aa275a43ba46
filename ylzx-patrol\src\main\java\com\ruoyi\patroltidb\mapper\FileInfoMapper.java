package com.ruoyi.patroltidb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patroltidb.domain.FileInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件信息表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileInfoMapper extends BaseMapper<FileInfoEntity> {

    int batchUpdateFileInfo(@Param("list") List<FileInfoEntity> fileInfoList);


    /**
     * 批量新增
     */
    Long insertBatch(List<FileInfoEntity> list);


}
