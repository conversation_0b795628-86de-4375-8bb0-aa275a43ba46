package com.ruoyi.patrol.service.handler.impl;

import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * "桥梁经常检查记录表"生成处理器。
 * 继承自抽象处理器以重用通用的Excel生成逻辑。
 */
@Slf4j
public class BridgeRegularReportHandler extends AbstractExcelReportHandler<PatrolAssetCheck> {

    private final List<PatrolAssetCheck> reportDataList;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");

    // --- 此报表的特定样式定义 ---
    // 继承的样式：titleStyle, headerLabelStyle, headerValueStyle, itemHeaderStyle, itemCellStyle, itemFirstColStyle
    private CellStyle itemSuggestionStyle; // 处理建议样式 (左对齐) - 特定
    private CellStyle footerLabelStyle;    // 特定样式
    private CellStyle footerValueStyle;    // 特定样式
    private CellStyle normalBorderStyle;   // 仅用于细边框的特定样式
    private CellStyle noBorderStyle;      // 无边框左对齐自动换行样式

    // 继承的：signUrlMap, signImageMap, failedSignIds, log, SIGNATURE_HEIGHT_POINTS

    public BridgeRegularReportHandler(List<PatrolAssetCheck> reportDataList, Map<String, String> signUrlMap) {
        // super(); // 隐式调用超类构造函数
        this.reportDataList = reportDataList != null ? reportDataList : new ArrayList<>();
        if (signUrlMap != null) {
            this.signUrlMap.putAll(signUrlMap); // 填充继承的映射
        }
        // 预加载现在在beforeSheetCreate中调用
    }

    /**
     * 重写createStyles方法，为此报表类型添加特定样式
     * 并初始化基类中的通用样式。
     */
    @Override
    protected void createStyles(Workbook workbook) {
        super.createStyles(workbook); // 首先初始化通用样式

        // --- 此报表的特定样式 ---

        // --- 通用边框样式 ---
        normalBorderStyle = workbook.createCellStyle();
        setThinBorders(normalBorderStyle); // 使用继承的辅助方法

        // 无边框左对齐自动换行样式
        noBorderStyle = workbook.createCellStyle();
        noBorderStyle.setBorderTop(BorderStyle.NONE);
        noBorderStyle.setBorderBottom(BorderStyle.NONE);
        noBorderStyle.setBorderLeft(BorderStyle.NONE);
        noBorderStyle.setBorderRight(BorderStyle.NONE);
        noBorderStyle.setAlignment(HorizontalAlignment.LEFT);
        noBorderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        noBorderStyle.setWrapText(true);

        // --- 如需要，重新初始化/确保继承样式上的边框 ---
        // 如果基本样式没有边框，但此报表处处需要边框，
        // 克隆基本样式并应用边框。
        // 标题标签样式 (确保它有来自normalBorderStyle的边框)
        CellStyle baseHeaderLabelStyle = super.headerLabelStyle; // 获取基本样式
        headerLabelStyle = workbook.createCellStyle();
        headerLabelStyle.cloneStyleFrom(baseHeaderLabelStyle); // 从基本样式开始
        copyBorders(normalBorderStyle, headerLabelStyle); // 确保边框

        // 标题值样式
        CellStyle baseHeaderValueStyle = super.headerValueStyle;
        headerValueStyle = workbook.createCellStyle();
        headerValueStyle.cloneStyleFrom(baseHeaderValueStyle);
        copyBorders(normalBorderStyle, headerValueStyle);

        // 项目标题样式
        CellStyle baseItemHeaderStyle = super.itemHeaderStyle;
        itemHeaderStyle = workbook.createCellStyle();
        itemHeaderStyle.cloneStyleFrom(baseItemHeaderStyle);
        copyBorders(normalBorderStyle, itemHeaderStyle);

        // 项目单元格样式
        CellStyle baseItemCellStyle = super.itemCellStyle;
        itemCellStyle = workbook.createCellStyle();
        itemCellStyle.cloneStyleFrom(baseItemCellStyle);
        copyBorders(normalBorderStyle, itemCellStyle);
        itemCellStyle.setWrapText(true); // 确保设置了文本换行

        // 项目第一列样式
        CellStyle baseItemFirstColStyle = super.itemFirstColStyle;
        itemFirstColStyle = workbook.createCellStyle();
        itemFirstColStyle.cloneStyleFrom(baseItemFirstColStyle);
        copyBorders(normalBorderStyle, itemFirstColStyle);
        itemFirstColStyle.setWrapText(true); // 确保设置了文本换行

        // --- 新的/特定样式 ---
        // 处理建议列样式 (左对齐，带边框) - 继承自itemCellStyle (现在有边框)
        itemSuggestionStyle = workbook.createCellStyle();
        itemSuggestionStyle.cloneStyleFrom(itemCellStyle); // 继承字体、文本换行、边框
        itemSuggestionStyle.setAlignment(HorizontalAlignment.LEFT); // 覆盖对齐方式
        itemSuggestionStyle.setWrapText(true);

        // 页脚标签样式 (右对齐，带边框) - 基于headerLabelStyle (现在有边框)
        footerLabelStyle = workbook.createCellStyle();
        footerLabelStyle.cloneStyleFrom(headerLabelStyle); // 继承字体、对齐方式、边框
        footerLabelStyle.setWrapText(true);

        // 页脚值样式 (左对齐，带边框) - 基于headerValueStyle (现在有边框)
        footerValueStyle = workbook.createCellStyle();
        footerValueStyle.cloneStyleFrom(headerValueStyle); // 继承字体、对齐方式、边框
        footerValueStyle.setWrapText(true);
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        super.beforeSheetCreate(writeWorkbookHolder, writeSheetHolder); // 通过createStyles重写创建样式
        // 在生成表格内容前预加载签名
        if (!signUrlMap.isEmpty()) {
            preloadSignImages(); // 调用继承的预加载器
        }
    }


    // beforeSheetCreate在上面处理，调用super和预加载

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();

        // 诊断日志记录（使用继承的log）
        if (signUrlMap == null || signUrlMap.isEmpty()) {
            log.warn("签名URL映射为空或null。签名可能不会出现。");
        } else {
            log.info("签名URL映射包含 {} 条记录。", signUrlMap.size());
        }
        log.info("预加载的签名图像缓存包含 {} 条记录。", signImageMap.size()); // 使用继承的映射
        if (!failedSignIds.isEmpty()) {
            log.warn("有 {} 个签名下载失败。ID列表: {}", failedSignIds.size(), failedSignIds);
        }

        int currentRowIndex = 0; // 当前行索引

        // --- 设置列宽 ---
        sheet.setColumnWidth(0, 10 * 256);   // A列 (序号/标签)
        sheet.setColumnWidth(1, 13 * 256);  // B列 (项目名/值)
        sheet.setColumnWidth(2, 14 * 256);  // C列 (类型/标签)
        sheet.setColumnWidth(3, 16 * 256);  // D列 (范围/值)
        sheet.setColumnWidth(4, 11 * 256);  // E列 (建议/标签)
        sheet.setColumnWidth(5, 20 * 256);  // F列 (值)

        // 存储列宽信息用于行高调整
        int[] columnWidths = new int[] {10, 13, 14, 16, 11, 20};

        if (reportDataList == null || reportDataList.isEmpty()) {
             log.warn("报告数据列表为空。生成空表格。");
             // 可选择创建一个表示无数据的标题行
             Row titleRow = sheet.createRow(currentRowIndex);
             titleRow.setHeightInPoints(30);
             createCell(titleRow, 0, "桥梁经常检查记录表 (无数据)", titleStyle);
             sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));
             return; // 如果没有数据则停止处理
        }


        for (int i = 0; i < reportDataList.size(); i++) {
            PatrolAssetCheck reportData = reportDataList.get(i);
            if (reportData == null) {
                log.warn("跳过索引 {} 处的null报告数据对象", i);
                continue; // 跳过null数据条目
            }

            // === 开始为reportData绘制报告部分 ===
            log.debug("正在为资产编码生成报告部分: {}", reportData.getAssetCode());

            // 1. 标题行
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(30);
            // 使用继承的createCell和titleStyle
            createCell(titleRow, 0, "桥梁经常检查记录表", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));
            currentRowIndex++;

            // 2. 机构行
            Row agencyRow = sheet.createRow(currentRowIndex);
            agencyRow.setHeightInPoints(20);
            String agencyName = reportData.getPropertyUnitName() != null ? reportData.getPropertyUnitName() : reportData.getMaintainUnitName();
            // 首先创建A-F单元格，应用边框，然后合并
            for (int col = 0; col <= 5; col++) {
                if (col == 0) {
                    // 使用继承的createCell和headerValueStyle
                    createCell(agencyRow, col, "公路管理机构名称：" + Objects.toString(agencyName, ""), headerValueStyle);
                } else {
                    createCell(agencyRow, col, "", headerValueStyle); // 为边框/合并创建空单元格
                }
            }
            // 使用特定的normalBorderStyle为行应用边框
            applyRowBorder(agencyRow, 0, 5, normalBorderStyle); // 使用继承的帮助方法
            // 在应用样式/边框后合并A-F单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));
            // 确保保留合并后左上角单元格(A)的样式
            // (createCell已应用样式，合并通常保留左上角样式)
            
            // 设置机构行的条件高度
            setConditionalRowHeight(agencyRow, 0, 5, columnWidths, 20);
            
            currentRowIndex++;
            // 3. 路线信息行
            Row routeRow = sheet.createRow(currentRowIndex);
            createCell(routeRow, 0, "1 路线编号", headerLabelStyle);
            createCell(routeRow, 1, Objects.toString(reportData.getRouteCode(), ""), headerValueStyle);
            createCell(routeRow, 2, "2 路线名称", headerLabelStyle);
            createCell(routeRow, 3, Objects.toString(reportData.getMaintenanceSectionName(), ""), headerValueStyle);
            createCell(routeRow, 4, "3 桥位桩号", headerLabelStyle);
            createCell(routeRow, 5, formatStake(reportData.getCenterStake(), reportData.getStakeFormat()), headerValueStyle); // 使用本地辅助方法
            applyRowBorder(routeRow, 0, 5, normalBorderStyle); // 为所有单元格应用边框
            
            // 设置路线信息行的条件高度
            setConditionalRowHeight(routeRow, 0, 5, columnWidths, 20);
            
            currentRowIndex++;
            // 4. 桥梁信息行
            Row bridgeRow = sheet.createRow(currentRowIndex);
            createCell(bridgeRow, 0, "4 桥梁编号", headerLabelStyle);
            createCell(bridgeRow, 1, Objects.toString(reportData.getAssetCode(), ""), headerValueStyle);
            createCell(bridgeRow, 2, "5 桥梁名称", headerLabelStyle);
            createCell(bridgeRow, 3, Objects.toString(reportData.getAssetName(), ""), headerValueStyle);
            createCell(bridgeRow, 4, "6 养护单位", headerLabelStyle);
            createCell(bridgeRow, 5, Objects.toString(reportData.getMaintainUnitName(), ""), headerValueStyle);
            applyRowBorder(bridgeRow, 0, 5, normalBorderStyle);
            
            // 设置桥梁信息行的条件高度
            setConditionalRowHeight(bridgeRow, 0, 5, columnWidths, 20);
            
            currentRowIndex++;
            // 5. 项目标题行
            Row itemHeaderRow = sheet.createRow(currentRowIndex);
            itemHeaderRow.setHeightInPoints(25);
            createCell(itemHeaderRow, 0, "7 检查项目", itemHeaderStyle); // 合并A、B
            createCell(itemHeaderRow, 1, "", itemHeaderStyle); // 用于合并的占位符
            createCell(itemHeaderRow, 2, "缺损类型", itemHeaderStyle); // C
            createCell(itemHeaderRow, 3, "缺损范围", itemHeaderStyle); // D
            createCell(itemHeaderRow, 4, "处治建议", itemHeaderStyle); // 合并E、F
            createCell(itemHeaderRow, 5, "", itemHeaderStyle); // 用于合并的占位符
            // 在合并之前应用边框可能更安全，或者在合并后应用以确保所有部分都有边框
            applyRowBorder(itemHeaderRow, 0, 5, itemHeaderStyle); // 使用itemHeaderStyle的边框应用边框
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 1)); // 合并A、B
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 5)); // 合并E、F
            currentRowIndex++;
            // 6. 项目详情行
            List<PatrolAssetCheckDetail> details = reportData.getPatrolCheckDetailList();
            if (details != null && !details.isEmpty()) {
                for (PatrolAssetCheckDetail item : details) {
                    if (item == null) {
                         log.warn("跳过资产编码 {} 的null详情项", reportData.getAssetCode());
                         continue;
                    }
                    Row itemRow = sheet.createRow(currentRowIndex);
                    
                    // 项目名称 (合并A、B) - 使用itemFirstColStyle (左对齐)
                    String partsTypeName = Objects.toString(item.getPartsTypeName(), "");
                    createCell(itemRow, 0, partsTypeName, itemFirstColStyle);
                    createCell(itemRow, 1, "", itemFirstColStyle); // 占位符
                    sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 1));

                    // 缺陷类型 (C) - 使用itemCellStyle (居中)
                    String defectType = item.getDes() == null || item.getDes().trim().isEmpty() ? "无" : item.getDes();
                    createCell(itemRow, 2, defectType, itemCellStyle);

                    // 缺陷范围 (D) - 使用itemCellStyle (居中)
                    String defectRange = item.getDefect() == null || item.getDefect().trim().isEmpty() ? "未见异常" : item.getDefect();
                    createCell(itemRow, 3, defectRange, itemCellStyle);

                    // 建议 (合并E、F) - 使用itemSuggestionStyle (左对齐)
                    String advice = item.getAdvice() == null || item.getAdvice().trim().isEmpty() ? "正常保养" : item.getAdvice();
                    createCell(itemRow, 4, advice, itemSuggestionStyle);
                    createCell(itemRow, 5, "", itemSuggestionStyle); // 占位符
                    sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 5));

                    // 使用一致的边框样式为整行应用边框 (例如，normalBorderStyle或如果itemCellStyle有边框)
                    applyRowBorder(itemRow, 0, 5, normalBorderStyle); // 确保一致的细边框
                    
                    // 使用继承的方法设置条件高度
                    setConditionalRowHeight(itemRow, 0, 5, columnWidths, 18);

                    currentRowIndex++;
                }
            } else {
                // 无详情行
                log.info("资产编码 {} 未找到检查详情", reportData.getAssetCode());
                Row emptyRow = sheet.createRow(currentRowIndex);
                createCell(emptyRow, 0, "无检查明细", itemFirstColStyle); // 使用左对齐样式
                createCell(emptyRow, 1, "", itemFirstColStyle);
                createCell(emptyRow, 2, "", itemCellStyle);
                createCell(emptyRow, 3, "", itemCellStyle);
                createCell(emptyRow, 4, "", itemSuggestionStyle);
                createCell(emptyRow, 5, "", itemSuggestionStyle);
                sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 5));
                applyRowBorder(emptyRow, 0, 5, normalBorderStyle); // 应用边框
                // 使用条件高度而不是固定高度
                setConditionalRowHeight(emptyRow, 0, 5, columnWidths);
                currentRowIndex++;
            }
            // 2. 添加空白行防止签名越界
            Row blankRow1 = sheet.createRow(currentRowIndex);
            createCell(blankRow1, 1, "", noBorderStyle); // 合并创建空单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            blankRow1.setHeightInPoints(5);
            currentRowIndex++;

            // 7. 页脚行 (标签和日期)
            // 此行将包含标签和日期。签名/姓名在下面。
            int footerLabelRowIndex = currentRowIndex;
            Row footerLabelRow = sheet.createRow(footerLabelRowIndex);
            footerLabelRow.setHeightInPoints(25); // 标签的高度

            createCell(footerLabelRow, 0, "34 负责人", footerLabelStyle); // 标签A
            createCell(footerLabelRow, 1, "", footerValueStyle);       // 占位符B (用于下面的姓名/签名)
            createCell(footerLabelRow, 2, "35 记录人", footerLabelStyle); // 标签C
            createCell(footerLabelRow, 3, "", footerValueStyle);       // 占位符D (用于下面的姓名/签名)
            createCell(footerLabelRow, 4, "36 检查日期", footerLabelStyle); // 标签E
            Date checkDate = reportData.getCheckTime();
            createCell(footerLabelRow, 5, checkDate != null ? dateFormat.format(checkDate) : "", footerValueStyle); // 值F (日期)


            // 8. 页脚签名/姓名区域
            int signatureStartRowIndex = currentRowIndex; // 第一个姓名/签名出现的行

            List<String> kahunaSignList = reportData.getKahunaSignList();
            List<String> oprUserSignList = reportData.getOprUserSignList();

            // 记录签名可用性
            log.debug("负责人 ({}) 签名: {}", reportData.getKahunaName(), kahunaSignList != null ? kahunaSignList.size() : 0);
            log.debug("操作员 ({}) 签名: {}", reportData.getOprUserName(), oprUserSignList != null ? oprUserSignList.size() : 0);

            // 将负责人姓名/签名添加到B列 (索引1)
            int lastRowForKahuna = signatureStartRowIndex;
            // 从姓名行*之后*的行开始添加图像
            if (kahunaSignList != null && !kahunaSignList.isEmpty()) {
                lastRowForKahuna = addSignatureImages(sheet, signatureStartRowIndex, 1,1,
                        kahunaSignList, Objects.toString(reportData.getKahunaName(), ""), footerValueStyle, true);
            } else {
                createCell(footerLabelRow, 1, Objects.toString(reportData.getKahunaName(), ""), footerValueStyle);
            }


            // 将操作员（记录人）姓名/签名添加到D列（索引3）
            int lastRowForOpr = signatureStartRowIndex;
            // 从姓名行*之后*的行开始添加图像
             if (oprUserSignList != null && !oprUserSignList.isEmpty()) {
                 lastRowForOpr = addSignatureImages(sheet, signatureStartRowIndex, 3,3,
                         oprUserSignList, Objects.toString(reportData.getOprUserName(), ""), footerValueStyle, true);
             } else {
                createCell(footerLabelRow, 3, Objects.toString(reportData.getOprUserName(), ""), footerValueStyle);
             }

             
            // 为标签行应用边框
            applyRowBorder(footerLabelRow, 0, 5, normalBorderStyle);

            // 确定页脚部分使用的最终行索引
            // 它是姓名行之后的行和签名使用的行的最大值
            int footerEndRowIndex = Math.max(lastRowForKahuna, lastRowForOpr);
            currentRowIndex = footerEndRowIndex;

            // === 当前报告部分完成 ===

            // 在下一个报告部分之前插入分页符（如果不是最后一个）
            if (i < reportDataList.size() - 1) {
                sheet.setRowBreak(footerEndRowIndex - 1); // 在当前部分的最后一行之后设置分页符
                log.debug("已在行 {} 之后插入分页符", footerEndRowIndex - 1);
            }
        }
        log.info("已完成生成所有报告部分。");
    }
}