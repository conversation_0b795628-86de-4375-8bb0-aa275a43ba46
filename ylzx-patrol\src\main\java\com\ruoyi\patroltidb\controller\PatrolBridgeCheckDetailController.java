package com.ruoyi.patroltidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheck;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheckDetail;
import com.ruoyi.patroltidb.service.PatrolBridgeCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolBridgeCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 桥梁巡检查子表Controller
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Api(tags = "桥梁巡检查子表" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/bridgeCheckDetail")
public class PatrolBridgeCheckDetailController extends BaseController {


    final private PatrolBridgeCheckService patrolBridgeCheckService;
    final private PatrolBridgeCheckDetailService patrolBridgeCheckDetailService;


    /**
     * 迁移巡检查图片
     */
    @PostMapping("/testSaveSignId")
    public AjaxResult testSaveSignId() {
        patrolBridgeCheckService.extracted();
        return AjaxResult.success();
    }

    /**
     * 查询桥梁巡检查子表列表(分页)
     */
    @ApiOperation("查询桥梁巡检查子表列表")
    //@RequiresPermissions("patrol:bridgeCheckDetail:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<PatrolBridgeCheckDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<PatrolBridgeCheckDetail> list = patrolBridgeCheckDetailService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询桥梁巡检查子表列表(不分页)
     */
    @ApiOperation("查询桥梁巡检查子表列表(不分页)")
    //@RequiresPermissions("patrol:bridgeCheckDetail:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolBridgeCheckDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolBridgeCheckDetail> list = patrolBridgeCheckDetailService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询桥梁巡检查子表数据
     */
    @ApiOperation("根据id查询桥梁巡检查子表数据")
    //@RequiresPermissions("patrol:bridgeCheckDetail:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            PatrolBridgeCheckDetail patrolBridgeCheckDetail = patrolBridgeCheckDetailService.getById(id);
        if (patrolBridgeCheckDetail == null) {
            return error("未查询到【桥梁巡检查子表】记录");
        }
        return success(patrolBridgeCheckDetail);
    }

    /**
     * assertCheckId查询桥梁巡检查子表数据
     */
    @ApiOperation("assertCheckId查询桥梁巡检查子表数据")
    //@RequiresPermissions("patrol:bridgeCheckDetail:query")
    @GetMapping(value = "/getByCheckId")
    public AjaxResult getByCheckId(@RequestParam String assertCheckId) {
        QueryWrapper<PatrolBridgeCheckDetail> qw = new QueryWrapper<>();
        qw.eq("check_id", assertCheckId);
        List<PatrolBridgeCheckDetail> list = patrolBridgeCheckDetailService.list(qw);
        return success(list);
    }


    /**
     * assertCheckId查询桥梁巡检查子表数据
     */
    @ApiOperation("查询桥梁巡检查子表数据")
    @ApiResponse(code = 200, message = "成功", response = PatrolBridgeCheckDetail.class)
    @GetMapping(value = "/getByCondition")
    public AjaxResult getByCondition(@ApiParam("桥梁id") @RequestParam String bridgeId, @ApiParam("检查类型,'1': 桥梁日常巡查，'2': 桥梁经常检查") @RequestParam String type,@ApiParam("检查年份") @RequestParam() String year) {
        List<PatrolBridgeCheckDetail> list = new ArrayList<>();
        List<PatrolBridgeCheck> bridgeCheckList = patrolBridgeCheckService.list(new QueryWrapper<>() {{
            this.eq(bridgeId != null, "asset_id", bridgeId).
                    eq(type != null, "type", type).
                    le(year != null, "DATE_FORMAT(check_time,'%Y')", year).orderByDesc("check_time");
        }});

        if (bridgeCheckList.size() >= 1) {
            for (PatrolBridgeCheck patrolBridgeCheck : bridgeCheckList) {
                QueryWrapper<PatrolBridgeCheckDetail> qw = new QueryWrapper<>();
                qw.eq("check_id", patrolBridgeCheck.getId());
                list = patrolBridgeCheckDetailService.list(qw);
                if (list != null && !list.isEmpty()) {
                    break;  // 找到数据就退出循环
                }
            }
        }

        return success(list);
    }

    /**
     * 新增桥梁巡检查子表
     */
    @ApiOperation("新增桥梁巡检查子表")
    //@RequiresPermissions("patrol:bridgeCheckDetail:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolBridgeCheckDetail patrolBridgeCheckDetail) {
        return toAjax(patrolBridgeCheckDetailService.save(patrolBridgeCheckDetail));
    }

    /**
     * 修改桥梁巡检查子表
     */
    @ApiOperation("修改桥梁巡检查子表")
    //@RequiresPermissions("patrol:bridgeCheckDetail:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolBridgeCheckDetail patrolBridgeCheckDetail) {
        return toAjax(patrolBridgeCheckDetailService.updateById(patrolBridgeCheckDetail));
    }

    /**
     * 删除桥梁巡检查子表
     */
    @ApiOperation("删除桥梁巡检查子表")
    //@RequiresPermissions("patrol:bridgeCheckDetail:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolBridgeCheckDetailService.removeById(id));
    }

    /**
     * 导出桥梁巡检查子表列表
     */
    @ApiOperation("导出桥梁巡检查子表列表")
    //@RequiresPermissions("patrol:bridgeCheckDetail:export")
    @Log(title = "桥梁巡检查子表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolBridgeCheckDetail> list = patrolBridgeCheckDetailService.list();
        ExcelUtil<PatrolBridgeCheckDetail> util = new ExcelUtil<PatrolBridgeCheckDetail>(PatrolBridgeCheckDetail.class);
        util.exportExcel(response, list, "桥梁巡检查子表数据");
    }


}
