package com.ruoyi.supervise.domain;


import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import com.ruoyi.system.api.domain.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 督查详情对象 supervise_inspection_detail
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "督查详情")
@TableName("supervise_inspection_detail")
@Data
public class SuperviseInspectionDetail extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 督察记录表id
     */
    @ApiModelProperty(value = "督察记录表id", required = true)
    @NotBlank
    private String inspectionRecordId;

    /**
     * 检查项
     */
    @Excel(name = "检查内容")
    @ApiModelProperty(value = "检查内容")
    private String content;

    /**
     * 意见
     */
    @Excel(name = "建议及要求")
    @ApiModelProperty(value = "建议及要求")
    private String suggestion;

    /**
     * 状态（未整改、已整改）
     */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态（未整改、已整改）")
    private String status;


    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 整改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "整改时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "整改时间")
    private Date rectificationTime;

    /**
     * 整改前照片
     */
    @ApiModelProperty(value = "整改前照片")
    private String beforeImage;


    @TableField(exist = false)
    private List<SysFile> beforeImageList;
    /**
     * 整改后照片
     */
    @ApiModelProperty(value = "整改后照片")
    private String afterImage;

    @TableField(exist = false)
    private List<SysFile> afterImageList;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
