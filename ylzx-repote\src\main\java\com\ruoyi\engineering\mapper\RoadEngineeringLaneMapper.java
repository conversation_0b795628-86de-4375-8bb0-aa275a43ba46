package com.ruoyi.engineering.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.ruoyi.engineering.domain.RoadEngineeringLane;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 涉路工程Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@Mapper
public interface RoadEngineeringLaneMapper extends BaseMapper<RoadEngineeringLane> {

    List<RoadEngineeringLane> findListByParam(Map<String, Object> params);

    List<String> getByEngineeringId(String engineeringId);

}
