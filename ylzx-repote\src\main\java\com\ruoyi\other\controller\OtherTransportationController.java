package com.ruoyi.other.controller;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.*;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.other.domain.dto.OtherTransportationDTO;
import com.ruoyi.system.api.RemoteMaintenanceSectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.other.domain.OtherTransportation;
import com.ruoyi.other.service.OtherTransportationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 大件运输Controller
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@Api(tags = "大件运输" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/transportation")
public class OtherTransportationController extends BaseController {
    final private OtherTransportationService otherTransportationService;
    @Resource
    private RemoteMaintenanceSectionService remoteMaintenanceSectionService;

    /**
     * 查询大件运输列表(分页)
     */
    @ApiOperation("查询大件运输列表")
    //@RequiresPermissions("other:transportation:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<OtherTransportation> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<OtherTransportation> list = otherTransportationService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询大件运输列表(不分页)
     */
    @ApiOperation("查询大件运输列表(不分页)")
    //@RequiresPermissions("other:transportation:listAll")
    @GetMapping("/listAll")
    public TableDataInfo listAll(@RequestParam Map<String, Object> params) throws ExecutionException, InterruptedException {
        QueryWrapper<OtherTransportation> qw = new QueryWrapper<>();
        R<List<String>> r = remoteMaintenanceSectionService.findUserMaintenanceIds(null);
        if (r.getCode() != 200) {
            return getDataTable(new ArrayList<>());
        }
        params.put("sectionIdList", r.getData());
        System.out.printf("params:" + params.toString());
        qw.orderByDesc("create_time");
        startPage();
        List<OtherTransportation> list = otherTransportationService.findListByParam(params);
        return getDataTable(list);
    }

    /**
     * 根据id查询大件运输数据
     */
    @ApiOperation("根据id查询大件运输数据")
    //@RequiresPermissions("other:transportation:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            OtherTransportation otherTransportation = otherTransportationService.getById(id);
        if (otherTransportation == null) {
            return error("未查询到【大件运输】记录");
        }
        return success(otherTransportation);
    }

    /**
     * 新增大件运输
     */
    @ApiOperation("新增大件运输")
    @RequiresPermissions("other:transportation:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody OtherTransportation otherTransportation) {
        otherTransportationService.save(otherTransportation);
        String id = otherTransportation.getId();
        return AjaxResult.success("操作成功", id);
    }

    /**
     * 修改大件运输
     */
    @ApiOperation("修改大件运输")
    @RequiresPermissions("other:transportation:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody OtherTransportation otherTransportation) {
        return toAjax(otherTransportationService.updateById(otherTransportation));
    }

    /**
     * 删除大件运输
     */
    @ApiOperation("删除大件运输")
    @RequiresPermissions("other:transportation:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(otherTransportationService.removeById(id));
    }

    /**
     * 导出大件运输列表
     */
    @ApiOperation("导出大件运输列表")
    //@RequiresPermissions("other:transportation:export")
    @Log(title = "大件运输", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<OtherTransportation> list = otherTransportationService.list();
        ExcelUtil<OtherTransportation> util = new ExcelUtil<OtherTransportation>(OtherTransportation.class);
        util.exportExcel(response, list, "大件运输数据");
    }

    /**
     * 查询大件运输列表连着管理处和路段
     */
    @ApiOperation("查询大件运输列表")
    //@RequiresPermissions("other:transportation:list")
    @PostMapping("/AllList")
    public TableDataInfo AllList(@RequestParam Map<String, Object> params) {
        // 如果 deptIds 是单一值，则将其转换为列表
        if (params.containsKey("deptIds") && params.get("deptIds") instanceof String) {
            String deptIdsStr = (String) params.get("deptIds");
            List<String> deptIdsList = Arrays.asList(deptIdsStr.split(","));
            params.put("deptIds", deptIdsList);
        } else if (params.containsKey("deptIds") && !(params.get("deptIds") instanceof List)) {
            // 如果 deptIds 不是列表也不是字符串，则尝试直接转换（假设它是一个单一整数）
            Object singleDeptId = params.get("deptIds");
            params.put("deptIds", Arrays.asList(singleDeptId));
        }

        startPage();
        List<OtherTransportationDTO> list = otherTransportationService.findAll(params);

        return getDataTable(list);
    }

//    /**
//     * 查询权限能看到的大件运输列表
//     */
//    @ApiOperation("查询权限能看到的大件运输列表")
//    @PostMapping("/AllUserList")
//    public TableDataInfo AllUserList(@RequestBody List<String> transportationIds) {
//        System.out.printf("transportationIds" + transportationIds);
//        // 判断传入的 transportationIds 是否为空
//        if (transportationIds == null || transportationIds.isEmpty()) {
//            return getDataTable(Collections.emptyList());  // 如果为空，则返回空数据
//        }
//        startPage();
//
//        // 调用服务层方法根据 transportationIds 查找相应的记录
//        List<OtherTransportationDTO> list = otherTransportationService.findByTransportationIds(transportationIds);
//        return getDataTable(list);
//    }
    /**
     * 查询权限能看到的大件运输列表
     */

//    @ApiOperation("查询权限能看到的大件运输列表")
//    @PostMapping("/AllUserList")
//    public TableDataInfo AllUserList1(@RequestBody Map<String, Object> params) {
//
//        // 调用服务层方法根据 transportationIds 查找相应的记录
//        List<OtherTransportationDTO> list = otherTransportationService.findByTransportationIds(params);
//
//        startPage();
//        return getDataTable(list);
//    }

    @ApiOperation("查询权限能看到的大件运输列表")
    @PostMapping("/AllUserList")
    public TableDataInfo AllUserList1(@RequestBody Map<String, Object> params) {


        params.put("order", "create_time desc");
        // 获取分页参数
        int pageNum = (int) params.getOrDefault("pageNum", 1); // 默认第一页
        int pageSize = (int) params.getOrDefault("pageSize", 20); // 默认每页20条

        // 设置分页
        PageHelper.startPage(pageNum, pageSize);// 如果你使用的是 PageHelper

        // 调用服务层方法根据 transportationIds 查找相应的记录
        List<OtherTransportationDTO> list = otherTransportationService.findByTransportationIds(params);

        return getDataTable(list);

    }
}
