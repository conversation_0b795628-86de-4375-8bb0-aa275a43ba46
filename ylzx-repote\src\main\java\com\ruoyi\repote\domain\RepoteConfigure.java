package com.ruoyi.repote.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.entity.BaseModelEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;

/**
 * 填报配置对象 repote_configure
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@ApiModel(value="填报配置")
@TableName("repote_configure")
@Data
public class RepoteConfigure extends BaseModelEntity<RepoteConfigure> {
    private static final long serialVersionUID = 1L;

    /** 填报表格ID */
    @Excel(name = "填报表格ID")
    @ApiModelProperty(value = "填报表格ID")
    private String formId;

    /** 配置字段名称 */
    @Excel(name = "配置字段名称")
    @ApiModelProperty(value = "配置字段名称")
    private String configureName;

    /** 填报类型 */
    @Excel(name = "填报类型")
    @ApiModelProperty(value = "填报类型")
    private String configureType;

}
