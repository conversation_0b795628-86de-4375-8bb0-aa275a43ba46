<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.PatrolInspectionUserMapper">
    <!-- 批量新增巡查人员关联数据 -->
<insert id="batchInsert" parameterType="java.util.List">
    INSERT IGNORE INTO patrol_inspection_user (
        patrol_id,
        user_id,
        nick_name,
        sign_id
    ) VALUES 
    <foreach collection="list" item="item" separator=",">
        (
            #{item.patrolId},
            #{item.userId},
            #{item.nickName},
            #{item.signId}
        )
    </foreach>
</insert>

<!-- 批量删除巡查人员关联数据 -->
<delete id="batchDeleteByPatrolIds" parameterType="java.util.List">
    DELETE FROM patrol_inspection_user
    WHERE patrol_id IN
    <foreach collection="list" item="patrolId" open="(" separator="," close=")">
        #{patrolId}
    </foreach>
</delete>
</mapper>