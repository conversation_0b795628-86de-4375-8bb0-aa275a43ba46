package com.ruoyi.other.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.other.domain.OtherTransportation;
import com.ruoyi.other.domain.dto.OtherTransportationDTO;
import io.lettuce.core.dynamic.annotation.Param;


/**
 * 大件运输Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface OtherTransportationMapper extends BaseMapper<OtherTransportation> {

    List<OtherTransportation> findListByParam(Map<String, Object> params);

    List<OtherTransportationDTO> findAll(Map<String, Object> params);


//    List<OtherTransportationDTO> findByTransportationIds(List<String> transportationIds);

    List<OtherTransportationDTO> findByTransportationIds(Map<String, Object> params);
}
