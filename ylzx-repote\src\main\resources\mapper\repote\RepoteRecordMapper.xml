<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repote.mapper.RepoteRecordMapper">

    <resultMap type="com.ruoyi.repote.domain.RepoteRecord" id="RepoteRecordResult">
        <result property="id" column="id"/>
        <result property="formId" column="form_id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="url" column="url"/>
        <result property="annexUrls" column="annex_urls"/>
        <result property="remake" column="remake"/>
        <result property="status" column="status"/>
        <result property="formName" column="form_name"/>
        <result property="missionId" column="mission_id"/>
        <result property="missionName" column="mission_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">
         id, form_id, user_id, user_name, url, annex_urls, remake, status
        ,form_name
        ,mission_id
        ,mission_name,create_by,create_time,update_by,update_time
    </sql>

    <sql id="where_column">
        <if test="formId != null and formId != ''">
            AND form_id = #{formId}
        </if>
        <if test="formIdLike != null and formIdLike != ''">
            AND form_id like CONCAT('%', #{formIdLike}, '%')
        </if>
        <if test="formName != null and formName != ''">
            AND form_name = #{formName}
        </if>
        <if test="formNameLike != null and formNameLike != ''">
            AND form_name like CONCAT('%', #{formNameLike}, '%')
        </if>
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND user_name = #{userName}
        </if>
        <if test="url != null and url != ''">
            AND url = #{url}
        </if>
        <if test="annexUrls != null and annexUrls != ''">
            AND annex_urls = #{annexUrls}
        </if>
        <if test="remake != null and remake != ''">
            AND remake = #{remake}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="missionId != null and missionId != ''">
            AND mission_Id = #{missionId}
        </if>
        <if test="missionName != null and missionName != ''">
            AND mission_name = #{missionName}
        </if>
    </sql>

    <sql id="set_column">
        <if test="formId != null">
            form_id = #{formId},
        </if>
        <if test="userId != null">
            user_id = #{userId},
        </if>
        <if test="userName != null">
            user_name = #{userName},
        </if>
        <if test="url != null">
            url = #{url},
        </if>
        <if test="remark != null">
            remark = #{remark},
        </if>
        <if test="remake != null">
            remake = #{remake},
        </if>
        <if test="status != null">
            status = #{status},
        </if>
        <if test="formName != null">
            form_name = #{formName},
        </if>
        <if test="missionId != null">
            mission_Id = #{missionId},
        </if>
        <if test="missionName != null">
            mission_name = #{missionName},
        </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="RepoteRecordResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_record
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultType="com.ruoyi.repote.domain.RepoteRecord">
        SELECT
            r.id AS id,
            r.form_id AS formId,
            r.user_id AS userId,
            r.user_name AS userName,
            r.url AS url,
            r.annex_urls AS annexUrls,
            r.remake AS remake,
            r.`status` AS `status`,
            r.mission_id AS missionId,
            m.`name` AS missionName,
            r.create_by AS createBy,
            r.create_time AS createTime,
            r.update_by AS updateBy,
            r.update_time AS updateTime,
            f.`name` AS formName,
            f.url AS formUrl,
            f.request AS request,
            f.expiry AS expiry,
            m.url AS missionUrl,
            m.remark AS missionRemark
        FROM
        repote_record AS r
        LEFT JOIN repote_form AS f ON r.form_id = f.id
        LEFT JOIN repote_mission AS m ON r.mission_id = m.id
        <where> 1=1
            <include refid="where_column_r"/>
        </where>
        GROUP BY
        r.id
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <sql id="where_column_r">
        <if test="formId != null and formId != ''">
            AND r.form_id = #{formId}
        </if>
        <if test="formIdLike != null and formIdLike != ''">
            AND r.form_id like CONCAT('%', #{formIdLike}, '%')
        </if>
        <if test="formName != null and formName != ''">
            AND r.form_name = #{formName}
        </if>
        <if test="formNameLike != null and formNameLike != ''">
            AND r.form_name like CONCAT('%', #{formNameLike}, '%')
        </if>
        <if test="userId != null and userId != ''">
            AND r.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND r.user_name = #{userName}
        </if>
        <if test="userNameLike != null and userNameLike != ''">
            AND r.user_name like CONCAT('%', #{userNameLike}, '%')
        </if>
        <if test="url != null and url != ''">
            AND r.url = #{url}
        </if>
        <if test="annexUrls != null and annexUrls != ''">
            AND r.annex_urls = #{annexUrls}
        </if>
        <if test="remake != null and remake != ''">
            AND r.remake = #{remake}
        </if>
        <if test="status != null and status != ''">
            AND r.status = #{status}
        </if>
        <if test="missionId != null and missionId != ''">
            AND r.mission_Id = #{missionId}
        </if>
        <if test="missionName != null and missionName != ''">
            AND r.mission_name = #{missionName}
        </if>
        <if test="missionNameLike != null and missionNameLike != ''">
            AND r.mission_name like CONCAT('%', #{missionNameLike}, '%')
        </if>
    </sql>


</mapper>