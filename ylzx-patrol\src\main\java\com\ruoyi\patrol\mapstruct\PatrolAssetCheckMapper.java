package com.ruoyi.patrol.mapstruct;

import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.dto.AssetTimesDTO;
import com.ruoyi.patrol.domain.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 *
 */
@Mapper
public interface PatrolAssetCheckMapper {
    PatrolAssetCheckMapper INSTANCE = Mappers.getMapper(PatrolAssetCheckMapper.class);

    /**
     * 转换桥梁巡查统计VO
     * @param patrolAssetCheck 巡查资产
     * @return BridgeDetailsVO
     */
    BridgeDetailsVO toBridgeDetailsVO(PatrolAssetCheck patrolAssetCheck);

    /**
     * 转换涵洞巡查统计VO
     * @param patrolAssetCheck 巡查资产
     * @return CulvertDetailsVO
     */
    CulvertDetailsVO toCulvertDetailsVO(PatrolAssetCheck patrolAssetCheck);

    /**
     * 转换隧道巡查统计VO
     * @param patrolAssetCheck 巡查资产
     * @return TunnelDetailsVO
     */
    TunnelDetailsVO toTunnelDetailsVO(PatrolAssetCheck patrolAssetCheck);

    /**
     * 转换桥梁巡查统计VO
     * @param patrolAssetCheck 巡查资产
     * @return BridgeTimesVO
     */
    @Mapping(target = "id", source = "assetId")
    BridgeTimesVO toBridgeTimesVO(PatrolAssetCheck patrolAssetCheck);

    /**
     *  转换涵洞巡查统计VO
     * @param patrolAssetCheck 巡查资产
     *  @return CulvertTimesVO
     */
    @Mapping(target = "id", source = "assetId")
    CulvertTimesVO toCulvertTimesVO(PatrolAssetCheck patrolAssetCheck);

    /**
     *  转换隧道巡查统计VO
     *  @param patrolAssetCheck 巡查资产
     *  @return TunnelTimesVO
     */
    @Mapping(target = "id", source = "assetId")
    TunnelTimesVO toTunnelTimesVO(PatrolAssetCheck patrolAssetCheck);

    /**
     * 转换桥梁巡查统计VO
     * @param patrolAssetCheck 巡查资产
     * @param assetTimesDTO 巡查次数
     * @return BridgeTimesVO
     */
    @Mapping(target = "id", source = "patrolAssetCheck.assetId")
    BridgeTimesVO toBridgeTimesVO(PatrolAssetCheck patrolAssetCheck, AssetTimesDTO assetTimesDTO);

    /**
     *  转换涵洞巡查统计VO
     * @param patrolAssetCheck 巡查资产
     *  @param assetTimesDTO 巡查次数
     *  @return CulvertTimesVO
     */
    @Mapping(target = "id", source = "patrolAssetCheck.assetId")
    CulvertTimesVO toCulvertTimesVO(PatrolAssetCheck patrolAssetCheck, AssetTimesDTO assetTimesDTO);

    /**
     *  转换隧道巡查统计VO
     *  @param patrolAssetCheck 巡查资产
     *  @param assetTimesDTO 巡查次数
     *  @return TunnelTimesVO
     */
    @Mapping(target = "id", source = "patrolAssetCheck.assetId")
    TunnelTimesVO toTunnelTimesVO(PatrolAssetCheck patrolAssetCheck, AssetTimesDTO assetTimesDTO);

    /**
     * 转换为InspectionInfoVO
     * @param patrolAssetCheck 巡查资产
     * @return InspectionInfoVO
     */
    @Mappings({
            @Mapping(target = "expireTime", source = "expiry"),
            @Mapping(target = "assetId", source = "assetId"),
            @Mapping(target = "assetName", source = "assetName"),
            @Mapping(target = "inspectionType", source = "type"),
            @Mapping(target = "inspectionTypeName", ignore = true),
            @Mapping(target = "inspectionDesc", ignore = true)
    })
    InspectionInfoVO toInspectionInfoVO(PatrolAssetCheck patrolAssetCheck);



}
