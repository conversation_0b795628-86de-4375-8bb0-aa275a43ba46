<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.PatrolAssetDiseasesMapper">

    <resultMap type="com.ruoyi.patrol.domain.PatrolAssetDiseases" id="PatrolAssetDiseasesResult">
        <result property="assetType" column="asset_type"/>
        <result property="assetName" column="asset_name"/>
        <result property="diseaseCode" column="disease_code"/>
        <result property="diseaseName" column="disease_name"/>
        <result property="exampleImage" column="example_image"/>
        <result property="pileMode" column="pile_mode"/>
        <result property="sort" column="sort"/>
        <result property="color" column="color"/>
    </resultMap>

    <sql id="base_column"> id, asset_type, asset_name, disease_code, disease_name, example_image, pile_mode, sort, color </sql>

    <!-- 联合查询 -->
    <select id="selectPatrolPartsInfoByPartsType" parameterType="String" resultMap="PatrolAssetDiseasesResult">
        SELECT pad.*
        FROM patrol_parts_info ppi
                 JOIN patrol_parts_diseases ppd ON ppi.id = ppd.parts_id
                 JOIN patrol_asset_diseases pad ON ppd.diseases_id = pad.id
        WHERE ppi.parts_type = #{partsType}
        ORDER BY pad.sort ASC
    </select>
</mapper>