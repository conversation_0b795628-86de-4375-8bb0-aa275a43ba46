package com.ruoyi.other.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class OtherTransportationDTO {
//    private static final long serialVersionUID = 1L;
    /** 项目名称 */
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private String id;

    /** 项目名称 */
    @Excel(name = "项目名称")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /** 车辆货物信息 */
    @Excel(name = "车辆货物信息")
    @ApiModelProperty(value = "车辆货物信息")
    private String vehicleCargoInfo;

    /** 来文时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "来文时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "来文时间")
    private Date submissionTime;

    /** 运输起点 */
    @Excel(name = "运输起点")
    @ApiModelProperty(value = "运输起点")
    private String startingPoint;

    /** 运输终点 */
    @Excel(name = "运输终点")
    @ApiModelProperty(value = "运输终点")
    private String endingPoint;

    /** 总重 */
    @Excel(name = "总重")
    @ApiModelProperty(value = "总重")
    private BigDecimal totalWeight;

    /** 轴重 */
    @Excel(name = "轴重")
    @ApiModelProperty(value = "轴重")
    private BigDecimal axleLoad;

    /** 轴距 */
    @Excel(name = "轴距")
    @ApiModelProperty(value = "轴距")
    private BigDecimal wheelBase;

    /** 是否同意通行 */
    @Excel(name = "是否同意通行")
    @ApiModelProperty(value = "是否同意通行")
    private String isPass;

    /** 回函文号 */
    @Excel(name = "回函文号")
    @ApiModelProperty(value = "回函文号")
    private String replyNumber;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remarks;


    /** 删除标志 */
    @ApiModelProperty(value = "删除标志")
    private Long delFlag;

    /** 部门ID */
    @Excel(name = "部门ID")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /** 路段Id */
    @Excel(name = "路段Id")
    @ApiModelProperty(value = "路段Id")
    private String roadId;

    /** 路段名称 */
    @Excel(name = "路段名称")
    @ApiModelProperty(value = "路段名称")
    private String roadName;
}
