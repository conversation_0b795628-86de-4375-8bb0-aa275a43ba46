package com.ruoyi.patroltidb.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.patrol.enums.PatrolCategory;
import com.ruoyi.patrol.enums.StageType;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheck;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheck;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheckDetail;
import com.ruoyi.patrol.enums.AuditStatusType;
import com.ruoyi.patroltidb.mapper.PatrolTunnelCheckMapper;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.patroltidb.service.PatrolTunnelCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolTunnelCheckService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.EnumSet;
import java.util.List;

/**
 * (PatrolTunnelCheck)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-25 09:48:22
 */
@Service("patrolTunnelCheckService")
@Slave
public class PatrolTunnelCheckServiceImpl extends ServiceImpl<PatrolTunnelCheckMapper, PatrolTunnelCheck>
        implements PatrolTunnelCheckService {

    @Resource
    private PatrolTunnelCheckDetailService patrolTunnelCheckDetailService;

    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;

    /**
     * 通过id获取实体（带子表）
     * @param id
     * @return
     */
    @Override
    public PatrolTunnelCheck getById(Serializable id) {
        PatrolTunnelCheck patrolTunnelCheck = super.getById(id);
        List<PatrolTunnelCheckDetail> patrolTunnelCheckDetailList = patrolTunnelCheckDetailService.list(new QueryWrapper<>(PatrolTunnelCheckDetail.class) {{
            this.orderByDesc("create_time");
            this.eq("check_id", patrolTunnelCheck.getId());
        }});
        patrolTunnelCheck.setPatrolTunnelCheckDetailList(patrolTunnelCheckDetailList);
        return patrolTunnelCheck;
    }

    /**
     * 通过id获取实体（不带子表）
     */
    @Override
    public PatrolTunnelCheck getPatrolTunnelCheckById(Serializable id) {
        return super.getById(id);
    }

    /**
     * 保存（带子表）
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(PatrolTunnelCheck entity) {
        patrolAssetCheckService.setExpiryAndFrequency(entity);
        patrolAssetCheckService.setSign(entity);
        //        if(!entity.getType().getFlag()){
//            entity.setStage(StageType.COMPLETED);
//        }
        String id = IdWorker.getIdStr();
        entity.setId(id);
        entity.setStage(StageType.COMPLETED);
        if (entity.getCategory() == null) {
            entity.setCategory(PatrolCategory.REGULAR_PATROL);
        }
        List<PatrolTunnelCheckDetail> patrolTunnelCheckDetailList = entity.getPatrolTunnelCheckDetailList();
        if (!CollectionUtil.isEmpty(patrolTunnelCheckDetailList)) {
            patrolTunnelCheckDetailList.forEach(item -> item.setCheckId(entity.getId()));
            patrolTunnelCheckDetailService.saveOrUpdateBatch(patrolTunnelCheckDetailList);
        }
        boolean save = super.save(entity);
        return save;
    }

    /**
     * 更新（带子表）
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(PatrolTunnelCheck entity) {
        // 获取当前数据库中的实体，用于比较状态变化
        PatrolTunnelCheck originalEntity = super.getById(entity.getId());
        
        // 只有当状态从非审核状态变为审核状态时，才更新审核人信息
        if (originalEntity != null && 
            !EnumSet.of(AuditStatusType.APPROVED, AuditStatusType.REJECTED).contains(originalEntity.getStatus()) &&
            EnumSet.of(AuditStatusType.APPROVED, AuditStatusType.REJECTED).contains(entity.getStatus())) {
            
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if(entity.getAuditTime() == null){
                entity.setAuditTime(new Date());
            }
            entity.setKahunaId(loginUser.getUserid() + " ");
            entity.setKahunaName(loginUser.getSysUser().getNickName());
            entity.setKahunaSign(loginUser.getSysUser().getSignId());
        }
        
        // 如果检查时间修改了
        if(originalEntity != null && entity.getCheckTime() != null && !entity.getCheckTime().equals(originalEntity.getCheckTime())){
            // 如果检查时间修改了，则需要更新养护路段的检查时间
            patrolAssetCheckService.setExpiryAndFrequency(entity);
        }

        patrolAssetCheckService.setSign(entity);
        entity.setStage(StageType.COMPLETED);
        List<PatrolTunnelCheckDetail> patrolTunnelCheckDetailList = entity.getPatrolTunnelCheckDetailList();
        if (!CollectionUtil.isEmpty(patrolTunnelCheckDetailList)) {
            patrolTunnelCheckDetailList.forEach(item -> item.setCheckId(entity.getId()));
            patrolTunnelCheckDetailService.updateBatchById(patrolTunnelCheckDetailList);
        }
        patrolAssetCheckService.setDiseaseNum(entity);
        boolean update = super.updateById(entity);
        return update;
    }

    /**
     * 删除（带子表）
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<?> list) {
        boolean a = super.removeByIds(list);
        boolean b = patrolTunnelCheckDetailService.remove(new QueryWrapper<>(PatrolTunnelCheckDetail.class) {{
            this.in("check_id", list);
        }});

        return a && b;
    }
}

