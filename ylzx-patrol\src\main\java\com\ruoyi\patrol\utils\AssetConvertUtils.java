package com.ruoyi.patrol.utils;

import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.dto.AssetTimesDTO;
import com.ruoyi.patrol.domain.vo.*;
import com.ruoyi.patrol.mapstruct.PatrolAssetCheckMapper;
import org.apache.poi.ss.formula.functions.T;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.List;
import java.util.Collections;
import java.util.ArrayList;
import java.util.concurrent.ForkJoinPool;

/**
 * 资产转换工具类
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public class AssetConvertUtils {
    private static final PatrolAssetCheckMapper MAPPER = PatrolAssetCheckMapper.INSTANCE;

    private static final Map<Class<?>, Function<PatrolAssetCheck, Object>> CONVERTERS = new ConcurrentHashMap<>();

    private static final int PARALLEL_THRESHOLD = 1000;
    private static final int BATCH_SIZE = 2000;
    private static final ForkJoinPool COMMON_POOL = ForkJoinPool.commonPool();

    static {
        // 初始化转换器映射
        CONVERTERS.put(BridgeDetailsVO.class, MAPPER::toBridgeDetailsVO);
        CONVERTERS.put(CulvertDetailsVO.class, MAPPER::toCulvertDetailsVO);
        CONVERTERS.put(TunnelDetailsVO.class, MAPPER::toTunnelDetailsVO);
        CONVERTERS.put(BridgeTimesVO.class, MAPPER::toBridgeTimesVO);
        CONVERTERS.put(CulvertTimesVO.class, MAPPER::toCulvertTimesVO);
        CONVERTERS.put(TunnelTimesVO.class, MAPPER::toTunnelTimesVO);
        CONVERTERS.put(InspectionInfoVO.class, MAPPER::toInspectionInfoVO);
    }

    /**
     * 根据类型转换为DetailsVO
     */
    @SuppressWarnings("unchecked")
    public static <T> T convertToDetailsVO(PatrolAssetCheck assetCheck) {
        if (assetCheck == null) {
            return null;
        }
        Object result = switch (assetCheck.getType().getAssetType()) {
            case BRIDGE -> toBridgeDetails(assetCheck);
            case CULVERT -> toCulvertDetails(assetCheck);
            case TUNNEL -> toTunnelDetails(assetCheck);
            default -> throw new IllegalArgumentException("不支持的类型: " + assetCheck.getType());
        };
        return (T) result;
    }

    /**
     * 根据类型转换为TimesVO（带巡查次数）
     */
    @SuppressWarnings("unchecked")
    public static <T> T convertToTimesVO(PatrolAssetCheck assetCheck, AssetTimesDTO assetTimesDTO) {
        if (assetCheck == null) {
            return null;
        }
        Object result = switch (assetCheck.getType().getAssetType()) {
            case BRIDGE -> MAPPER.toBridgeTimesVO(assetCheck, assetTimesDTO);
            case CULVERT -> MAPPER.toCulvertTimesVO(assetCheck, assetTimesDTO);
            case TUNNEL -> MAPPER.toTunnelTimesVO(assetCheck, assetTimesDTO);
            default -> throw new IllegalArgumentException("不支持的类型: " + assetCheck.getType());
        };
        return (T) result;
    }

    /**
     * 根据类型转换为TimesVO
     */
    @SuppressWarnings("unchecked")
    public static <T> T convertToTimesVO(PatrolAssetCheck assetCheck) {
        if (assetCheck == null) {
            return null;
        }
        Object result = switch (assetCheck.getType().getAssetType()) {
            case BRIDGE -> toBridgeTimes(assetCheck);
            case CULVERT -> toCulvertTimes(assetCheck);
            case TUNNEL -> toTunnelTimes(assetCheck);
            default -> throw new IllegalArgumentException("不支持的类型: " + assetCheck.getType());
        };
        return (T) result;
    }

    /**
     * 批量转换资产对象列表
     */
    public static <T> List<T> convertList(List<PatrolAssetCheck> list, Class<T> clazz) {
        if (list == null || list.isEmpty() || clazz == null) {
            return Collections.emptyList();
        }

        Function<PatrolAssetCheck, Object> converter = CONVERTERS.get(clazz);
        if (converter == null) {
            throw new IllegalArgumentException("不支持的转换类型: " + clazz.getSimpleName());
        }

        int size = list.size();
        if (size > PARALLEL_THRESHOLD) {
            return processBatchParallel(list, converter);
        } else {
            return processSequential(list, converter);
        }
    }

    @SuppressWarnings("unchecked")
    private static <T> List<T> processBatchParallel(
            List<PatrolAssetCheck> list,
            Function<PatrolAssetCheck, Object> converter) {
        int threads = Math.min(Runtime.getRuntime().availableProcessors(), 
                             (list.size() + BATCH_SIZE - 1) / BATCH_SIZE);
        
        try {
            return COMMON_POOL.submit(() ->
                list.parallelStream()
                    .map(item -> (T) converter.apply(item))
                    .collect(
                        ArrayList<T>::new,
                        ArrayList::add,
                        ArrayList::addAll)
            ).get();
        } catch (Exception e) {
            throw new IllegalStateException("并行处理失败", e);
        }
    }

    @SuppressWarnings("unchecked")
    private static <T> List<T> processSequential(
            List<PatrolAssetCheck> list,
            Function<PatrolAssetCheck, Object> converter) {
        List<T> result = new ArrayList<>(list.size());
        for (PatrolAssetCheck item : list) {
            result.add((T) converter.apply(item));
        }
        return result;
    }

    /**
     * 转换单个资产对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T convert(PatrolAssetCheck assetCheck, Class<T> clazz) {
        if (assetCheck == null || clazz == null) {
            return null;
        }
        Function<PatrolAssetCheck, Object> converter = CONVERTERS.get(clazz);
        if (converter == null) {
            throw new IllegalArgumentException("不支持的转换类型: " + clazz.getSimpleName());
        }
        return (T) converter.apply(assetCheck);
    }

    /**
     * 转换为桥梁详情VO
     */
    public static BridgeDetailsVO toBridgeDetails(PatrolAssetCheck assetCheck) {
        return convert(assetCheck,BridgeDetailsVO.class);
    }

    /**
     * 转换为涵洞详情VO
     */
    public static CulvertDetailsVO toCulvertDetails(PatrolAssetCheck assetCheck) {
        return convert(assetCheck,CulvertDetailsVO.class);
    }

    /**
     * 转换为隧道详情VO
     */
    public static TunnelDetailsVO toTunnelDetails(PatrolAssetCheck assetCheck) {
        return convert(assetCheck,TunnelDetailsVO.class);
    }

    /**
     * 转换为桥梁次数VO
     */
    public static BridgeTimesVO toBridgeTimes(PatrolAssetCheck assetCheck) {
        return convert(assetCheck,BridgeTimesVO.class);
    }

    /**
     * 转换为涵洞次数VO
     */
    public static CulvertTimesVO toCulvertTimes(PatrolAssetCheck assetCheck) {
        return convert(assetCheck,CulvertTimesVO.class);
    }

    /**
     * 转换为隧道次数VO
     */
    public static TunnelTimesVO toTunnelTimes(PatrolAssetCheck assetCheck) {
        return convert(assetCheck,TunnelTimesVO.class);
    }

    /**
     * 转换为InspectionInfoVO
     */
    public static InspectionInfoVO toInspectionInfoVO(PatrolAssetCheck assetCheck) {
        return convert(assetCheck, InspectionInfoVO.class);
    }

}
