package com.ruoyi.patrol.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.patrol.domain.PatrolPartsDiseases;
import com.ruoyi.patrol.service.PatrolPartsDiseasesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部件-病害关系Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Api(tags = "部件-病害关系")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/partsDiseases")
public class PatrolPartsDiseasesController extends BaseController {

    final private PatrolPartsDiseasesService patrolPartsDiseasesService;

    /**
     * 查询部件-病害关系列表(分页)
     */
    @ApiOperation("查询部件-病害关系列表")
    //@RequiresPermissions("patrol:partsDiseases:list")
    @GetMapping("/list")
    public TableDataInfo list(PatrolPartsDiseases patrolPartsDiseases) {
        QueryWrapper<PatrolPartsDiseases> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        qw.setEntity(patrolPartsDiseases);
        startPage();
        List<PatrolPartsDiseases> list = patrolPartsDiseasesService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询部件-病害关系列表(不分页)
     */
    @ApiOperation("查询部件-病害关系列表(不分页)")
    //@RequiresPermissions("patrol:partsDiseases:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll(PatrolPartsDiseases patrolPartsDiseases) {
        QueryWrapper<PatrolPartsDiseases> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        qw.setEntity(patrolPartsDiseases);
        List<PatrolPartsDiseases> list = patrolPartsDiseasesService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询部件-病害关系数据
     */
    @ApiOperation("根据id查询部件-病害关系数据")
    //@RequiresPermissions("patrol:partsDiseases:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        PatrolPartsDiseases patrolPartsDiseases = patrolPartsDiseasesService.getById(id);
        if (patrolPartsDiseases == null) {
            return error("未查询到【部件-病害关系】记录");
        }
        return success(patrolPartsDiseases);
    }

    /**
     * 新增部件-病害关系
     */
    @ApiOperation("新增部件-病害关系")
    //@RequiresPermissions("patrol:partsDiseases:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolPartsDiseases patrolPartsDiseases) {
        return toAjax(patrolPartsDiseasesService.save(patrolPartsDiseases));
    }

    /**
     * 新增部件-病害关系
     *
     * @param listPatrolPartsDiseases
     * @return
     */
    @ApiOperation("新增部件-病害关系")
    //@RequiresPermissions("patrol:partsDiseases:add")
    @PostMapping("/addBatch")
    public AjaxResult addBatch(@RequestBody List<PatrolPartsDiseases> listPatrolPartsDiseases) {
        return toAjax(patrolPartsDiseasesService.saveBatch(listPatrolPartsDiseases));
    }

    /**
     * 修改部件-病害关系
     */
    @ApiOperation("修改部件-病害关系")
    //@RequiresPermissions("patrol:partsDiseases:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolPartsDiseases patrolPartsDiseases) {
        return toAjax(patrolPartsDiseasesService.updateById(patrolPartsDiseases));
    }

    /**
     * 删除部件-病害关系
     */
    @ApiOperation("删除部件-病害关系")
    //@RequiresPermissions("patrol:partsDiseases:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolPartsDiseasesService.removeById(id));
    }

    /**
     * 删除部件-病害关系
     *
     * @param partsId
     * @return
     */
    @ApiOperation("删除部件-病害关系")
    //@RequiresPermissions("patrol:partsDiseases:remove")
    @DeleteMapping("/deleteByPartsId/{partsId}")
    public AjaxResult removeByPartsId(@PathVariable String partsId) {
        QueryWrapper<PatrolPartsDiseases> qw = new QueryWrapper<>();
        qw.eq("parts_id", partsId);
        return toAjax(patrolPartsDiseasesService.remove(qw));
    }

}
