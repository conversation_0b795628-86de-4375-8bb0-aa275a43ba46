package com.ruoyi.repote.service;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.repote.domain.RepoteForm;
import com.ruoyi.repote.domain.RepoteRecord;

/**
 * 填报格规范Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
public interface RepoteFormService extends IService<RepoteForm> {

    /**
     * 根据条件查询填报格规范数据列表
     * @param params
     */
    List<RepoteForm> findListByParam(Map<String, Object> params);


    /**
     * 传入formId，合并所有的excel文件
     * @param formId 表单ID
     * @return 返回合并后的RepoteForm类
     */
    void mergeExcel(String formId);


    /**
     * 传入form，合并所有的excel文件
     * @param form 表单
     * @return 返回合并后的RepoteForm类
     */
    void mergeExcel(RepoteForm form) throws Exception;


    /**
     * 从指定 URL 的 Excel 文件中获取最后一个有值的行号
     * @param fileUrl Excel 文件的 URL
     * @return 最后一个有值的行号（从0开始计数）
     */
    Integer findLastRowWithValues(String fileUrl);


    /**
     * 从指定 URL 的 Excel 文件中获取最后一个有值的行号
     * @param formId 表单ID
     * @param outputStream 输出流
     */
    void downloadAndZipFiles(String formId, OutputStream outputStream) throws Exception;




}
