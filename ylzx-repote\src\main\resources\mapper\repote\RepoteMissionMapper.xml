<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repote.mapper.RepoteMissionMapper">

    <resultMap type="com.ruoyi.repote.domain.RepoteMission" id="RepoteMissionResult">
        <result property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="name" column="name"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="url" column="url"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">
        id, status, name, user_id, user_name, dept_id, dept_name, url, remark, create_by,create_time,update_by,update_time    </sql>

    <sql id="where_column">
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="statusLike != null and statusLike != ''">
            AND status like CONCAT('%', #{statusLike}, '%')
        </if>
        <if test="name != null and name != ''">
            AND name = #{name}
        </if>
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND user_name = #{userName}
        </if>
        <if test="deptId != null and deptId != ''">
            AND dept_id = #{deptId}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name = #{deptName}
        </if>
        <if test="url != null and url != ''">
            AND url = #{url}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}
        </if>
        <if test="startTime != null and startTime != ''">
            AND update_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND update_time &lt;= #{endTime}
        </if>
    </sql>

    <sql id="set_column">
            <if test="status != null">
                status = #{status},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="userName != null">
                user_name = #{userName},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="deptName != null">
                dept_name = #{deptName},
            </if>
            <if test="url != null">
                url = #{url},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="RepoteMissionResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_mission
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="RepoteMissionResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_mission
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>

    <select id="getCountByReportMission" resultType="long" parameterType="String">
        SELECT COUNT(*)
        FROM (
                 SELECT form_id
                 FROM repote_record
                 WHERE mission_id = #{reportMission}
                   AND status = 3
                   AND user_id IN (
                     SELECT DISTINCT user_id
                     FROM repote_person
                     WHERE mission_id = #{reportMission}
                 )
                 GROUP BY form_id
                 HAVING COUNT(DISTINCT user_id) = (
                     SELECT COUNT(DISTINCT user_id)
                     FROM repote_person
                     WHERE mission_id = #{reportMission}
                 )
             ) AS valid_forms
    </select>

</mapper>