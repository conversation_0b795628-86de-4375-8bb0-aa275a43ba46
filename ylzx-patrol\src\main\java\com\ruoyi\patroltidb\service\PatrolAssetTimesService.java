package com.ruoyi.patroltidb.service;

import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 *
 */
public interface PatrolAssetTimesService {
    /**
     * 获取本年度的巡查记录次数统计
     * @param request 巡查记录请求
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param total 总记录数
     * @return 经常检查当年统计
     */
    <T> List<T> getCurrentTimesVO(AssetBaseDataRequest request, Long pageNum, Long pageSize, AtomicInteger total);
}
