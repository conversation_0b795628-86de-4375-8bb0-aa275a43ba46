package com.ruoyi.other.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;

/**
 * 大件运输对象 other_transportation
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@ApiModel(value="大件运输")
@TableName("other_transportation")
@Data
public class OtherTransportation extends BaseTableEntity {
    private static final long serialVersionUID = 1L;

    /** 项目名称 */
    @Excel(name = "项目名称")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /** 车辆货物信息 */
    @Excel(name = "车辆货物信息")
    @ApiModelProperty(value = "车辆货物信息")
    private String vehicleCargoInfo;

    /** 来文时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "来文时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "来文时间")
    private Date submissionTime;

    /** 运输起点 */
    @Excel(name = "运输起点")
    @ApiModelProperty(value = "运输起点")
    private String startingPoint;

    /** 运输终点 */
    @Excel(name = "运输终点")
    @ApiModelProperty(value = "运输终点")
    private String endingPoint;

    /** 总重 */
    @Excel(name = "总重")
    @ApiModelProperty(value = "总重")
    private BigDecimal totalWeight;

    /** 轴重 */
    @Excel(name = "轴重")
    @ApiModelProperty(value = "轴重")
    private BigDecimal axleLoad;

    /** 轴距 */
    @Excel(name = "轴距")
    @ApiModelProperty(value = "轴距")
    private BigDecimal wheelBase;

    /** 是否同意通行 */
    @Excel(name = "是否同意通行")
    @ApiModelProperty(value = "是否同意通行")
    private String isPass;

    /** 回函文号 */
    @Excel(name = "回函文号")
    @ApiModelProperty(value = "回函文号")
    private String replyNumber;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remarks;


    /** 删除标志 */
    @ApiModelProperty(value = "删除标志")
    private Long delFlag;

}
