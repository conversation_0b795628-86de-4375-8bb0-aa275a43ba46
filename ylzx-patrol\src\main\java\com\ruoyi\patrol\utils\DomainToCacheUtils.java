package com.ruoyi.patrol.utils;

import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.manage.api.domain.bridge.BridgeStaticResponse;
import com.ruoyi.manage.api.domain.culvert.BaseCulvertResponse;
import com.ruoyi.manage.api.domain.tunnel.BaseTunnelResponse;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseCulvertResponseCache;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.mapstruct.BaseDataToCacheMapper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 * BaseDataDomain转换为缓存对象的工具类
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public class DomainToCacheUtils {
    
    private static final int PARALLEL_THRESHOLD = 1000;
    private static final int BATCH_SIZE = 2000;
    private static final ForkJoinPool COMMON_POOL = ForkJoinPool.commonPool();

    /**
     * 将BaseDataDomain转换为对应的缓存对象
     *
     * @param domain    BaseDataDomain对象
     * @param cacheType 目标缓存类型
     * @return 转换后的缓存对象
     */
    @SuppressWarnings("unchecked")
    public static <T extends BaseDataCache> T convertToCache(BaseDataDomain domain, Class<T> cacheType) {
        if (domain == null) {
            return null;
        }
        
        if (cacheType == BaseBridgeResponseCache.class) {
            return (T) BaseDataToCacheMapper.INSTANCE.toBaseBridgeCache(domain);
        } else if (cacheType == BaseCulvertResponseCache.class) {
            return (T) BaseDataToCacheMapper.INSTANCE.toBaseCulvertCache(domain);
        } else if (cacheType == BaseTunnelResponseCache.class) {
            return (T) BaseDataToCacheMapper.INSTANCE.toBaseTunnelCache(domain);
        }
        
        throw new IllegalArgumentException("不支持的缓存类型: " + cacheType.getName());
    }
    
    /**
     * 根据资产类型将BaseDataDomain转换为对应的缓存对象
     *
     * @param domain    BaseDataDomain对象
     * @param assetType 资产类型
     * @return 转换后的缓存对象
     */
    public static BaseDataCache convertToCache(BaseDataDomain domain, AssetType assetType) {
        if (domain == null) {
            return null;
        }
        
        switch (assetType) {
            case BRIDGE:
                return BaseDataToCacheMapper.INSTANCE.toBaseBridgeCache(domain);
            case CULVERT:
                return BaseDataToCacheMapper.INSTANCE.toBaseCulvertCache(domain);
            case TUNNEL:
                return BaseDataToCacheMapper.INSTANCE.toBaseTunnelCache(domain);
            default:
                throw new IllegalArgumentException("不支持的资产类型: " + assetType);
        }
    }

    /**
     * 将桥梁响应对象直接转换为缓存对象（保留所有字段信息）
     *
     * @param response 桥梁响应对象
     * @return 桥梁缓存对象
     */
    public static BaseBridgeResponseCache convertToCache(BridgeStaticResponse response) {
        if (response == null) {
            return null;
        }
        return BaseDataToCacheMapper.INSTANCE.toBaseBridgeResponseCache(response);
    }

    /**
     * 将涵洞响应对象直接转换为缓存对象（保留所有字段信息）
     *
     * @param response 涵洞响应对象
     * @return 涵洞缓存对象
     */
    public static BaseCulvertResponseCache convertToCache(BaseCulvertResponse response) {
        if (response == null) {
            return null;
        }
        return BaseDataToCacheMapper.INSTANCE.toBaseCulvertResponseCache(response);
    }

    /**
     * 将隧道响应对象直接转换为缓存对象（保留所有字段信息）
     *
     * @param response 隧道响应对象
     * @return 隧道缓存对象
     */
    public static BaseTunnelResponseCache convertToCache(BaseTunnelResponse response) {
        if (response == null) {
            return null;
        }
        return BaseDataToCacheMapper.INSTANCE.toBaseTunnelResponseCache(response);
    }

    /**
     * 批量转换桥梁响应列表（保留所有字段信息）
     *
     * @param responses 桥梁响应对象列表
     * @return 桥梁缓存对象列表
     */
    public static List<BaseBridgeResponseCache> convertBridgeList(List<BridgeStaticResponse> responses) {
        if (responses == null || responses.isEmpty()) {
            return Collections.emptyList();
        }

        int size = responses.size();
        if (size > PARALLEL_THRESHOLD) {
            return responses.parallelStream()
                    .map(DomainToCacheUtils::convertToCache)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            return responses.stream()
                    .map(DomainToCacheUtils::convertToCache)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 批量转换涵洞响应列表（保留所有字段信息）
     *
     * @param responses 涵洞响应对象列表
     * @return 涵洞缓存对象列表
     */
    public static List<BaseCulvertResponseCache> convertCulvertList(List<BaseCulvertResponse> responses) {
        if (responses == null || responses.isEmpty()) {
            return Collections.emptyList();
        }

        int size = responses.size();
        if (size > PARALLEL_THRESHOLD) {
            return responses.parallelStream()
                    .map(DomainToCacheUtils::convertToCache)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            return responses.stream()
                    .map(DomainToCacheUtils::convertToCache)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 批量转换隧道响应列表（保留所有字段信息）
     *
     * @param responses 隧道响应对象列表
     * @return 隧道缓存对象列表
     */
    public static List<BaseTunnelResponseCache> convertTunnelList(List<BaseTunnelResponse> responses) {
        if (responses == null || responses.isEmpty()) {
            return Collections.emptyList();
        }

        int size = responses.size();
        if (size > PARALLEL_THRESHOLD) {
            return responses.parallelStream()
                    .map(DomainToCacheUtils::convertToCache)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            return responses.stream()
                    .map(DomainToCacheUtils::convertToCache)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 批量转换列表为缓存对象列表
     *
     * @param list      源列表
     * @param assetType 资产类型
     * @return 转换后的缓存对象列表
     */
    @SuppressWarnings("unchecked")
    public static <T extends BaseDataCache> List<T> convertList(List<?> list, AssetType assetType) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        
        Class<T> cacheType;
        switch (assetType) {
            case BRIDGE:
                cacheType = (Class<T>) BaseBridgeResponseCache.class;
                break;
            case CULVERT:
                cacheType = (Class<T>) BaseCulvertResponseCache.class;
                break;
            case TUNNEL:
                cacheType = (Class<T>) BaseTunnelResponseCache.class;
                break;
            default:
                throw new IllegalArgumentException("不支持的资产类型: " + assetType);
        }
        
        return convertList(list, cacheType);
    }

    /**
     * 批量转换列表为缓存对象列表
     *
     * @param list      源列表
     * @param cacheType 目标缓存类型
     * @return 转换后的缓存对象列表
     */
    @SuppressWarnings("unchecked")
    public static <T extends BaseDataCache> List<T> convertList(List<?> list, Class<T> cacheType) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        int size = list.size();
        if (size > PARALLEL_THRESHOLD) {
            return processBatchParallel(list, cacheType);
        } else {
            return processSequential(list, cacheType);
        }
    }

    /**
     * 并行处理大批量数据转换
     */
    private static <T extends BaseDataCache> List<T> processBatchParallel(List<?> list, Class<T> cacheType) {
        int threads = Math.min(Runtime.getRuntime().availableProcessors(), 
                             (list.size() + BATCH_SIZE - 1) / BATCH_SIZE);
        
        try {
            return COMMON_POOL.submit(() ->
                list.parallelStream()
                    .filter(item -> item instanceof BaseDataDomain)
                    .map(item -> convertToCache((BaseDataDomain) item, cacheType))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList())
            ).get();
        } catch (Exception e) {
            throw new IllegalStateException("并行处理转换失败", e);
        }
    }

    /**
     * 顺序处理小批量数据转换
     */
    private static <T extends BaseDataCache> List<T> processSequential(List<?> list, Class<T> cacheType) {
        List<T> result = new ArrayList<>(list.size());
        for (Object item : list) {
            if (item instanceof BaseDataDomain) {
                T converted = convertToCache((BaseDataDomain) item, cacheType);
                if (converted != null) {
                    result.add(converted);
                }
            }
        }
        return result;
    }
} 