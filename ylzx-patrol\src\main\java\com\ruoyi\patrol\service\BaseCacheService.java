package com.ruoyi.patrol.service;

import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.manage.api.domain.culvert.BaseCulvertRequest;
import com.ruoyi.manage.api.domain.culvert.BaseCulvertResponse;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.domain.dto.BaseDataDomainWithDistance;
import com.ruoyi.patrol.domain.dto.DeptToSectionDTO;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.enums.AssetType;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月29日 11:08
 */
public interface BaseCacheService {


    /**
     * 权限id设置
     * @param
     * @return
     */
    void setDeptIds(AssetBaseDataRequest request);

    /**
     * 获取总记录数
     * @param request 请求参数
     * @return 总记录数
     */
    int getTotalCount(AssetBaseDataRequest request);

    <T> void saveCache(List<T> baseDataList, Class<T> clazz);

    /**
     * 传入查询条件PatrolAssetCheckRequest，查询PatrolAssetCheckDTO
     */
    void selectBaseType(AssetType assetType);

    /**
     * 根据当前查询条件request返回管理处的记录List<sys_dept>
     * @param request 查询条件
     * @return List<sys_dept>
     */
    DeptToSectionDTO getDeptList(AssetBaseDataRequest request,  List<Long> allowDeptIds);


    /**
     * 把获取的selectBaseDataResponseByAssetIds的id提取出来返回List<String>
     */
    List<String> getBaseDataResponseId(AssetBaseDataRequest assetBaseDataRequest);

    /**
     * 根据资产id查询 BridgeStaticResponse 会失败重试3次
     *
     * @param assetBaseDataRequest 查询条件
     * @return List 基础数据
     */
    <T extends BaseDataCache> List<T> selectBaseDataResponseByAssetIds(
            AssetBaseDataRequest assetBaseDataRequest);

    /**
     * 根据条件查询资产信息
     */
    <T extends BaseDataCache> List<T> selectBaseDataResponseByAssetIds(
            AssetBaseDataRequest assetBaseDataRequest, Long PageNum, Long PageSize);

    /**
     * 获取分页后的资产id列表
     *
     * @param request  查询请求参数(不包含经纬度)
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param total    返回总记录数
     * @return 资产id列表
     */
    List<String> getBaseDataResponseIdByPage(AssetBaseDataRequest request, Long pageNum, Long pageSize, AtomicInteger total);

    /**
     * 从List<BaseDataDomainWithDistance<T>>中提取List<T> baseData
     *
     */
     <T extends BaseDataCache> List<T> extractBaseData(List<BaseDataDomainWithDistance<T>> baseDataList);


    /**
     * 根据查询条件查询资产列表
     *
     * @param assetBaseDataRequest 查询请求参数(包含检查类型、经纬度等)
     * @param pageNum              页码
     * @param pageSize             每页大小
     * @param total                返回总记录数
     * @param <T>                  资产数据类型(继承自BaseDataCache)
     * @return 资产列表(可能包含距离信息)
     * <p>
     * 处理流程:
     * 1. 验证必要参数(检查类型)
     * 2. 设置默认检查时间(如果未指定)
     * 3. 根据是否提供经纬度选择不同的查询策略:
     * - 有经纬度: 计算距离并排序
     * - 无经纬度: 不计算距离，提高性能
     * @throws IllegalArgumentException 当检查类型为空时抛出
     */
    <T extends BaseDataCache> List<BaseDataDomainWithDistance<T>> listBy(
            AssetBaseDataRequest assetBaseDataRequest, Long pageNum, Long pageSize, AtomicInteger total);
}
