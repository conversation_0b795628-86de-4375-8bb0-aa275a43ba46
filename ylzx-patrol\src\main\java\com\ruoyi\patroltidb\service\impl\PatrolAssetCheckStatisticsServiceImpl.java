package com.ruoyi.patroltidb.service.impl;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.patrol.domain.dto.BaseDataDomainWithDistance;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.vo.AssetCheckStatisticsVO;
import com.ruoyi.patrol.mapper.PatrolAssetCheckMapper;
import com.ruoyi.patrol.service.BaseCacheService;
import com.ruoyi.patroltidb.service.PatrolAssetCheckStatisticsService;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteMaintenanceSectionService;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.Comparator;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;

/**
 * 巡查资产检查统计服务实现类
 */
@Slf4j
@Slave
@Service
public class PatrolAssetCheckStatisticsServiceImpl implements PatrolAssetCheckStatisticsService {

    @Resource
    PatrolAssetCheckMapper patrolAssetCheckMapper;
    @Resource
    BaseCacheService baseCacheService;
    @Resource
    RemoteDeptAuthService remoteDeptAuthService;
    @Resource
    RemoteMaintenanceSectionService remoteMaintenanceSectionService;
    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    
    // 定义常量，提高可维护性
    private static final String DATASOURCE_SLAVE = "slave";
    private static final String MAINTENANCE_SECTION_DEPT_MAP_CACHE_KEY = "maintenance:section:dept:map";
    private static final String UNKNOWN_SECTION = "未知";
    private static final String ERROR_MSG_EXPIRY_EMPTY = "检查时间不能为空";
    private static final String ERROR_MSG_SECTION_MAP_FAILED = "获取养护路段ID与部门名称的映射关系失败";
    private static final int CACHE_EXPIRY_MINUTES = 10;
    private static final int CACHE_MAX_SIZE = 100;

    private static final Cache<String, Map<String, String>> maintenanceSectionDeptNameCache = Caffeine.newBuilder()
            .expireAfterWrite(CACHE_EXPIRY_MINUTES, TimeUnit.MINUTES)
            .maximumSize(CACHE_MAX_SIZE)
            .build();

    /**
     * 获取年度统计数据
     * <p>
     * 处理流程:
     * 1. 计算需要处理的月份数。
     * 2. 使用自定义线程池并行执行每个月的统计任务。
     * 3. 等待所有任务完成后合并结果。
     * <p>
     * 优化策略:
     * - 使用线程池并行处理以提高性能。
     * - 根据当前年份动态调整处理的月份数。
     * <p>
     * 示例:
     * - 输入: 2023年
     * - 输出: 2023年每个月的资产检查统计数据列表。
     *
     * @param request 资产基础数据请求对象
     * @return 年度统计数据列表
     */
    @Override
    public List<AssetCheckStatisticsVO> getYearlyStatistics(AssetBaseDataRequest request) {
        request.setIsCheck(null);
        if (request.getCheckTimeHas() == null) {
            throw new RuntimeException(ERROR_MSG_EXPIRY_EMPTY);
        }
        int year = request.getCheckTimeHas().getYear();
        int monthsToProcess = calculateMonthsToProcess(year);

        if (request.getAssetType() == null) {
            return List.of();
        }

        baseCacheService.setDeptIds(request);
        request.setDataRule(false);

        List<CompletableFuture<List<AssetCheckStatisticsVO>>> monthlyTasks = IntStream
                .rangeClosed(1, monthsToProcess)
                .mapToObj(month -> CompletableFuture.supplyAsync(
                        () -> getMonthlyStatistics(year, month, request), 
                        taskExecutor))
                .toList();

        // 等待所有任务完成并获取结果
        CompletableFuture.allOf(monthlyTasks.toArray(CompletableFuture[]::new)).join();

        List<List<AssetCheckStatisticsVO>> monthlyResults = monthlyTasks.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
        List<AssetCheckStatisticsVO> hasData = monthlyResults.stream()
                .flatMap(List::stream)  // 将List<List>展平为单个Stream
                .filter(stat -> !(stat.getManagementOffice() == null
                        && stat.getSectionName() == null
                        && stat.getTotalCount() == 0))  // 过滤掉无效数据
                .collect(Collectors.toList());  // 收集有效数据
        return hasData.stream()
                .sorted(Comparator.comparing(AssetCheckStatisticsVO::getManagementOffice, Comparator.nullsLast(String::compareTo))
                        .thenComparing(AssetCheckStatisticsVO::getSectionName, Comparator.nullsLast(String::compareTo))
                        .thenComparing(AssetCheckStatisticsVO::getMonth, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    /**
     * 计算需要处理的月份数
     * <p>
     * 处理流程:
     * 1. 获取当前日期。
     * 2. 如果目标年份是当前年份，则返回当前月份数。
     * 3. 否则，返回12个月。
     *
     * @param year 目标年份
     * @return 需要处理的月份数
     */
    private int calculateMonthsToProcess(int year) {
        LocalDate now = LocalDate.now();
        return year == now.getYear() ? now.getMonthValue() : 12;
    }


    private Map<String, String> getMaintenanceSectionDeptNameMap() {
        // 尝试从本地缓存获取
        String cacheKey = MAINTENANCE_SECTION_DEPT_MAP_CACHE_KEY;
        Map<String, String> cachedMap = maintenanceSectionDeptNameCache.getIfPresent(cacheKey);

        if (cachedMap != null) {
            return cachedMap;
        }

        // 如果本地缓存没有，从远程服务获取
        R<Map<String, String>> maintenanceSectionDeptNameMapR = remoteMaintenanceSectionService.getMaintenanceSectionDeptNameMap();
        if (maintenanceSectionDeptNameMapR.getCode() != 200) {
            log.error("获取养护路段ID与部门名称的映射关系失败:{}", maintenanceSectionDeptNameMapR.getMsg());
            throw new RuntimeException(ERROR_MSG_SECTION_MAP_FAILED);
        }

        // 将结果存入本地缓存
        Map<String, String> result = maintenanceSectionDeptNameMapR.getData();
        if (result != null && !result.isEmpty()) {
            maintenanceSectionDeptNameCache.put(cacheKey, result);
        }

        return result;
    }

    /**
     * 获取单月统计数据
     * <p>
     * 处理流程:
     * 1. 切换到从库进行查询以减轻主库压力。
     * 2. 创建新的请求对象以避免共享状态。
     * 3. 设置请求的过期时间为月初。
     * 4. 查询并返回该月的统计数据。
     *
     * @param year    目标年份
     * @param month   目标月份
     * @param request 资产基础数据请求对象
     * @return 单月的资产检查统计数据列表
     */
    private List<AssetCheckStatisticsVO> getMonthlyStatistics(
            int year, int month, AssetBaseDataRequest request) {
        // 切换到从库查询
        DynamicDataSourceContextHolder.push(DATASOURCE_SLAVE);
        try {
            // 创建新的请求对象,避免共享状态
            AssetBaseDataRequest monthRequest = new AssetBaseDataRequest();
            BeanUtils.copyProperties(request, monthRequest);

            // 设置月初时间
            LocalDateTime monthStart = LocalDateTime.of(year, month, 1, 0, 0);
            monthRequest.setCheckTimeHas(monthStart);
            //  格式化 2024-03
            String formatTime = String.format("%d-%02d", year, month);

            // 使用BaseCacheService的listBy方法获取带有距离和巡查状态信息的数据
            AtomicInteger total = new AtomicInteger(0);
            List<BaseDataDomainWithDistance<BaseDataCache>> dataList = baseCacheService.listBy(
                    monthRequest, null, null, total);

            // 如果dataList为空，返回空记录
            if (dataList == null || dataList.isEmpty()) {
                return Collections.emptyList();
            }

            // 获取养护路段与部门名称的映射关系
            Map<String, String> maintenanceSectionDeptNameMap = getMaintenanceSectionDeptNameMap();
            
            // 按管理处和养护路段分组统计
            Map<String, Map<String, List<BaseDataDomainWithDistance<BaseDataCache>>>> groupedData = 
                groupDataByDeptAndSection(dataList, maintenanceSectionDeptNameMap);

            // 转换为AssetCheckStatisticsVO列表
            return convertToStatisticsVOList(groupedData, month, formatTime);
        } finally {
            // 清理数据源上下文
            DynamicDataSourceContextHolder.clear();
        }
    }
    
    /**
     * 将数据按管理处和养护路段进行分组
     *
     * @param dataList 数据列表
     * @param maintenanceSectionDeptNameMap 养护路段与部门名称的映射关系
     * @return 分组后的数据
     */
    private Map<String, Map<String, List<BaseDataDomainWithDistance<BaseDataCache>>>> groupDataByDeptAndSection(
            List<BaseDataDomainWithDistance<BaseDataCache>> dataList,
            Map<String, String> maintenanceSectionDeptNameMap) {
        
        return dataList.stream()
                .filter(data -> data.getBaseData().getMaintenanceSectionId() != null)  // 过滤掉maintenanceSectionId为空的记录
                .collect(Collectors.groupingBy(
                        data -> maintenanceSectionDeptNameMap.getOrDefault(
                                data.getBaseData().getMaintenanceSectionId(), UNKNOWN_SECTION),  // 管理处名称
                        Collectors.groupingBy(
                                data -> data.getBaseData().getMaintenanceSectionName(),  // 养护路段名称
                                Collectors.toList()
                        )
                ));
    }
    
    /**
     * 将分组数据转换为统计VO列表
     * 
     * @param groupedData 分组后的数据
     * @param month 月份
     * @param formatTime 格式化时间
     * @return 统计VO列表
     */
    private List<AssetCheckStatisticsVO> convertToStatisticsVOList(
            Map<String, Map<String, List<BaseDataDomainWithDistance<BaseDataCache>>>> groupedData,
            int month, String formatTime) {
            
        return groupedData.entrySet().stream()
                .flatMap(deptEntry -> deptEntry.getValue().entrySet().stream()
                        .map(sectionEntry -> {
                            List<BaseDataDomainWithDistance<BaseDataCache>> dataInGroup = sectionEntry.getValue();

                            // 获取该组中第一条记录的maintenanceSectionId作为sectionId
                            String sectionId = dataInGroup.stream()
                                    .findFirst()
                                    .map(data -> data.getBaseData().getMaintenanceSectionId())
                                    .orElse(null);

                            // 统计已巡查和未巡查的数量 - 使用Collectors.teeing在一次遍历中完成两个计数
                            Predicate<BaseDataDomainWithDistance<BaseDataCache>> isCompleted = 
                                data -> Boolean.TRUE.equals(data.getIsCheck());
                            
                            Map.Entry<Long, Long> counts = dataInGroup.stream()
                                .collect(Collectors.teeing(
                                    Collectors.filtering(isCompleted, Collectors.counting()),
                                    Collectors.filtering(isCompleted.negate(), Collectors.counting()),
                                    (completed, inPeriod) -> Map.entry(completed, inPeriod)
                                ));
                            
                            long completedCount = counts.getKey();
                            long inPeriodCount = counts.getValue();

                            return AssetCheckStatisticsVO.builder()
                                    .managementOffice(deptEntry.getKey())
                                    .sectionName(sectionEntry.getKey())
                                    .sectionId(sectionId)
                                    .month(month)
                                    .formatTime(formatTime)
                                    .totalCount(dataInGroup.size())
                                    .inPeriodCount((int) inPeriodCount)
                                    .completedCount((int) completedCount)
                                    .build();
                        }))
                .collect(Collectors.toList());
    }


}
