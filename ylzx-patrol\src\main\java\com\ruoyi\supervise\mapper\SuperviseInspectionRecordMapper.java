package com.ruoyi.supervise.mapper;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.supervise.domain.SuperviseInspectionRecord;

/**
 * 督查记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface SuperviseInspectionRecordMapper extends BaseMapper<SuperviseInspectionRecord> {

    List<SuperviseInspectionRecord> findListByParam(Map<String, Object> params);


}
