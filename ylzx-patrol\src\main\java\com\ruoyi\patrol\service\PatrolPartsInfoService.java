package com.ruoyi.patrol.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patrol.domain.PatrolPartsInfo;
import com.ruoyi.patrol.enums.InspectionType;

import java.util.List;

/**
 * 检查类型Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface PatrolPartsInfoService extends IService<PatrolPartsInfo> {

    /**
     * 根据传入的类型查询检查部位
     * @param partsType  类型(1:桥梁日常巡查;2:桥梁经常检查;3:涵洞定期检查;4:涵洞经常检查;5:隧道日常巡查;6:隧道经常检查;)
     * @return List<PatrolPartsInfo>
     */
    List<PatrolPartsInfo> selectByType(InspectionType partsType);


}
