package com.ruoyi.patrol.utils.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.PrecisionModel;
import org.locationtech.jts.io.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月26日 17:08
 */
@MappedTypes({String.class})
@MappedJdbcTypes({JdbcType.OTHER})
public class GeometryTypeHandler extends BaseTypeHandler<String> {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory(new PrecisionModel());
    private static final WKTReader WKT_READER = new WKTReader(GEOMETRY_FACTORY);
    private static final WKBWriter WKB_WRITER = new WKBWriter(2, ByteOrderValues.LITTLE_ENDIAN, false);
    private static final WKBReader WKB_READER = new WKBReader();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        try {
            Geometry geometry = WKT_READER.read(parameter);
            byte[] geometryBytes = WKB_WRITER.write(geometry);
            ByteBuffer buffer = ByteBuffer.allocate(geometryBytes.length + 4);
            buffer.putInt(0); // SRID
            buffer.put(geometryBytes);
            ps.setBytes(i, buffer.array());
        } catch (Exception e) {
            throw new SQLException("设置非空参数时出错", e);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getGeometryString(rs.getBinaryStream(columnName));
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getGeometryString(rs.getBinaryStream(columnIndex));
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getGeometryString(new ByteArrayInputStream(cs.getBytes(columnIndex)));
    }

    private String getGeometryString(InputStream inputStream) throws SQLException {
        if (inputStream == null) {
            return null;
        }
        try {
            Geometry geometry = getGeometryFromInputStream(inputStream);
            return geometry.toString();
        } catch (Exception e) {
            throw new SQLException("从输入流获取几何字符串时出错", e);
        }
    }

    private Geometry getGeometryFromInputStream(InputStream inputStream) throws Exception {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024]; // 增加缓冲区大小以减少读取次数
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }

            byte[] geometryAsBytes = baos.toByteArray();
            if (geometryAsBytes.length < 5) {
                throw new Exception("无效的几何数据");
            }

            ByteBuffer byteBuffer = ByteBuffer.wrap(geometryAsBytes);
            int srid = byteBuffer.getInt();
            byte[] wkb = new byte[geometryAsBytes.length - 4];
            byteBuffer.get(wkb);

            Geometry geometry = WKB_READER.read(wkb);
            geometry.setSRID(srid);
            return geometry;
        }
    }
}


