<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.supervise.mapper.SuperviseInspectionDetailMapper">

    <resultMap type="com.ruoyi.supervise.domain.SuperviseInspectionDetail" id="SuperviseInspectionDetailResult">
            <result property="inspectionRecordId" column="inspection_record_id"/>
            <result property="content" column="content"/>
            <result property="suggestion" column="suggestion"/>
            <result property="status" column="status"/>
            <result property="rectificationTime" column="rectification_time"/>
            <result property="beforeImage" column="before_image"/>
            <result property="afterImage" column="after_image"/>
            <result property="remark" column="remark"/>
    </resultMap>

    <sql id="base_column">
 inspection_record_id, content, suggestion, status, rectification_time, before_image, after_image, remark    </sql>

    <sql id="where_column">
        <if test="inspectionRecordId != null and inspectionRecordId != ''">
            AND inspection_record_id = #{inspectionRecordId}
        </if>
        <if test="inspectionRecordIdLike != null and inspectionRecordIdLike != ''">
            AND inspection_record_id like CONCAT('%', #{inspectionRecordIdLike}, '%')
        </if>
        <if test="content != null and content != ''">
            AND content = #{content}
        </if>
        <if test="suggestion != null and suggestion != ''">
            AND suggestion = #{suggestion}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="rectificationTime != null and rectificationTime != ''">
            AND rectification_time = #{rectificationTime}
        </if>
        <if test="rectificationTimes != null and rectificationTimes != ''">
            AND date_format(rectification_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(#{rectificationTimes}, '%Y-%m-%d')
        </if>
        <if test="rectificationTimee != null and rectificationTimee != ''">
            AND date_format(rectification_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(#{rectificationTimee}, '%Y-%m-%d')
        </if>
        <if test="beforeImage != null and beforeImage != ''">
            AND before_image = #{beforeImage}
        </if>
        <if test="afterImage != null and afterImage != ''">
            AND after_image = #{afterImage}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}
        </if>
    </sql>

    <sql id="set_column">
            <if test="inspectionRecordId != null">
                inspection_record_id = #{inspectionRecordId},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="suggestion != null">
                suggestion = #{suggestion},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="rectificationTime != null">
                rectification_time = #{rectificationTime},
            </if>
            <if test="beforeImage != null">
                before_image = #{beforeImage},
            </if>
            <if test="afterImage != null">
                after_image = #{afterImage},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="SuperviseInspectionDetailResult">
        SELECT
        <include refid="base_column"/>
        FROM supervise_inspection_detail
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="SuperviseInspectionDetailResult">
        SELECT
        <include refid="base_column"/>
        FROM supervise_inspection_detail
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

</mapper>