package com.ruoyi.patroltidb.service;

import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.vo.AssetCheckStatisticsVO;

import java.util.List;

public interface PatrolAssetCheckStatisticsService {
    /**
     * 获取年度统计数据
     * <p>
     * 处理流程:
     * 1. 计算需要处理的月份数。
     * 2. 使用自定义线程池并行执行每个月的统计任务。
     * 3. 等待所有任务完成后合并结果。
     * <p>
     * 优化策略:
     * - 使用线程池并行处理以提高性能。
     * - 根据当前年份动态调整处理的月份数。
     * <p>
     * 示例:
     * - 输入: 2023年
     * - 输出: 2023年每个月的资产检查统计数据列表。
     *
     * @param request 资产基础数据请求对象
     * @return 年度统计数据列表
     */
    public List<AssetCheckStatisticsVO> getYearlyStatistics(AssetBaseDataRequest request);
}
