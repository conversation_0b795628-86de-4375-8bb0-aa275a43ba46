package com.ruoyi.patroltidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheckDetail;

import java.util.List;
import java.util.Map;

/**
 * 隧道机电巡查明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
public interface PatrolDeviceCheckDetailService extends IService<PatrolDeviceCheckDetail> {

    /**
     * 根据条件查询隧道机电巡查明细数据列表
     * @param params
     */
    List<PatrolDeviceCheckDetail> findListByParam(Map<String, Object> params);

}
