package com.ruoyi.test.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "检查类型")
@TableName("patrol_bridge_check")
@Data
public class Test extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 状态list */
    @TableField(exist = false)
    List<String> statusList;

    /** 巡查结果 */
    @TableField(exist = false)
    Boolean isException;

//    /** 隧道巡检查详情 */
//    @TableField(exist = false)
//    @JsonProperty("details")
//    List<PatrolBridgeCheckDetail> patrolBridgeCheckDetailList;

}
