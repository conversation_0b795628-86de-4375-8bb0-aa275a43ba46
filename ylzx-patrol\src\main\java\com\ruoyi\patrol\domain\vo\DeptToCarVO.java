package com.ruoyi.patrol.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 巡查车辆实时坐标（15分钟过期）
 * @author: sfc
 * @date: 2025年03月17日 15:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeptToCarVO {


    @ApiModelProperty(value = "管理处ID")
    private Long deptId;

    @ApiModelProperty(value = "管理处")
    private String deptName;

    @ApiModelProperty(value = "在线巡查车数量")
    private Integer carNum;

}
