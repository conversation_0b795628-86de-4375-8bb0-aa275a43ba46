package com.ruoyi.patroltidb.domain;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.patrol.domain.PatrolCheckBase;
import com.ruoyi.patrol.enums.DeleteFlagType;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.enums.StageType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 隧道机电日常巡查对象 patrol_device_check
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
@ApiModel(value="隧道机电日常巡查")
@TableName("patrol_device_check")
@Data
public class PatrolDeviceCheck extends PatrolCheckBase {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 记录类型（1经常检查，2日常巡查，3故障记录，4故障月报） */
    @ApiModelProperty(value = "记录类型（1经常检查，2日常巡查，3故障记录，4故障月报）")
    private Integer recordType;

//    @ApiModelProperty(value = "检查类型: 1, 桥梁日常巡查; 2, 桥梁经常检查; " +
//            "3, 涵洞日常检查; 4, 涵洞经常检查; " +
//            "5, 隧道日常巡查; 6, 隧道经常检查; " +
//            "7, 隧道机电日常巡查; 8, 隧道机电经常检查;")
//    @EnumValue
//    private InspectionType type;

//    /** 资产id */
//    @ApiModelProperty(value = "资产id")
//    private String assetId;

    @ApiModelProperty(value = "资产idList")
    @TableField(exist = false)
    private List<String> assetIds;

    @ApiModelProperty(value = "检查开始日期")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date oprTimes;

    @ApiModelProperty(value = "检查结束日期")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date oprTimee;

    /** 资产名称 */
    @Excel(name = "资产名称", sort = 4)
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /** 资产编码 */
    @Excel(name = "资产编码")
    @ApiModelProperty(value = "资产编码")
    private String assetCode;

    /** 路线编码 */
    @Excel(name = "路线编码", sort = 1)
    @ApiModelProperty(value = "路线编码")
    private String routeCode;

    /** 路线名称 */
    @Excel(name = "路线名称", sort = 2)
    @ApiModelProperty(value = "路线名称")
    private String routeName;

    /** 管养单位id */
    @ApiModelProperty(value = "管养单位id")
    private Long domainId;

    /** 管养单位名称 */
    @Excel(name = "管养单位")
    @TableField(exist = false)
    private String deptName;

//    /** 操作时间 */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
//    @ApiModelProperty(value = "操作时间")
//    private Date checkTime;

    /** 天气 */
    @Excel(name = "天气")
    @ApiModelProperty(value = "天气")
    private String weather;

    /** 检查人Id */
    @ApiModelProperty(value = "检查人Id")
    private Long checkerId;

    /** 检查人 */
    @Excel(name = "检查人")
    @ApiModelProperty(value = "检查人")
    private String checker;

    /** 记录人Id */
    @ApiModelProperty(value = "记录人Id")
    private Long recorderId;

    /** 记录人 */
    @Excel(name = "记录人")
    @ApiModelProperty(value = "记录人")
    private String recorder;

    /** 制表 */
    @Excel(name = "制表")
    @ApiModelProperty(value = "制表")
    private String maker;

    /** 复核 */
    @Excel(name = "复核")
    @ApiModelProperty(value = "复核")
    private String reviewer;

    /** 审定 */
    @Excel(name = "审定")
    @ApiModelProperty(value = "审定")
    private String approver;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

//    @Excel(name = "到期时间")
//    @ApiModelProperty(value = "到期时间")
//    private LocalDateTime expiry;

//    @Excel(name = "周期(天)")
//    @ApiModelProperty(value = "周期(天)")
//    private Integer frequency;

    @Excel(name = "阶段")
    @ApiModelProperty(value = "阶段(经常检查)0:在期,1:完成,2过期")
    @EnumValue
    private StageType stage;

    /** 删除标识 */
    @ApiModelProperty(value = "删除标识")
    @EnumValue
    private DeleteFlagType delFlag;

    /** 机电详情 */
    @TableField(exist = false)
    private List<PatrolDeviceCheckDetail> domains;

    /** 故障上报 */
    @TableField(exist = false)
    private List<PatrolDeviceFaultRecord> faultRecordList;

}
