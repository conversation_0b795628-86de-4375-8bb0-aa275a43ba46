package com.ruoyi.other.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.other.domain.OtherTransportationAttachment;

import java.util.List;
import java.util.Map;


/**
 * 大件运输Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface OtherTransportationAttachmentService extends IService<OtherTransportationAttachment> {

    /**
     * 根据条件查询大件运输列表
     * @param params
     */
    List<OtherTransportationAttachment> findListByParam(Map<String, Object> params);

}
