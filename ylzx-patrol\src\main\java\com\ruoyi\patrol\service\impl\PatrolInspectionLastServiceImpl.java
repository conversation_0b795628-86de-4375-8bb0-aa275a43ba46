package com.ruoyi.patrol.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolFrequencySettings;
import com.ruoyi.patrol.domain.PatrolInspectionLast;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.mapper.PatrolInspectionLastMapper;
import com.ruoyi.patrol.service.BaseCacheService;
import com.ruoyi.patrol.service.PatrolFrequencySettingsService;
import com.ruoyi.patrol.service.PatrolInspectionLastService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * (PatrolInspectionLast)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-17 15:01:08
 */
@Service("patrolInspectionLastService")
@Master
public class PatrolInspectionLastServiceImpl extends ServiceImpl<PatrolInspectionLastMapper, PatrolInspectionLast>
        implements PatrolInspectionLastService {
    @Resource
    private PatrolFrequencySettingsService patrolFrequencySettingsService;
    @Resource
    private BaseCacheService baseCacheService;


    /**
     * 根据type查询
     * @param type 类型
     * @return List<PatrolInspectionLast>
     */
    @Override
    public List<PatrolInspectionLast> listByType(AssetType type) {
        QueryWrapper<PatrolInspectionLast> queryWrapper = new QueryWrapper<>();
        if(type!=null) queryWrapper.eq("type", type.getValue());
        return this.list(queryWrapper);
    }

    /**
     * 根据type查询
     * @param type 类型
     * @param flag true:generatedAt(日常检查) false:generatedBy(经常检查)
     * @return Map<String, LocalDateTime> assetId到生成时间的映射
     */
    public Map<String, LocalDateTime> assetMapByType(AssetType type, Boolean flag){
        Map<String, LocalDateTime> assetMap;
        List<PatrolInspectionLast> patrolInspectionLastList = this.listByType(type);

        if (flag) {
            assetMap = patrolInspectionLastList.stream()
                    .filter(p -> p.getGeneratedAt() != null) // 过滤掉 getGeneratedAt 为 null 的条目
                    .collect(Collectors.toMap(PatrolInspectionLast::getAssetId, PatrolInspectionLast::getGeneratedAt));
        } else {
            assetMap = patrolInspectionLastList.stream()
                    .filter(p -> p.getGeneratedBy() != null) // 过滤掉 getGeneratedBy 为 null 的条目
                    .collect(Collectors.toMap(PatrolInspectionLast::getAssetId, PatrolInspectionLast::getGeneratedBy));
        }
        return assetMap;
    }


    /**
     * @param assetIds 资产id
     * @Description: 传入bridgeId修改generatedAt为当前时间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateGeneratedAtByAssetIds(List<String> assetIds, AssetType type, LocalDateTime now) {
        // 传入bridgeId修改generatedAt为当前时间
        List<PatrolInspectionLast> patrolInspectionLastList = new ArrayList<>();
        for (String assetId : assetIds) {
            PatrolInspectionLast patrolInspectionLast = new PatrolInspectionLast();
            patrolInspectionLast.setAssetId(assetId);
            patrolInspectionLast.setGeneratedAt(now);
            patrolInspectionLast.setType(type);
            patrolInspectionLastList.add(patrolInspectionLast);
        }
        return this.insertOrUpdateBatch(patrolInspectionLastList);
    }


    /**
     * @param assetIds 资产id
     * @Description: 传入bridgeId修改generatedAt为当前时间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateGeneratedAtByAssetIdList(List<String> assetIds, AssetType type, LocalDateTime generatedAt, LocalDateTime generatedBy) {
        return baseMapper.insertOrUpdateBatchByAssetIdList(assetIds, type, generatedAt, generatedBy);
    }


    /**
     * 获取date需要自动生成的桥梁ID列表
     * @param date 传入日期，避免极端情况跨日期
     * @return 日期到桥梁ID列表的映射
     */
    public <T extends PatrolAssetCheck>  Map<LocalDate, Map<LocalDate, List<String>>> generaByAssetIdList(
            LocalDate date, InspectionType type, List<String> ids, Map<String, Integer> assetIdToFrequencyMap) {

        ids.stream().map(id -> {
            T t = (T) new PatrolAssetCheck();
            t.setAssetId(id);
            t.setType(type);
            t.setCheckTime(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));

            return id;
        }).collect(Collectors.toList());

        // 生成assetId为key, generatedAt为value的map
        CompletableFuture<Map<String, LocalDateTime>> assetIdGeneratedAtMapFuture =
                CompletableFuture.supplyAsync( () -> this.assetMapByType(type.getAssetType(), true));


        // 生成assetId为key, dayFrequency为value的map
        CompletableFuture<Map<String, Integer>> assetIdDayFrequencyMapFuture =
                CompletableFuture.supplyAsync(() -> patrolFrequencySettingsService.assetMapByType(type.getAssetType(), true));


        // 结果存储
        Map<LocalDate, Map<LocalDate, List<String>>> dateToAssetIdsMap = new ConcurrentHashMap<>();

        // 组合两个Future并处理结果
        CompletableFuture<Void> combinedFuture = assetIdDayFrequencyMapFuture.thenCombine(
                assetIdGeneratedAtMapFuture,
                (assetIdFrequencyMap, assetIdGeneratedAtMap) -> {
                    // 将assetIdDayFrequencyMap赋值给assetIdToDayFrequencyMap
                    assetIdToFrequencyMap.clear();
                    assetIdToFrequencyMap.putAll(assetIdFrequencyMap);

                    assetIdFrequencyMap.forEach((assetId, frequency) -> {
                        LocalDateTime lastGeneratedAt = assetIdGeneratedAtMap.get(assetId);

                        // 计算下次生成日期
                        LocalDate nextGeneratedDate = (lastGeneratedAt != null) ?
                                lastGeneratedAt.plusDays(frequency).toLocalDate() :
                                date;
                        // 计算上次生成日期
                        LocalDate lastGeneratedDate = (lastGeneratedAt != null) ?
                                lastGeneratedAt.toLocalDate() :
                                date.minusDays(frequency);

                        // 检查是否需要在给定日期生成
                        while (nextGeneratedDate.isBefore(date) || nextGeneratedDate.isEqual(date)) {
                            dateToAssetIdsMap.computeIfAbsent(nextGeneratedDate, k -> new ConcurrentHashMap<>())
                                    .computeIfAbsent(lastGeneratedDate, k -> Collections.synchronizedList(new ArrayList<>()))
                                    .add(assetId);

                            lastGeneratedDate = nextGeneratedDate;
                            nextGeneratedDate = nextGeneratedDate.plusDays(frequency);
                        }
                    });
                    return null;
                }
        );

        // 处理异常
        combinedFuture.exceptionally(e -> {
            e.printStackTrace();
            return null;
        }).join(); // 等待所有任务完成

        // 将ConcurrentHashMap转换为普通的HashMap以返回
        return new HashMap<>(dateToAssetIdsMap);
    }




    /**
     * 获取date需要自动生成的桥梁ID列表
     * @param date 传入日期，避免极端情况跨日期
     * @return 日期到桥梁ID列表的映射
     */
    public Map<LocalDate, Map<LocalDate, List<String>>> generatedAtByAssetIdList(
            LocalDate date, AssetType assetType, Map<String, Integer> assetIdToFrequencyMap) {


        // 生成assetId为key, generatedAt为value的map
        CompletableFuture<Map<String, LocalDateTime>> assetIdGeneratedAtMapFuture =
                CompletableFuture.supplyAsync( () -> this.assetMapByType(assetType, true));


        // 生成assetId为key, dayFrequency为value的map
        CompletableFuture<Map<String, Integer>> assetIdDayFrequencyMapFuture =
                CompletableFuture.supplyAsync(() -> patrolFrequencySettingsService.assetMapByType(assetType, true));


        // 结果存储
        Map<LocalDate, Map<LocalDate, List<String>>> dateToAssetIdsMap = new ConcurrentHashMap<>();

        // 组合两个Future并处理结果
        CompletableFuture<Void> combinedFuture = assetIdDayFrequencyMapFuture.thenCombine(
                assetIdGeneratedAtMapFuture,
                (assetIdFrequencyMap, assetIdGeneratedAtMap) -> {
                    // 将assetIdDayFrequencyMap赋值给assetIdToDayFrequencyMap
                    assetIdToFrequencyMap.clear();
                    assetIdToFrequencyMap.putAll(assetIdFrequencyMap);

                    assetIdFrequencyMap.forEach((assetId, frequency) -> {
                        LocalDateTime lastGeneratedAt = assetIdGeneratedAtMap.get(assetId);

                        // 计算下次生成日期
                        LocalDate nextGeneratedDate = (lastGeneratedAt != null) ?
                                lastGeneratedAt.plusDays(frequency).toLocalDate() :
                                date;
                        // 计算上次生成日期
                        LocalDate lastGeneratedDate = (lastGeneratedAt != null) ?
                                lastGeneratedAt.toLocalDate() :
                                date.minusDays(frequency);

                        // 检查是否需要在给定日期生成
                        while (nextGeneratedDate.isBefore(date) || nextGeneratedDate.isEqual(date)) {
                            dateToAssetIdsMap.computeIfAbsent(nextGeneratedDate, k -> new ConcurrentHashMap<>())
                                    .computeIfAbsent(lastGeneratedDate, k -> Collections.synchronizedList(new ArrayList<>()))
                                    .add(assetId);

                            lastGeneratedDate = nextGeneratedDate;
                            nextGeneratedDate = nextGeneratedDate.plusDays(frequency);
                        }
                    });
                    return null;
                }
        );

        // 处理异常
        combinedFuture.exceptionally(e -> {
            e.printStackTrace();
            return null;
        }).join(); // 等待所有任务完成

        // 将ConcurrentHashMap转换为普通的HashMap以返回
        return new HashMap<>(dateToAssetIdsMap);
    }


    /**
     * 获取date需要自动生成的桥梁ID列表
     * @param yearMonth 传入日期，避免极端情况跨日期
     * @return 日期到桥梁ID列表的映射
     */

    public Map<YearMonth,Map<YearMonth, List<String>>> generatedAtByAssetIdList(
            YearMonth yearMonth, AssetType assetType, Map<String, Integer> assetIdToFrequencyMap) {
        // 生成assetId为key, generatedAt为value的map
        CompletableFuture<Map<String, LocalDateTime>> assetIdGeneratedAtMapFuture =
                CompletableFuture.supplyAsync( () -> this.assetMapByType(assetType, false));
        // 生成assetId为key, dayFrequency为value的map
        CompletableFuture<Map<String, Integer>> assetIdDayFrequencyMapFuture =
                CompletableFuture.supplyAsync(() -> {
                    // 获取现有的频率设置
                    Map<String, Integer> assetIdMap = patrolFrequencySettingsService.assetMapByType(assetType, false);
                    // 获取所有资产ID
                    AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
                    assetBaseDataRequest.setAssetType(assetType);
                    assetBaseDataRequest.setDataRule(false);
                    List<String> ids = baseCacheService.getBaseDataResponseId(assetBaseDataRequest);
                    
                    // 为没有频率设置的资产ID设置默认值1
                    ids.forEach(id -> assetIdMap.putIfAbsent(id, 1));
                    
                    return assetIdMap;
                });
        // 结果存储
        Map<YearMonth,Map<YearMonth, List<String>>> dateToAssetIdsMap = new ConcurrentHashMap<>();

        // 组合两个Future并处理结果
        CompletableFuture<Void> combinedFuture = assetIdDayFrequencyMapFuture.thenCombine(
                assetIdGeneratedAtMapFuture,
                (assetIdFrequencyMap, assetIdGeneratedAtMap) -> {
                    // 将assetIdDayFrequencyMap赋值给assetIdToDayFrequencyMap
                    assetIdToFrequencyMap.clear();
                    assetIdToFrequencyMap.putAll(assetIdFrequencyMap);

                    assetIdFrequencyMap.forEach((assetId, frequency) -> {
                        LocalDateTime generatedAt = assetIdGeneratedAtMap.get(assetId);
                        YearMonth lastGeneratedAt = (generatedAt != null) ? YearMonth.from(generatedAt) : null;

                        // 计算下次生成日期
                        YearMonth nextGeneratedDate = (lastGeneratedAt != null) ?
                                lastGeneratedAt.plusMonths(frequency) :
                                yearMonth;

                        // 计算上次生成日期
                        YearMonth lastGeneratedDate = (lastGeneratedAt != null) ?
                                lastGeneratedAt :
                                yearMonth.minusMonths(frequency);

                        // 检查是否需要在给定日期生成
                        while (nextGeneratedDate.isBefore(yearMonth) || nextGeneratedDate.equals(yearMonth)) {
                            dateToAssetIdsMap.computeIfAbsent(nextGeneratedDate, k -> new ConcurrentHashMap<>())
                                    .computeIfAbsent(lastGeneratedDate, k -> Collections.synchronizedList(new ArrayList<>()))
                                    .add(assetId);

                            lastGeneratedDate = nextGeneratedDate;
                            nextGeneratedDate = nextGeneratedDate.plusMonths(frequency);
                        }

                        // 检查是否需要在给定日期生成
//                        if (nextGeneratedDate.equals(yearMonth)) {
//                            dateToAssetIdsMap.computeIfAbsent(lastGeneratedDate, k ->
//                                    Collections.synchronizedList(new ArrayList<>())).add(assetId);
//                        }
                    });
                    return null;
                }
        );

        // 处理异常
        combinedFuture.exceptionally(e -> {
            e.printStackTrace();
            return null;
        }).join(); // 等待所有任务完成

        // 将ConcurrentHashMap转换为普通的HashMap以返回
        return new HashMap<>(dateToAssetIdsMap);
    }


    public Integer insertOrUpdateBatch(List<PatrolInspectionLast> entities){
        return baseMapper.insertOrUpdateBatch(entities);
    }




}

