package com.ruoyi.patroltidb.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheck;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheckDetail;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.patroltidb.service.PatrolTunnelCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolTunnelCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (PatrolTunnelCheck)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-25 09:48:21
 */

@RestController
@Api(tags = "隧道巡检查记录")
@Slf4j
@RequestMapping("/patrolTunnelCheck")
public class PatrolTunnelCheckController extends BaseController {
    @Resource
    private PatrolTunnelCheckService patrolTunnelCheckService;

    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;

    @Resource
    private PatrolTunnelCheckDetailService patrolTunnelCheckDetailService;

    /**
     * 分页查询所有数据
     *
     * @param page              分页对象
     * @param patrolTunnelCheck 查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    //@RequiresPermissions("patrol:assetCheck:list")
    @RequestMapping(value = "/selectAll", method = RequestMethod.POST)
    public AjaxResult selectAll(Page<PatrolTunnelCheck> page, @RequestBody PatrolTunnelCheck patrolTunnelCheck, Integer pageNum, Integer pageSize) {
        if (pageNum != null && pageSize != null) {
            page.setCurrent(pageNum);
            page.setSize(pageSize);
        }
        return success(this.patrolTunnelCheckService.page(page, new QueryWrapper<>(patrolTunnelCheck) {{
            this.in(patrolTunnelCheck.getStatusList() != null, "status", patrolTunnelCheck.getStatusList()).
                    ge(patrolTunnelCheck.getCheckStartTime() != null, "check_time", patrolTunnelCheck.getCheckStartTime()).
                    le(patrolTunnelCheck.getCheckEndTime() != null, "check_time", patrolTunnelCheck.getCheckEndTime()).orderByDesc("check_time");
        }}));
    }


    /**
     * @param type
     * @param year
     * @return
     */
    @ApiOperation("查询所有数据(带是否异常)")
    //@RequiresPermissions("patrol:assetCheck:list")
    @RequestMapping(value = "/selectWithResult", method = RequestMethod.GET)
    public AjaxResult selectResult(@RequestParam() String tunnelId, @ApiParam("巡查类型：'5': '隧道日常巡查','6': '隧道经常检查'") @RequestParam() String type, @RequestParam String year) {

        List<PatrolTunnelCheck> list = this.patrolTunnelCheckService.list(new QueryWrapper<>() {{
            this.eq(tunnelId != null, "asset_id", tunnelId)
                    .eq(type != null, "type", type)
                    .like(year != null, "check_time", year)
                    .orderByDesc("check_time");
        }});

        for (PatrolTunnelCheck check : list) {

            QueryWrapper<PatrolTunnelCheckDetail> qw = new QueryWrapper<>();
            qw.eq("check_id", check.getId());
            List<PatrolTunnelCheckDetail> detailList = patrolTunnelCheckDetailService.list(qw);

            List<PatrolTunnelCheckDetail> badDetailList = detailList.stream().filter(i -> (!i.getDefect().equals("未见异常")) && (!i.getDefect().equals("/"))).collect(Collectors.toList());

            check.setIsException(badDetailList.size() > 0);
        }

        return success(list);
    }


    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @RequestMapping(value = "/selectOne/{id}", method = RequestMethod.GET)
    public AjaxResult selectOne(@PathVariable Serializable id) {
        return success(this.patrolTunnelCheckService.getById(id));
    }

    @ApiOperation("通过主键查询单条数据")
    @RequestMapping(value = "/selectRequestOne", method = RequestMethod.GET)
    public AjaxResult selectRequestOne(@RequestParam String id) {
        PatrolTunnelCheck patrolTunnelCheck = this.patrolTunnelCheckService.getPatrolTunnelCheckById(id);
        try {
            patrolAssetCheckService.setStack(patrolTunnelCheck);
            patrolAssetCheckService.setSignUrl(patrolTunnelCheck);
        } catch (Exception e) {
            log.error("获取签名图片失败", e);
        }
        return success(patrolTunnelCheck);
    }

    /**
     * 新增数据
     *
     * @param patrolTunnelCheck 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public AjaxResult insert(@RequestBody PatrolTunnelCheck patrolTunnelCheck) {
        if(patrolTunnelCheck.getAssetId() == null){
            return error("资产id不能为空");
        }
        return success(this.patrolTunnelCheckService.save(patrolTunnelCheck));
    }

    /**
     * 修改数据
     *
     * @param patrolTunnelCheck 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public AjaxResult update(@RequestBody PatrolTunnelCheck patrolTunnelCheck) {
        return success(this.patrolTunnelCheckService.updateById(patrolTunnelCheck));
    }

    /**
     * 删除数据
     *
     * @param idList 主键集合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public AjaxResult delete(@RequestBody List<String> idList) {
        return success(this.patrolTunnelCheckService.removeByIds(idList));
    }

    /**
     * 查询数据列表
     *
     * @param patrolTunnelCheck 查询实体
     * @return 数据列表
     */
    @ApiOperation("查询数据列表")
    @RequestMapping(value = "/getListByEntity", method = RequestMethod.POST)
    public AjaxResult getListByEntity(@RequestBody PatrolTunnelCheck patrolTunnelCheck) {
        return success(this.patrolTunnelCheckService.list(new QueryWrapper<>(patrolTunnelCheck)));
    }
}

