package com.ruoyi.other.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.other.domain.OtherTransportation;
import com.ruoyi.other.domain.dto.OtherTransportationDTO;
import com.ruoyi.other.mapper.OtherTransportationMapper;
import org.springframework.stereotype.Service;
import com.ruoyi.other.service.OtherTransportationService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 大件运输Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class OtherTransportationServiceImpl extends ServiceImpl<OtherTransportationMapper, OtherTransportation> implements OtherTransportationService {
    @Resource
    private OtherTransportationMapper otherTransportationMapper;

    @Override
    public List<OtherTransportation> findListByParam(Map<String, Object> params) {
        return otherTransportationMapper.findListByParam(params);
    }
    @Override
    public List<OtherTransportationDTO> findAll(Map<String, Object> params) {
        return otherTransportationMapper.findAll(params);
    }


//    @Override
//    public List<OtherTransportationDTO> findByTransportationIds(List<String> transportationIds) {
//        return otherTransportationMapper.findByTransportationIds(transportationIds);
//    }
    @Override
    public List<OtherTransportationDTO> findByTransportationIds(Map<String, Object> params) {
        return otherTransportationMapper.findByTransportationIds(params);
    }

//    @Override
//    public List<OtherTransportationDTO> findByTransportationIds(List<String> transportationIds) {
//        return otherTransportationMapper.findByTransportationIds(transportationIds);
//    }
}
