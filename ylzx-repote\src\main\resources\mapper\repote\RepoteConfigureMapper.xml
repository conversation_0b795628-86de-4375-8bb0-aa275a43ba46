<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repote.mapper.RepoteConfigureMapper">

    <resultMap type="com.ruoyi.repote.domain.RepoteConfigure" id="RepoteConfigureResult">
            <result property="formId" column="form_id"/>
            <result property="configureName" column="configure_name"/>
            <result property="configureType" column="configure_type"/>
    </resultMap>

    <sql id="base_column">
 form_id, configure_name, configure_type    </sql>

    <sql id="where_column">
        <if test="formId != null and formId != ''">
            AND form_id = #{formId}
        </if>
        <if test="formIdLike != null and formIdLike != ''">
            AND form_id like CONCAT('%', #{formIdLike}, '%')
        </if>
        <if test="configureName != null and configureName != ''">
            AND configure_name = #{configureName}
        </if>
        <if test="configureType != null and configureType != ''">
            AND configure_type = #{configureType}
        </if>
    </sql>

    <sql id="set_column">
            <if test="formId != null">
                form_id = #{formId},
            </if>
            <if test="configureName != null">
                configure_name = #{configureName},
            </if>
            <if test="configureType != null">
                configure_type = #{configureType},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="RepoteConfigureResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_configure
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="RepoteConfigureResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_configure
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

</mapper>