package com.ruoyi.patrol.service.handler.impl;

import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ruoyi.manage.api.domain.RemoteRoadDiseaseResponse;
import com.ruoyi.patrol.domain.PatrolInspectionLogs;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;
import java.math.BigDecimal;

/**
 * "日常巡查记录表(公路)"生成处理器。
 * 继承自抽象处理器以重用通用的Excel生成逻辑。
 */
@Slf4j
public class DailyPatrolExcelReportHandler extends AbstractExcelReportHandler<PatrolInspectionLogs> {

    private final List<PatrolInspectionLogs> reportDataList;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    final float MIN_ROW_HEIGHT = 18.0f; // 默认最小行高（以点为单位）

    // --- 此报表的特定样式定义 ---
    // 继承的样式：titleStyle, headerLabelStyle, headerValueStyle, itemHeaderStyle, itemCellStyle, itemFirstColStyle
    private CellStyle itemContentStyle;       // 内容样式 (居中) - 基于itemCellStyle
    private CellStyle itemRemarkStyle;        // 说明样式 (左对齐) - 基于itemCellStyle
    private CellStyle footerLabelStyle;       // 页脚标签样式 (无边框)
    private CellStyle footerValueStyle;       // 页脚值样式 (无边框)
    private CellStyle normalBorderStyle;      // 仅用于细边框的样式
    private CellStyle headerLabelNoBorderStyle; // 不带边框的标题标签样式
    private CellStyle headerValueNoBorderStyle; // 不带边框的标题值样式

    private CellStyle noBorderStyle;      // 无边框左对齐自动换行样式

    public DailyPatrolExcelReportHandler(List<PatrolInspectionLogs> reportDataList, Map<String, String> signUrlMap) {
        this.reportDataList = reportDataList != null ? reportDataList : new ArrayList<>();
        if (signUrlMap != null) {
            this.signUrlMap.putAll(signUrlMap); // 填充继承的映射
        }
    }

    /**
     * 重写createStyles方法，为此报表类型添加特定样式
     * 并初始化基类中的通用样式。
     */
    @Override
    protected void createStyles(Workbook workbook) {
        super.createStyles(workbook); // 首先初始化通用样式

        // --- 通用边框样式 ---
        normalBorderStyle = workbook.createCellStyle();
        setThinBorders(normalBorderStyle); // 使用继承的辅助方法

        // 无边框左对齐自动换行样式
        noBorderStyle = workbook.createCellStyle();
        noBorderStyle.setBorderTop(BorderStyle.NONE);
        noBorderStyle.setBorderBottom(BorderStyle.NONE);
        noBorderStyle.setBorderLeft(BorderStyle.NONE);
        noBorderStyle.setBorderRight(BorderStyle.NONE);
        noBorderStyle.setAlignment(HorizontalAlignment.LEFT);
        noBorderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        noBorderStyle.setWrapText(true);

        // --- 如需要，重新初始化/确保继承样式上的边框 ---
        // 克隆基类样式并应用边框，以确保此报告中的所有内容都有边框
        // 标题标签样式
        CellStyle baseHeaderLabelStyle = super.headerLabelStyle;
        headerLabelStyle = workbook.createCellStyle();
        headerLabelStyle.cloneStyleFrom(baseHeaderLabelStyle);
        copyBorders(normalBorderStyle, headerLabelStyle); // 应用边框

        // 标题值样式
        CellStyle baseHeaderValueStyle = super.headerValueStyle;
        headerValueStyle = workbook.createCellStyle();
        headerValueStyle.cloneStyleFrom(baseHeaderValueStyle);
        copyBorders(normalBorderStyle, headerValueStyle);
        
        // 创建不带边框的样式版本
        headerLabelNoBorderStyle = workbook.createCellStyle();
        headerLabelNoBorderStyle.cloneStyleFrom(baseHeaderLabelStyle);
        // 不调用copyBorders，保持无边框状态
        
        headerValueNoBorderStyle = workbook.createCellStyle();
        headerValueNoBorderStyle.cloneStyleFrom(baseHeaderValueStyle);
        // 不调用copyBorders，保持无边框状态

        // 项目表头样式
        CellStyle baseItemHeaderStyle = super.itemHeaderStyle;
        itemHeaderStyle = workbook.createCellStyle();
        itemHeaderStyle.cloneStyleFrom(baseItemHeaderStyle);
        copyBorders(normalBorderStyle, itemHeaderStyle); // 应用边框

        // 项目单元格样式 (通用居中)
        CellStyle baseItemCellStyle = super.itemCellStyle;
        itemCellStyle = workbook.createCellStyle();
        itemCellStyle.cloneStyleFrom(baseItemCellStyle);
        copyBorders(normalBorderStyle, itemCellStyle);

        // 项目第一列样式 (左对齐)
        CellStyle baseItemFirstColStyle = super.itemFirstColStyle;
        itemFirstColStyle = workbook.createCellStyle();
        itemFirstColStyle.cloneStyleFrom(baseItemFirstColStyle);
        copyBorders(normalBorderStyle, itemFirstColStyle); // 应用边框

        // --- 新的/特定样式 ---
        
        // 内容样式 (基于itemCellStyle，已居中带边框)
        itemContentStyle = workbook.createCellStyle();
        itemContentStyle.cloneStyleFrom(itemCellStyle);
        itemContentStyle.setWrapText(true);

        // 说明样式 (左对齐，带边框)
        itemRemarkStyle = workbook.createCellStyle();
        itemRemarkStyle.cloneStyleFrom(itemCellStyle);
        itemRemarkStyle.setAlignment(HorizontalAlignment.LEFT); // 左对齐
        itemRemarkStyle.setWrapText(true);

        // 页脚标签样式 (继承headerLabelNoBorderStyle对齐方式，无边框)
        footerLabelStyle = workbook.createCellStyle();
        footerLabelStyle.cloneStyleFrom(headerLabelNoBorderStyle);
        footerLabelStyle.setWrapText(true);

        // 页脚值样式 (继承headerValueNoBorderStyle对齐方式，无边框)
        footerValueStyle = workbook.createCellStyle();
        footerValueStyle.cloneStyleFrom(headerValueNoBorderStyle);
        footerValueStyle.setWrapText(true);
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        super.beforeSheetCreate(writeWorkbookHolder, writeSheetHolder); // 通过createStyles重写创建样式
        // 在生成表格内容前预加载签名
        if (!signUrlMap.isEmpty()) {
            preloadSignImages(); // 调用继承的预加载器
        }
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        
        // 诊断日志记录
        log.info("开始生成日常巡查记录表...");
        if (signUrlMap.isEmpty()) {
            log.warn("签名URL映射为空。签名可能不会出现。");
        } else {
            log.info("签名URL映射包含 {} 条记录。", signUrlMap.size());
            log.info("预加载的签名图像缓存包含 {} 条记录。", signImageMap.size());
            if (!failedSignIds.isEmpty()) {
                log.warn("有 {} 个签名下载失败。ID列表: {}", failedSignIds.size(), failedSignIds);
            }
        }

        // --- 设置列宽 ---
        sheet.setColumnWidth(0, 10 * 256);  // A列 (方向)
        sheet.setColumnWidth(1, 12 * 256);  // B列 (桩号)
        sheet.setColumnWidth(2, 12 * 256);  // C列 (缺陷项目)
        sheet.setColumnWidth(3, 12 * 256);  // D列 (事件名称)
        sheet.setColumnWidth(4, 10 * 256);  // E列 (单位)
        sheet.setColumnWidth(5, 10 * 256);  // F列 (数量)
        sheet.setColumnWidth(6, 16 * 256);  // G列 (说明)

        // 存储列宽信息用于行高调整
        int[] columnWidths = new int[] {10, 12, 12, 12, 10, 10, 16};

        if (reportDataList == null || reportDataList.isEmpty()) {
            log.warn("报告数据列表为空。生成空表格。");
            Row titleRow = sheet.createRow(0);
            titleRow.setHeightInPoints(30);
            createCell(titleRow, 0, "日常巡查记录表(公路) (无数据)", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));
            return; // 没有数据则停止处理
        }

        int currentRowIndex = 0;
        
        // 循环处理每条报告记录，为每条记录生成一张卡片
        for (int i = 0; i < reportDataList.size(); i++) {
            PatrolInspectionLogs report = reportDataList.get(i);
            if (report == null) {
                log.warn("跳过索引 {} 处的null巡查记录", i);
                continue;
            }
            
            log.debug("正在为巡查记录生成报告部分: {}", report.getId());
            
            // 1. 标题行
            Row titleRow = sheet.createRow(currentRowIndex);
            titleRow.setHeightInPoints(40);
            createCell(titleRow, 0, "日常巡查记录表(公路)", titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            currentRowIndex++;

            // 2. 管理单位行
            Row unitRow = sheet.createRow(currentRowIndex);
            createCell(unitRow, 0, "管理单位：", headerLabelNoBorderStyle);
            createCell(unitRow, 1, Objects.toString(report.getMaintenanceUnitName(), ""), headerValueNoBorderStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 2));
            
            createCell(unitRow, 3, "路线编号：", headerLabelNoBorderStyle);
            createCell(unitRow, 4, Objects.toString(report.getRouteCode(), ""), headerValueNoBorderStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 6));
            
            setConditionalRowHeight(unitRow, 0, 6, columnWidths, 30);
            currentRowIndex++;

            // 3. 养护路段行
            Row sectionRow = sheet.createRow(currentRowIndex);
            createCell(sectionRow, 0, "养护路段：", headerLabelNoBorderStyle);
            createCell(sectionRow, 1, Objects.toString(report.getMaintenanceSectionName(), ""), headerValueNoBorderStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 2));

            createCell(sectionRow, 3, "天气：", headerLabelNoBorderStyle);
            // 将天气、日期和温度整合到一起
            String weatherInfo = Objects.toString(report.getWeather(), "");
            // 直接使用天气信息，不再组合温度
            createCell(sectionRow, 4, weatherInfo, headerValueNoBorderStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 4, 6));
            
            setConditionalRowHeight(sectionRow, 0, 6, columnWidths, 30);
            currentRowIndex++;

            // 4. 巡查时间行
            Row timeRow = sheet.createRow(currentRowIndex);
            createCell(timeRow, 0, "巡查时间：", headerLabelNoBorderStyle);
            String timeStr = "";
            if (report.getStartTime() != null) {
                timeStr = dateFormat.format(report.getStartTime());
            }
            createCell(timeRow, 1, timeStr, headerValueNoBorderStyle);
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 1, 6));
            
            setConditionalRowHeight(timeRow, 0, 6, columnWidths, 30);
            currentRowIndex++;

            // 5. 项目标题行
            Row itemHeaderRow = sheet.createRow(currentRowIndex);
            itemHeaderRow.setHeightInPoints(25);
            createCell(itemHeaderRow, 0, "方向", itemHeaderStyle);
            createCell(itemHeaderRow, 1, "桩号", itemHeaderStyle);
            createCell(itemHeaderRow, 2, "缺陷项目", itemHeaderStyle);
            createCell(itemHeaderRow, 3, "事件名称", itemHeaderStyle);
            createCell(itemHeaderRow, 4, "单位", itemHeaderStyle);
            createCell(itemHeaderRow, 5, "数量", itemHeaderStyle);
            createCell(itemHeaderRow, 6, "说明", itemHeaderStyle);
            
            applyRowBorder(itemHeaderRow, 0, 6, itemHeaderStyle);
            currentRowIndex++;

            // 6. 项目详情行 - 从getRoadDiseaseList获取子项
            List<RemoteRoadDiseaseResponse> diseaseList = report.getRoadDiseaseList();
            int itemRowCount = 0; // 记录已添加的行数
            
            // 有病害列表时填充详情
            if (diseaseList != null && !diseaseList.isEmpty()) {
                for (RemoteRoadDiseaseResponse disease : diseaseList) {
                    if (disease == null) continue;
                    
                    Row detailRow = sheet.createRow(currentRowIndex);
                    
                    // 方向
                    String directionStr = Objects.toString(disease.getDirection(), "");
                    String direction;
                    if ("0".equals(directionStr)) {
                        direction = "上行";
                    } else if ("1".equals(directionStr)) {
                        direction = "下行";
                    } else if ("2".equals(directionStr)) {
                        direction = "双向";
                    } else {
                        direction = ""; // 或者根据需要处理其他情况，例如保留 directionStr
                    }
                    createCell(detailRow, 0, direction, itemCellStyle);
                    
                    // 桩号 - 尝试获取开始和结束桩号
                    String stakeNo = "";
                    try {
                        Long beginMile = disease.getBeginMile();
                        Long endMile = disease.getEndMile();
                        if (beginMile != null && endMile != null) {
                            String beginStake = formatStake(new BigDecimal(beginMile), null);
                            String endStake = formatStake(new BigDecimal(endMile), null);
                            stakeNo = beginStake + "至" + endStake;
                        } else {
                            stakeNo = Objects.toString(disease.getMile(), "");
                        }
                    } catch (Exception e) {
                        stakeNo = Objects.toString(disease.getMile(), "");
                    }
                    createCell(detailRow, 1, stakeNo, itemCellStyle);
                    
                    // 缺陷项目
                    String defectItem = Objects.toString(disease.getAssetMainTypeName(), "");
                    createCell(detailRow, 2, defectItem, itemCellStyle);
                    
                    // 事件名称
                    String eventName = Objects.toString(disease.getDiseaseName(), "");
                    createCell(detailRow, 3, eventName, itemCellStyle);
                    
                    // 单位 - 暂时为空
                    createCell(detailRow, 4, "", itemCellStyle);
                    
                    // 数量 - 暂时为空
                    createCell(detailRow, 5, "", itemCellStyle);
                    
                    // 说明
                    String description = Objects.toString(disease.getDiseaseDesc(), "");
                    createCell(detailRow, 6, description, itemRemarkStyle);
                    
                    applyRowBorder(detailRow, 0, 6, normalBorderStyle);
                    setConditionalRowHeight(detailRow, 0, 6, columnWidths,35);
                    currentRowIndex++;
                    itemRowCount++;
                }
            }else {
                log.warn("病害列表为空。");
                Row detailRow = sheet.createRow(currentRowIndex);
                    
                    // 方向
                    String directionStr = Objects.toString(report.getDirection(), "");
                    String direction;
                    if ("0".equals(directionStr)) {
                        direction = "上行";
                    } else if ("1".equals(directionStr)) {
                        direction = "下行";
                    } else if ("2".equals(directionStr)) {
                        direction = "双向";
                    } else {
                        direction = ""; // 或者根据需要处理其他情况，例如保留 directionStr
                    }
                    createCell(detailRow, 0, direction, itemCellStyle);
                    
                    // 桩号 - 空着
                    createCell(detailRow, 1, "", itemCellStyle);
                    
                    createCell(detailRow, 2, "无", itemCellStyle);
                    
                    // 事件名称
                    createCell(detailRow, 3, "", itemCellStyle);
                    
                    // 单位 - 暂时为空
                    createCell(detailRow, 4, "", itemCellStyle);
                    
                    // 数量 - 暂时为空
                    createCell(detailRow, 5, "", itemCellStyle);
                    
                    // 说明
                    createCell(detailRow, 6, "未见异常", itemRemarkStyle);
                    
                    applyRowBorder(detailRow, 0, 6, normalBorderStyle);
                    setConditionalRowHeight(detailRow, 0, 6, columnWidths,35);
                    currentRowIndex++;
                    itemRowCount++;
            }
            
            // 如果行数不足10行，添加空白行补足
            while (itemRowCount < 10) {
                Row emptyRow = sheet.createRow(currentRowIndex);
                for (int col = 0; col <= 6; col++) {
                    createCell(emptyRow, col, "", itemCellStyle);
                }
                applyRowBorder(emptyRow, 0, 6, normalBorderStyle);
                setConditionalRowHeight(emptyRow, 0, 6, columnWidths,35);
                currentRowIndex++;
                itemRowCount++;
            }
            
            // 7. 添加巡查内容说明行
            Row inspectionContentRow = sheet.createRow(currentRowIndex);
            Cell inspectionContentCell = createCell(inspectionContentRow, 0, 
                    Objects.toString(report.getContent(), "对路面、桥梁、隧道、路基、绿化、交通工程等进行日常巡查"), footerValueStyle);
            // 设置单元格样式为左对齐
            CellStyle noteStyle = sheet.getWorkbook().createCellStyle();
            noteStyle.cloneStyleFrom(footerValueStyle);
            noteStyle.setAlignment(HorizontalAlignment.LEFT);
            inspectionContentCell.setCellStyle(noteStyle);
            
            // 合并A-G列
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            applyRowBorder(inspectionContentRow, 0, 6, normalBorderStyle);
            setConditionalRowHeight(inspectionContentRow, 0, 6, columnWidths,30);
            
            currentRowIndex++;

            // 2. 添加空白行防止签名越界
            Row blankRow1 = sheet.createRow(currentRowIndex);
            createCell(blankRow1, 1, "", noBorderStyle); // 合并创建空单元格
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 6));
            blankRow1.setHeightInPoints(5);
            currentRowIndex++;
            // 8. 记录人签名行
            Row recorderRow = sheet.createRow(currentRowIndex);
            recorderRow.setHeightInPoints(25);
            createCell(recorderRow, 0, "记录人（巡查人）：", footerLabelStyle);
            
            // 合并A-B列
            sheet.addMergedRegion(new CellRangeAddress(currentRowIndex, currentRowIndex, 0, 1));
            
            // 获取巡查人姓名
            String inspectorNames = Objects.toString(report.getUserNames(), "");
            // 如果有签名ID，添加签名图片
            List<String> signIdList = report.getSignNameList();
            // 添加签名图片（水平方向，只显示第一个有效图片）
            currentRowIndex = addSignatureImages(sheet, currentRowIndex, 2, 6, signIdList,
                    inspectorNames, footerValueStyle, false);
            currentRowIndex++;
            
            // 在下一个卡片之前插入分页符（如果不是最后一个）
            if (i < reportDataList.size() - 1) {
                sheet.setRowBreak(currentRowIndex - 1); // 在当前卡片的最后一行之后设置分页符
                log.debug("已在行 {} 之后插入分页符", currentRowIndex - 1);
            }
        }
        
        log.info("日常巡查记录表生成完成，共 {} 行。", currentRowIndex);
    }
}