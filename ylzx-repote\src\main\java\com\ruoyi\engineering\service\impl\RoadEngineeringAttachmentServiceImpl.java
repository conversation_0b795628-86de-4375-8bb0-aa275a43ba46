package com.ruoyi.engineering.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.engineering.domain.RoadEngineeringAttachment;
import com.ruoyi.engineering.mapper.RoadEngineeringAttachmentMapper;
import com.ruoyi.engineering.service.RoadEngineeringAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 涉路工程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@Service
public class RoadEngineeringAttachmentServiceImpl extends ServiceImpl<RoadEngineeringAttachmentMapper, RoadEngineeringAttachment> implements RoadEngineeringAttachmentService {

    @Autowired
    private RoadEngineeringAttachmentMapper roadEngineeringAttachmentMapper;

    @Override
    public List<RoadEngineeringAttachment> findListByParam(Map<String, Object> params) {
        return roadEngineeringAttachmentMapper.findListByParam(params);
    }


}
