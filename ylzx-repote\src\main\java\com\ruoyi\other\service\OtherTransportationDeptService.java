package com.ruoyi.other.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.other.domain.OtherTransportation;
import com.ruoyi.other.domain.OtherTransportationDept;
import com.ruoyi.other.domain.dto.OtherTransportationDTO;

import java.util.List;
import java.util.Map;


/**
 * 大件运输Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface OtherTransportationDeptService extends IService<OtherTransportationDept> {

    /**
     * 根据条件查询大件运输列表
     * @param params
     */
    List<OtherTransportationDept> findListByParam(Map<String, Object> params);

    /**
     * 根据ID查询大件运输管理处ID
     * @ ID
     */
    List<String> getManagementDepartmentIdsByTransportationId(String id);


    boolean updateDepartmentByTransportationId(String transportationId, String deptId, String deptName);

    boolean removeByTransportationId(String transportationId);

    List<String> getTransportationList(List<String> userDeptIdList);



}
