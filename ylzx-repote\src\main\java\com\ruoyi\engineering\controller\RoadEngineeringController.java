package com.ruoyi.engineering.controller;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.engineering.domain.dto.RoadEngineeringDTO;
import com.ruoyi.system.api.RemoteMaintenanceSectionService;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.engineering.domain.RoadEngineering;
import com.ruoyi.engineering.service.RoadEngineeringService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 涉路工程Controller
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@Api(tags = "涉路工程" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/engineering")
public class RoadEngineeringController extends BaseController {
    final private RoadEngineeringService roadEngineeringService;

    private RemoteMaintenanceSectionService remoteMaintenanceSectionService;
    /**
     * 查询涉路工程列表(分页)
     */
    @ApiOperation("查询涉路工程列表")
//    @RequiresPermissions("repote:engineering:list")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> params)throws ExecutionException, InterruptedException {
        QueryWrapper<RoadEngineering> qw = new QueryWrapper<>();

        qw.orderByDesc("create_time");
        R<List<String>> r = remoteMaintenanceSectionService.findUserMaintenanceIds(null);
        if (r.getCode() != 200) {
            return getDataTable(new ArrayList<>());
        }
        params.put("sectionIdList", r.getData());
        params.put("order", "create_time desc");
        startPage();
        List<RoadEngineering> list = roadEngineeringService.findListByParam(params);
        System.out.printf("list:::"+list);
        return getDataTable(list);
    }

    /**
     * 查询涉路工程列表(不分页)
     */
    @ApiOperation("查询涉路工程列表(不分页)")
    @RequiresPermissions("repote:engineering:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll(@RequestParam Map<String, Object> params) {
        QueryWrapper<RoadEngineering> qw = new QueryWrapper<>();

        qw.orderByDesc("create_time");
        List<RoadEngineering> list = roadEngineeringService.list(qw);
        return success(list);
    }
    /**
     * 根据id查询涉路工程数据
     */
    @ApiOperation("根据id查询涉路工程数据")
//    @RequiresPermissions("repote:engineering:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            RoadEngineering roadEngineering = roadEngineeringService.getById(id);
        if (roadEngineering == null) {
            return error("未查询到【涉路工程】记录");
        }

        return success(roadEngineering);
    }

    /**
     * 新增涉路工程
     */
    @ApiOperation("新增涉路工程")
    @RequiresPermissions("repote:engineering:add")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody RoadEngineering roadEngineering) {
        System.out.printf("roadEngineering:::"+ roadEngineering);

        roadEngineeringService.save(roadEngineering);
        String id = roadEngineering.getId();
        System.out.printf("id:::"+ id);
        return AjaxResult.success("操作成功",id);
    }

    /**
     * 修改涉路工程
     */
    @ApiOperation("修改涉路工程")
    @RequiresPermissions("repote:engineering:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody RoadEngineering roadEngineering) {
        return toAjax(roadEngineeringService.updateById(roadEngineering));
    }

    /**
     * 删除涉路工程
     */
    @ApiOperation("删除涉路工程")
    @RequiresPermissions("repote:engineering:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(roadEngineeringService.removeById(id));
    }

    /**
     * 导出涉路工程列表
     */
    @ApiOperation("导出涉路工程列表")
    @RequiresPermissions("repote:engineering:export")
    @Log(title = "涉路工程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<RoadEngineering> list = roadEngineeringService.list();
        ExcelUtil<RoadEngineering> util = new ExcelUtil<RoadEngineering>(RoadEngineering.class);
        util.exportExcel(response, list, "涉路工程数据");
    }

    /**
     * 查询大件运输列表连着管理处和路段
     */
    @ApiOperation("查询涉路工程列表")
    @GetMapping("/AllList")
    public TableDataInfo AllList(@RequestParam Map<String, Object> params) {
        startPage();
        List<RoadEngineeringDTO> list = roadEngineeringService.findAll(params);

        return getDataTable(list);
    }

    /**
     * 新增涉路工程
     */
    @ApiOperation("根据用户权限去查询涉路工程列表")
//    @RequiresPermissions("repote:engineering:list")
    @PostMapping("/getUserPermissions")
    public TableDataInfo getUserPermissions(@RequestBody Map<String, Object> params) {
        params.put("order", "create_time desc");

        int pageNum = (int) params.getOrDefault("pageNum", 1); // 默认第一页
        int pageSize = (int) params.getOrDefault("pageSize", 10); // 默认每页20条

        // 设置分页
        PageHelper.startPage(pageNum, pageSize);// 如果你使用的是 PageHelper
        List<RoadEngineering> list = roadEngineeringService.getUserPermissions(params);
        return getDataTable(list);
    }
}
