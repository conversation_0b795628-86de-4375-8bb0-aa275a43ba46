package com.ruoyi.patrol.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PatrolDiseaseDTO {
    String id;
    Integer diseaseNum;
    LocalDateTime expiry;

    // 构造函数
    public PatrolDiseaseDTO(String id, Integer diseaseNum) {
        this.id = id;
        this.diseaseNum = diseaseNum;
    }
    public PatrolDiseaseDTO(String id, LocalDateTime expiry) {
        this.id = id;
        this.expiry = expiry;
    }


}
