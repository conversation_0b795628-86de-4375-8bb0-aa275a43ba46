package com.ruoyi.patroltidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheck;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheckDetail;
import com.ruoyi.patroltidb.domain.PatrolDeviceFaultRecord;
import com.ruoyi.patroltidb.domain.excel.DeviceMonthlyReportExport;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 隧道机电日常巡查Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
public interface PatrolDeviceCheckService extends IService<PatrolDeviceCheck> {

    List<PatrolDeviceCheck> findListByParam(Map<String, Object> params);

    /**
     * 根据条件查询隧道机电日常巡查数据列表
     * @param params
     */
    List<PatrolDeviceCheckDetail> findDetailListByParam(Map<String, Object> params);

    List<PatrolDeviceCheckDetail> findDetailListByParam(Map<String, Object> params, Map<String, String> signUrlMap);

    /**
     * 根据条件查询隧道机电故障上报数据列表 fault_record
     * @param params
     */
    List<PatrolDeviceFaultRecord> findFaultListByParam(Map<String, Object> params);

    List<PatrolDeviceFaultRecord> findFaultListByParam(Map<String, Object> params, Map<String, String> signUrlMap);

    Long findFaultListByParamCount(Map<String, Object> params);

    Long findDetailListByParamCount(Map<String, Object> params);

    List<DeviceMonthlyReportExport> listMonthlyReport(Map<String, Object> params);

    List<PatrolDeviceFaultRecord> getByAssetIdFault(Map<String, Object> params);

    @Transactional(rollbackFor = Exception.class)
    void batchAddAll(PatrolDeviceCheck patrolDeviceCheck);
}
