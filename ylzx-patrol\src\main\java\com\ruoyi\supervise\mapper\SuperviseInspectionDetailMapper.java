package com.ruoyi.supervise.mapper;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.supervise.domain.SuperviseInspectionDetail;

/**
 * 督查详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface SuperviseInspectionDetailMapper extends BaseMapper<SuperviseInspectionDetail> {

    List<SuperviseInspectionDetail> findListByParam(Map<String, Object> params);


}
