package com.ruoyi.patrol.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: sfc
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class InspectionStatsNewVO {
    @ApiModelProperty(value = "路面计划巡查里程(本日)")
    private String totalMileage = "0";      // 总巡查里程
    @ApiModelProperty(value = "路面已巡查里程(本日) ")
    private String inspectedMileage = "0";  // 已巡查里程
    @ApiModelProperty(value = "待巡查里程")
    private String pendingMileage = "0";    // 待巡查里程
    @ApiModelProperty(value = "计划巡查桥梁数")
    private Integer bridgeTotalCount = 0; // 计划巡查桥梁数
    @ApiModelProperty(value = "已巡查桥梁数")
    private Integer bridgeInspectedCount = 0; // 已巡查桥梁数
    @ApiModelProperty(value = "待巡查桥梁数")
    private Integer bridgePendingCount = 0; // 待巡查桥梁数
    @ApiModelProperty(value = "桥梁巡查")
    private String bridgeInspections = "0/0";    // 桥梁巡查
    @ApiModelProperty(value = "计划巡查涵洞数")
    private Integer culvertTotalCount = 0; // 计划巡查涵洞数
    @ApiModelProperty(value = "已巡查涵洞数")
    private Integer culvertInspectedCount = 0; // 已巡查涵洞数
    @ApiModelProperty(value = "待巡查涵洞数")
    private Integer culvertPendingCount = 0; // 待巡查涵洞数
    @ApiModelProperty(value = "涵洞巡查")
    private String culvertInspections = "0/0";   // 涵洞巡查
    @ApiModelProperty(value = "计划巡查隧道数")
    private Integer tunnelTotalCount = 0; // 计划巡查隧道数
    @ApiModelProperty(value = "已巡查隧道数")
    private Integer tunnelInspectedCount = 0; // 已巡查隧道数
    @ApiModelProperty(value = "待巡查隧道数")
    private Integer tunnelPendingCount = 0; // 待巡查隧道数
    @ApiModelProperty(value = "隧道巡查")
    private String tunnelInspections = "0/0";    // 隧道巡查
    @ApiModelProperty(value = "已巡查路网数")
    private Integer routeInspectedCount = 0; // 已巡查路网数
    @ApiModelProperty(value = "待巡查路网数")
    private Integer routePendingCount = 0; // 待巡查路网数
    @ApiModelProperty(value = "路网巡查")
    private String routeInspections = "0/0";   // 路网巡查
    @ApiModelProperty(value = "管养处ID")
    private String managementMaintenanceId; // 管理处
    @ApiModelProperty(value = "管理处Name")
    private String managementMaintenanceName; // 管理处
    @ApiModelProperty(value = "前一个时间已巡查次数")
    private Integer lastInspectedCount = 0; // 前一个时间已巡查次数
    @ApiModelProperty(value = "后一个时间已巡查次数")
    private Integer nextInspectedCount = 0; // 后一个时间已巡查次数
    @ApiModelProperty(value = "日常检查次数")
    private Integer dailyCount = 0; // 日常检查次数
    @ApiModelProperty(value = "经常检查次数")
    private Integer oftenCount = 0; // 经常检查次数
    // 在 InspectionStatsVO 类中添加方法:
    public Integer getTotalInspectedCount() {
        return this.bridgeInspectedCount + this.tunnelInspectedCount + this.routeInspectedCount;
    }
    public Integer getTotalPendingCount() {
        return this.bridgePendingCount + this.tunnelPendingCount + this.routePendingCount;
    }
    public Integer getTotalCount() {
        return this.getTotalInspectedCount() + this.getTotalPendingCount();
    }
}
