package com.ruoyi.patrol.mapstruct;

import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.manage.api.domain.bridge.BridgeStaticResponse;
import com.ruoyi.manage.api.domain.culvert.BaseCulvertResponse;
import com.ruoyi.manage.api.domain.tunnel.BaseTunnelResponse;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseCulvertResponseCache;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * MapStruct映射器，用于将BaseDataDomain对象转换为各类缓存对象
 */
@Mapper
public interface BaseDataToCacheMapper {
    BaseDataToCacheMapper INSTANCE = Mappers.getMapper(BaseDataToCacheMapper.class);

    /**
     * 将BaseDataDomain转换为对应的缓存对象
     * @param baseDataDomain
     * @return
     */
    BaseDataCache toBaseDataCache(BaseDataDomain baseDataDomain);
    
    /**
     * BaseDataDomain转换为桥梁基础数据缓存
     */
    BaseBridgeResponseCache toBaseBridgeCache(BaseDataDomain baseDataDomain);

    /**
     * BaseDataDomain转换为涵洞基础数据缓存
     */
    BaseCulvertResponseCache toBaseCulvertCache(BaseDataDomain baseDataDomain);

    /**
     * BaseDataDomain转换为隧道基础数据缓存
     */
    BaseTunnelResponseCache toBaseTunnelCache(BaseDataDomain baseDataDomain);

    /**
     * BridgeStaticResponse装换为BaseBridgeResponseCache
     */
    BaseBridgeResponseCache toBaseBridgeResponseCache(BridgeStaticResponse bridgeStaticResponse);


    /**
     * BaseCulvertResponse装换为BaseCulvertResponseCache
     */
    @Mapping(target = "assetName", source = "culvertType")
    BaseCulvertResponseCache toBaseCulvertResponseCache(BaseCulvertResponse baseCulvertResponse);

    /**
     * 将BaseTunnelResponse装换为BaseTunnelResponseCache
     */
    BaseTunnelResponseCache toBaseTunnelResponseCache(BaseTunnelResponse baseTunnelResponse);
} 