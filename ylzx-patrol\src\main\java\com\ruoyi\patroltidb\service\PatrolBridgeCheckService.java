package com.ruoyi.patroltidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.manage.api.domain.bridge.BridgeStaticResponse;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheck;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 桥梁巡检查记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface PatrolBridgeCheckService extends IService<PatrolBridgeCheck> {

    /**
     * 根据条件查询桥梁巡检查记录数据列表
     * @param params
     */
    List<PatrolBridgeCheck> findListByParam(Map<String, Object> params);

    MTableDataInfo<List<BridgeStaticResponse>> getListMTableDataInfo(Map<String, Object> params);

    /**
     * 通过id获取实体（不带子表）
     */
    PatrolBridgeCheck getPatrolBridgeCheckById(Serializable id);


    void extracted();

}
