package com.ruoyi.patroltidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheck;

import java.io.Serializable;

/**
 * 隧道巡检查主表Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface PatrolTunnelCheckService extends IService<PatrolTunnelCheck> {

    /**
     * 通过id获取实体（不带子表）
     */
    PatrolTunnelCheck getPatrolTunnelCheckById(Serializable id);


}
