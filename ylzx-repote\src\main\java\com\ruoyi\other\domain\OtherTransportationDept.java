package com.ruoyi.other.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 大件运输管理处对象 other_transportation_dept
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@ApiModel(value="大件运输管理处")
@TableName("other_transportation_dept")
@Data
public class OtherTransportationDept extends BaseTableEntity {
    private static final long serialVersionUID = 1L;

    /** 管理处ID */
    @Excel(name = "管理处ID")
    @ApiModelProperty(value = "管理处ID")
    private String deptId;

    /** 管理处名称 */
    @Excel(name = "管理处名称")
    @ApiModelProperty(value = "管理处名称")
    private String deptName;

    /** 大件运输ID */
    @Excel(name = "大件运输ID")
    @ApiModelProperty(value = "大件运输ID")
    private String transportationId;

}
