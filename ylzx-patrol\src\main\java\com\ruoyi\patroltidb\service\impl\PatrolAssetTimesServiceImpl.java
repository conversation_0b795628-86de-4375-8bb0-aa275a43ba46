package com.ruoyi.patroltidb.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolCheckBase;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.domain.dto.AssetTimesDTO;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.vo.BridgeTimesVO;
import com.ruoyi.patrol.domain.vo.CulvertTimesVO;
import com.ruoyi.patrol.domain.vo.TunnelTimesVO;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.enums.StageType;
import com.ruoyi.patrol.mapper.PatrolAssetCheckMapper;
import com.ruoyi.patrol.service.BaseCacheService;
import com.ruoyi.patrol.service.PatrolFrequencySettingsService;
import com.ruoyi.patrol.utils.AssetConvertUtils;
import com.ruoyi.patrol.utils.FrequencyUtils;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.patroltidb.service.PatrolAssetTimesService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * @Description:
 * @author: QD
 * @date: 2024年11月29日 17:20
 */
@Slf4j
@Slave
@Service
public class PatrolAssetTimesServiceImpl implements PatrolAssetTimesService {
    @Resource
    private BaseCacheService baseCacheService;
    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;
    @Resource
    private PatrolFrequencySettingsService patrolFrequencySettingsService;
    @Resource
    private PatrolAssetCheckMapper patrolAssetCheckMapper;

    // 添加资产类型到次数类的映射关系
    private static final Map<AssetType, Class<?>> ASSET_TIMES_MAP = new HashMap<>();

    static {
        ASSET_TIMES_MAP.put(AssetType.BRIDGE, BridgeTimesVO.class);
        ASSET_TIMES_MAP.put(AssetType.CULVERT, CulvertTimesVO.class);
        ASSET_TIMES_MAP.put(AssetType.TUNNEL, TunnelTimesVO.class);
    }

    // 自定义线程池，避免使用公共池
    private static final ExecutorService executorService = Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors(),
            new ThreadFactoryBuilder().setNameFormat("patrol-times-pool-%d").build()
    );

    public <T> List<T> getCurrentTimesVO(AssetBaseDataRequest request, Long pageNum, Long pageSize, AtomicInteger total) {
        if (request == null || request.getType() == null || request.getCheckTime() == null) {
            return Collections.emptyList();
        }
        // 获取已排序的检查记录列表
        List<PatrolAssetCheck> list;
        List<BaseDataCache> baseDataList = null;
        List<String> ids = null;
        Map<String, LocalDateTime> assetExpiryMap = new HashMap<>();
        try {
            baseCacheService.setDeptIds(request);
            if (pageNum == null || pageSize == null) {
                baseDataList = baseCacheService.selectBaseDataResponseByAssetIds(request,
                        null, null);
            } else {
                // 记录开始时间并查询缓存数据(带分页)
                baseDataList = baseCacheService.selectBaseDataResponseByAssetIds(request,
                        pageNum, pageSize);
                // 设置总记录数(从缓存服务获取)
                total.set(baseCacheService.getTotalCount(request));
                // 检查结果是否为空
                if (baseDataList == null || baseDataList.isEmpty()) {
                    return new ArrayList<>();
                }
                ids = baseDataList.stream().map(BaseDataCache::getAssetId).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(ids)) {
                    return new ArrayList<>();
                }
                request.setIds(ids);
            }
            String year = request.getCheckTime().format(DateTimeFormatter.ofPattern("yyyy"));
            list = patrolAssetCheckMapper.selectCheckedYear(request.getIds(), year,
                    request.getType(), request.getAssetType().getTableName());
//            if (list == null || CollectionUtil.isEmpty(list)) {
//                return Collections.emptyList();
//            }
            List<String> exitIds = list.stream()
                    .map(PatrolAssetCheck::getAssetId)
                    .distinct()
                    .collect(Collectors.toList());
            if (ids == null || ids.size() != exitIds.size()) {
                Optional.ofNullable(request.getExcludeIds())
                        .ifPresentOrElse(
                                idList -> idList.addAll(exitIds),
                                () -> request.setExcludeIds(exitIds)
                        );
                List<PatrolCheckBase> noExistCheckList = patrolAssetCheckService.selectMaxExpiryByCheckTimeGreaterThanExpiry(request);
                assetExpiryMap = noExistCheckList.stream()
                        .collect(Collectors.toMap(
                                PatrolCheckBase::getAssetId,
                                PatrolCheckBase::getExpiry,
                                (existing, replacement) -> existing.isAfter(replacement) ? existing : replacement
                        ));
            }
        } catch (Exception e) {
            log.error("获取检查记录列表异常", e);
            return Collections.emptyList();
        }

        Map<String, List<PatrolAssetCheck>> assetGroupMap = new LinkedHashMap<>();
        List<String> assetIds = new ArrayList<>();

        // 先根据baseDataList创建map框架
        if (baseDataList != null && !baseDataList.isEmpty()) {
            for (BaseDataCache baseData : baseDataList) {
                String assetId = baseData.getAssetId();
                assetGroupMap.put(assetId, new ArrayList<>());
                assetIds.add(assetId);
            }
        }

        // 将list中的检查记录填充到对应的assetId下
        for (PatrolAssetCheck check : list) {
            String assetId = check.getAssetId();
            List<PatrolAssetCheck> checkList = assetGroupMap.get(assetId);
            if (checkList != null) {
                checkList.add(check);
            }
        }
        // 获取资产频率映射
        final Map<String, Integer> assetFrequencyMap = getAssetFrequencyMap(assetIds, request);

        Class<?> clazz = ASSET_TIMES_MAP.get(request.getAssetType());
        if (clazz == null) {
            log.error("未找到资产类型对应的VO类: {}", request.getAssetType());
            return Collections.emptyList();
        }
        // 处理现有数据
        try {
            Map<String, LocalDateTime> finalAssetExpiryMap = assetExpiryMap;
            List<T> existResult = baseDataList.stream().map(
                            source -> setTimesVO(assetGroupMap.get(source.getAssetId()),
                                    source, request.getType(),
                                    assetFrequencyMap.get(source.getAssetId()),
                                    finalAssetExpiryMap.get(source.getAssetId()), clazz)
                    ).filter(Objects::nonNull)
                    .map(obj -> (T) obj)
                    .toList();
            return existResult;
        } catch (Exception e) {
            log.error("处理现有数据异常", e);
            return new ArrayList<>();
        }
    }

    public <T, B extends BaseDataCache> T setTimesVO(List<PatrolAssetCheck> patrolAssetCheckList,
                                                     B baseDataCache, InspectionType type,
                                                     Integer frequency,
                                                     LocalDateTime lastExpiry, Class<?> targetClass) {
        AssetTimesDTO.AssetTimesDTOBuilder builder = AssetTimesDTO.builder();
        LocalDate now = LocalDate.now();

        // 初始化统计数据
        int monthChecked = 0, monthRequired = 0;
        int quarterChecked = 0, quarterRequired = 0;
        int yearChecked = 0, yearRequired = 0;

        PatrolAssetCheck patrolAssetCheck = new PatrolAssetCheck();
        if (baseDataCache == null) {
            return null;
        }
        BeanUtils.copyProperties(baseDataCache, patrolAssetCheck);

        frequency = FrequencyUtils.getFrequency(frequency, type.getAssetType());

        // 计算季度起止时间
        int currentMonth = now.getMonthValue();
        int startMonth = (currentMonth - 1) / 3 * 3 + 1;
        LocalDate quarterStart = LocalDate.of(now.getYear(), startMonth, 1);
        LocalDate quarterEnd = quarterStart.plusMonths(3).minusDays(1);

        LocalDate startCalculateDate;

        // 处理有历史记录的情况
        if (patrolAssetCheckList != null && !patrolAssetCheckList.isEmpty()) {
            // 用于记录已统计的assetId和日期组合，避免重复统计
            Set<String> countedChecks = new HashSet<>();
            
            // 统计已完成的巡查
            for (PatrolAssetCheck check : patrolAssetCheckList) {
                if (check.getExpiry() == null) {
                    continue;
                }

                LocalDate checkDate = check.getExpiry().toLocalDate();
                String assetId = check.getAssetId();
                
                // 创建唯一标识，组合assetId和checkDate
                String checkKey = assetId + "_" + checkDate.toString();
                
                // 如果已经统计过该组合，则跳过
                if (countedChecks.contains(checkKey)) {
                    continue;
                }
                
                // 添加到已统计集合
                countedChecks.add(checkKey);
                
                boolean isCompleted = (check.getStage() == StageType.COMPLETED);

                // 只统计当年的记录
                if (checkDate.getYear() == now.getYear()) {
                    // 统计年度数据
                    yearRequired++;
                    if (isCompleted) {
                        yearChecked++;
                    }

                    // 统计季度数据
                    if (!checkDate.isBefore(quarterStart) && !checkDate.isAfter(quarterEnd)) {
                        quarterRequired++;
                        if (isCompleted) {
                            quarterChecked++;
                        }
                    }

                    // 统计月度数据
                    if (checkDate.getMonthValue() == now.getMonthValue()) {
                        monthRequired++;
                        if (isCompleted) {
                            monthChecked++;
                        }
                    }
                }
            }

            // 获取最后一次巡查时间，用于计算后续应巡查次数
            startCalculateDate = patrolAssetCheckList.stream()
                    .map(check -> check.getExpiry().toLocalDate())
                    .max(LocalDate::compareTo)
                    .orElse(now);
        } else {
            // 无历史记录，从lastExpiry开始计算
            startCalculateDate = lastExpiry != null ? lastExpiry.toLocalDate() : LocalDate.of(now.getYear(), 1, 1);
        }


        LocalDate calculateDate = startCalculateDate;

        // 生成巡检日期列表
        List<LocalDate> inspectionDates = generateInspectionDates(frequency, calculateDate, now);

        // 计算年度应巡查次数
        yearRequired += inspectionDates.size();
        LocalDate periodStart = null;
        LocalDate periodEnd = null;

        for(LocalDate inspectionDate : inspectionDates) {
            periodStart = periodEnd == null ? calculateDate : periodEnd;
            periodEnd = inspectionDate;
            if (isInQuarter(periodStart, periodEnd, quarterStart)) {
                quarterRequired++;
            }
            // 检查日期是否在当前月份
            if (isInMonth(periodStart, periodEnd, now)) {
                monthRequired++;
            }
        }
        // 设置类型
        if (patrolAssetCheck.getType() == null) {
            patrolAssetCheck.setType(type);
        }

        // 构建结果
        builder.monthRequiredCount(monthRequired)
                .monthCheckedCount(monthChecked)
                .quarterRequiredCount(quarterRequired)
                .quarterCheckedCount(quarterChecked)
                .yearRequiredCount(yearRequired)
                .yearCheckedCount(yearChecked);

        return AssetConvertUtils.convertToTimesVO(patrolAssetCheck, builder.build());
    }

    // 新增私有方法
    private Map<String, Integer> getAssetFrequencyMap(List<String> assetIds, AssetBaseDataRequest request) {
        try {
            return patrolFrequencySettingsService.assetMapByAssetIds(assetIds,
                    request.getAssetType().getCode(), request.getType().getFlag());
        } catch (Exception e) {
            log.error("获取资产频率映射异常", e);
            return new HashMap<>();
        }
    }

    /**
     * 根据频率生成巡检周期，从指定日期开始计算完整年度
     *
     * @param frequency 频率（多少个月一次）
     * @param startDate 开始日期
     * @return 巡检日期列表，每个日期表示一个巡检周期的结束日期
     */
    private List<LocalDate> generateInspectionDates(int frequency, LocalDate startDate,LocalDate now) {
        List<LocalDate> inspectionDates = new ArrayList<>();
        if (frequency <= 0) {
            return inspectionDates;
        }
        // 确保有效的开始日期
        LocalDate start = startDate != null ? startDate : LocalDate.now();
        // 计算当年结束日期
        LocalDate endOfYear = LocalDate.of(now.getYear(), 12, 31);
        // 生成一年内的所有巡检日期
        LocalDate currentDate = start;
        YearMonth currentYearMonth1 = YearMonth.from(currentDate);
        YearMonth nextYearMonth1 = currentYearMonth1.plusMonths(frequency);
        currentDate = nextYearMonth1.atEndOfMonth();
        while (!currentDate.isAfter(endOfYear)) {
            // 添加当前日期作为一个巡检日期
            inspectionDates.add(currentDate);
            // 计算下一个巡检日期，确保使用月末日期
            YearMonth currentYearMonth = YearMonth.from(currentDate);
            YearMonth nextYearMonth = currentYearMonth.plusMonths(frequency);
            currentDate = nextYearMonth.atEndOfMonth();
        }
        return inspectionDates;
    }

    /**
     * 判断一个巡检日期是否在指定的月份内
     *
     * @param dateStart 巡检开始日期
     * @param dateEnd  巡检结束日期
     * @param nowDate 当前日期
     * @return 是否在该月份内
     */
    private boolean isInMonth(LocalDate dateStart, LocalDate dateEnd, LocalDate nowDate) {
        LocalDate monthStart = LocalDate.of(nowDate.getYear(), nowDate.getMonthValue(), 1);
        LocalDate monthEnd = monthStart.plusMonths(1).minusDays(1);
        dateStart = dateStart.plusDays(1);
        return isDateIntersect(dateStart, dateEnd, monthStart, monthEnd);
    }

    /**
     * 判断一个巡检日期是否在指定的季度内
     *
     * @param dateStart          巡检开始日期
     * @param dateEnd           巡检结束日期
     * @param quarterStart     季度开始月份
     * @return 是否在该季度内
     */
    private boolean isInQuarter(LocalDate dateStart, LocalDate dateEnd, LocalDate quarterStart) {
        LocalDate quarterEnd = quarterStart.plusMonths(3).minusDays(1);
        dateStart = dateStart.plusDays(1);
        return isDateIntersect(dateStart, dateEnd, quarterStart, quarterEnd);
    }

    /**
     * 判断两个时间区间是否有交集
     * 区间1: [periodStart, periodEnd]
     * 区间2: [customStart, customEnd]
     *
     * @param periodStart 周期开始时间（包含）
     * @param periodEnd   周期结束时间（包含）
     * @param customStart 自定义开始时间（包含）
     * @param customEnd   自定义结束时间（包含）
     * @return 是否有交集
     */
    private boolean isDateIntersect(LocalDate periodStart, LocalDate periodEnd,
                                  LocalDate customStart, LocalDate customEnd) {
        // 确保参数不为空
        if (periodStart == null || periodEnd == null || 
            customStart == null || customEnd == null) {
            return false;
        }
        // 检查区间是否有效
        if (periodStart.isAfter(periodEnd) || customStart.isAfter(customEnd)) {
            return false;
        }
        // 判断是否有交集
        // 区间1: [periodStart, periodEnd]
        // 区间2: [customStart, customEnd]
        // 无交集的情况：
        // 1. customEnd < periodStart
        // 2. customStart > periodEnd
        return !(customEnd.isBefore(periodStart) || 
                customStart.isAfter(periodEnd));
    }

}
