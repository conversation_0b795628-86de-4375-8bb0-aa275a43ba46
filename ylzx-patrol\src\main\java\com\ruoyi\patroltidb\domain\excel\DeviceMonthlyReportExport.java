package com.ruoyi.patroltidb.domain.excel;


import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 机电故障上月报导出对象
 * 
 * <AUTHOR>
 * @date 2025-04-8
 */
@ApiModel(value="机电故障上月报导出对象")
@Data
public class DeviceMonthlyReportExport implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 资产名称 */
    @Excel(name = "隧道名称", type = Excel.Type.EXPORT, sort = 4)
    @TableField(exist = false)
    private String assetName;

    /** 资产ID */
//    @Excel(name = "资产ID", type = Excel.Type.EXPORT, sort = 4)
    @TableField(exist = false)
    private String assetId;

    /** 资产编码 */
    @Excel(name = "隧道编码", type = Excel.Type.EXPORT, sort = 3)
    @TableField(exist = false)
    private String assetCode;

    /** 路线编码 */
    @Excel(name = "路线编码", type = Excel.Type.EXPORT, sort = 1)
    @TableField(exist = false)
    private String routeCode;

    /** 路线名称 */
    @Excel(name = "路线名称", type = Excel.Type.EXPORT, sort = 2)
    @TableField(exist = false)
    private String routeName;

    /** 管养单位id */
    @TableField(exist = false)
    private Long domainId;

    /** 管养单位名称 */
    @Excel(name = "养护机构", type = Excel.Type.EXPORT, sort = 5)
    @TableField(exist = false)
    private String deptName;

    @Excel(name = "年份", type = Excel.Type.EXPORT, sort = 6)
    @ApiModelProperty(value = "年份")
    @TableField(exist = false)
    private Integer year;

    @Excel(name = "月份", type = Excel.Type.EXPORT, sort = 7)
    @ApiModelProperty(value = "月份")
    @TableField(exist = false)
    private Integer month;

    @Excel(name = "故障数量", type = Excel.Type.EXPORT, sort = 8)
    @ApiModelProperty(value = "故障数量")
    @TableField(exist = false)
    private Integer faultNum;

//    @Excel(name = "修复数量")
    @ApiModelProperty(value = "修复数量")
    @TableField(exist = false)
    private Integer repairNum;


}
