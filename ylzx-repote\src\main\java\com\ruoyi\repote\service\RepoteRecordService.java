package com.ruoyi.repote.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.repote.domain.RepoteRecord;

/**
 * 填报记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
public interface RepoteRecordService extends IService<RepoteRecord> {

    /**
     * 根据条件查询填报记录数据列表
     * @param params 查询条件
     */
    List<RepoteRecord> findListByParam(Map<String, Object> params);


}
