package com.ruoyi.patrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patrol.domain.PatrolInspectionLast;
import com.ruoyi.patrol.enums.AssetType;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * (PatrolInspectionLast)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-17 15:01:08
 */
public interface PatrolInspectionLastMapper extends BaseMapper<PatrolInspectionLast> {
    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<PatrolInspectionLast> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PatrolInspectionLast> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<PatrolInspectionLast> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<PatrolInspectionLast> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param assetIds List<String> 插入修改的资产id
     * @param type     AssetType 资产类型
     * @param generatedAt LocalDateTime 日常检查时间
     * @param generatedBy LocalDateTime 经常检查时间
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    Integer insertOrUpdateBatchByAssetIdList(@Param("assetIds") List<String> assetIds,
                                             @Param("type") AssetType type,
                                             @Param("generatedAt") LocalDateTime generatedAt,
                                             @Param("generatedBy") LocalDateTime generatedBy);


}

