package com.ruoyi.patrol.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 检查项排除对象 patrol_parts_ignore
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="检查项排除")
@TableName("patrol_parts_ignore")
@Data
public class PatrolPartsIgnore extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 检查项类型(冗余) */
    @Excel(name = "检查项类型(冗余)")
    @ApiModelProperty(value = "检查项类型(冗余)")
    private String partsType;

    /** 不检查项id */
    @Excel(name = "不检查项id")
    @ApiModelProperty(value = "不检查项id")
    private String partsId;

    /** 资源id */
    @Excel(name = "资源id")
    @ApiModelProperty(value = "资源id")
    private String assetId;

}
