package com.ruoyi.patrol.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.patrol.utils.handler.GeometryTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Resource;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月26日 15:11
 */
@Resource
@TableName("patrol_inspection_geo")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatrolInspectionGeo {
    @ApiModelProperty(value = "巡检日志id")
    @TableId("log_id")
    private String logId;

    @ApiModelProperty(value = "地理空间坐标")
    @TableField(value="shape", typeHandler = GeometryTypeHandler.class)
    private String shape;
}
