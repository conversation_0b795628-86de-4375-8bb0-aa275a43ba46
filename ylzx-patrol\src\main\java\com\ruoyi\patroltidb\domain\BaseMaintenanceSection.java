package com.ruoyi.patroltidb.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 养护路段管理对象 base_maintenance_section
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@ApiModel(description = "养护路段实体类")
@Data
@TableName("base_maintenance_section")
public class BaseMaintenanceSection implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 养护路段id
     */
    @ApiModelProperty(value = "养护路段id")
    private String maintenanceSectionId;

    /**
     * 养护路段名称
     */
    @ApiModelProperty(value = "养护路段名称")
    @Excel(name = "养护路段名称")
    private String maintenanceSectionName;

    /**
     * 管养单位id
     */
    @ApiModelProperty(value = "管养单位id")
    @Excel(name = "管养单位id")
    private String departmentId;

    @ApiModelProperty(value = "管养单位idList")
    @TableField(exist = false)
    private List<String> departmentIdList;

    @TableField(exist = false)
    private String departmentName;


    /**
     * 公路路段id
     */
    @ApiModelProperty(value = "公路路段id")
    @Excel(name = "公路路段id")
    private String roadSectionId;
    @TableField(exist = false)
    private String roadSectionName;

    /**
     * 起点桩号
     */
    @Excel(name = "起点桩号")
    private BigDecimal pileStart;

    /**
     * 终点桩号
     */
    @Excel(name = "终点桩号")
    private BigDecimal pileEnd;

    /**
     * 起点名称
     */
    @Excel(name = "起点名称")
    private String placeStartName;

    /**
     * 终点名称
     */
    @Excel(name = "终点名称")
    private String placeEndName;

    /**
     * 养护里程(m)
     */
    @Excel(name = "养护里程(m)")
    @ApiModelProperty(value = "养护里程(m)")
    private String mainLength;

    /**
     * 路线等级
     */
    @ApiModelProperty(value = "路线等级")
    @Excel(name = "路线等级")
    private String routeGrade;

    /**
     * 统一里程起点桩号
     */
    @Excel(name = "统一里程起点桩号")
    private BigDecimal unifiedMileagePileStart;

    /**
     * 统一里程终点桩号
     */
    @Excel(name = "统一里程终点桩号")
    private BigDecimal unifiedMileagePileEnd;

    /**
     * 通车时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "通车时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date openingTime;

    /**
     * 路段长度
     */
    @Excel(name = "路段长度")
    private String roadSectionLength;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    /**
     * 运营状态（1、未投入运营，2、运营中，3、已作废，4、已移交）
     */
    @ApiModelProperty(value = "运营状态（1、未投入运营，2、运营中，3、已作废，4、已移交）")
    private Integer state;

    /**
     * 管理单位性质代码
     */
    @ApiModelProperty(value = "管理单位性质代码")
    private String manageNatureCode;

    /**
     * 管理单位
     */
    @ApiModelProperty(value = "管理单位")
    private String managementUnit;

    /**
     * 监管单位
     */
    @ApiModelProperty(value = "监管单位")
    private String superUnitName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 产权单位id
     */
    @TableField(exist = false)
    private String propertyUnitId;

    /**
     * 产权单位名称
     */
    @TableField(exist = false)
    private String propertyUnitName;

    /**
     * 产权单位类型（“项目公司”，“集团公司”）
     */
    @TableField(exist = false)
    private String propertyUnitType;

    /**
     * 管理处id-中间库使用字段
     */
    @TableField(exist = false)
    private String manageUnitId;

    /**
     * 权属类型(集团公司,项目公司) - 中间库使用字段
     */
    @TableField(exist = false)
    private String ownerUnit;

    /**
     * id集合
     */
    @TableField(exist = false)
    private List<String> ids;
}
