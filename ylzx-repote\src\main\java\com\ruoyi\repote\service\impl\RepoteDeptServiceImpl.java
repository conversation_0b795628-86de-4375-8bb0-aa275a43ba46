package com.ruoyi.repote.service.impl;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repote.mapper.RepoteDeptMapper;
import com.ruoyi.repote.domain.RepoteDept;
import com.ruoyi.repote.service.RepoteDeptService;

/**
 * 填报单位Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class RepoteDeptServiceImpl extends ServiceImpl<RepoteDeptMapper, RepoteDept> implements RepoteDeptService {

    @Autowired
    private RepoteDeptMapper repoteDeptMapper;

    @Override
    public List<RepoteDept> findListByParam(Map<String, Object> params) {
        return repoteDeptMapper.findListByParam(params);
    }


}
