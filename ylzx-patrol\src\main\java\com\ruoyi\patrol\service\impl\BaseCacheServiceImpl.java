package com.ruoyi.patrol.service.impl;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.manage.api.domain.bridge.BridgeStaticRequest;
import com.ruoyi.manage.api.domain.bridge.BridgeStaticResponse;
import com.ruoyi.manage.api.domain.culvert.BaseCulvertRequest;
import com.ruoyi.manage.api.domain.culvert.BaseCulvertResponse;
import com.ruoyi.manage.api.domain.tunnel.BaseTunnelRequest;
import com.ruoyi.manage.api.domain.tunnel.BaseTunnelResponse;
import com.ruoyi.manage.api.service.BaseBridgeStaticService;
import com.ruoyi.manage.api.service.BaseCulvertService;
import com.ruoyi.manage.api.service.BaseTunnelService;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseCulvertResponseCache;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.domain.dto.BaseDataDomainWithDistance;
import com.ruoyi.patrol.domain.dto.DeptToSectionDTO;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.enums.StageType;
import com.ruoyi.patrol.mapper.PatrolAssetCheckMapper;
import com.ruoyi.patrol.service.BaseBridgeResponseCacheService;
import com.ruoyi.patrol.service.BaseCacheService;
import com.ruoyi.patrol.service.BaseCulvertResponseCacheService;
import com.ruoyi.patrol.service.BaseTunnelResponseCacheService;
import com.ruoyi.patrol.service.enrichment.DataEnrichmentService;
import com.ruoyi.patrol.service.handler.AssetDataHandler;
import com.ruoyi.patrol.service.handler.AssetDataHandlerRegistry;
import com.ruoyi.patrol.utils.BeanConvertUtils;
import com.ruoyi.patrol.utils.DomainToCacheUtils;
import com.ruoyi.patrol.utils.GeoUtils;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteRouteService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.api.domain.dto.BaseRouteDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.ruoyi.common.security.utils.SecurityUtils.getLoginUser;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月28日 17:41
 */
@Service
@Slf4j
@Master
public class BaseCacheServiceImpl implements BaseCacheService {
    @Resource
    BaseBridgeResponseCacheService baseBridgeResponseCacheService;
    @Resource
    BaseCulvertResponseCacheService baseCulvertResponseCacheService;
    @Resource
    BaseTunnelResponseCacheService baseTunnelResponseCacheService;
    @Resource
    RemoteDeptAuthService remoteDeptAuthService;
    @Resource
    private BaseBridgeStaticService baseBridgeStaticService;
    @Resource
    private BaseCulvertService baseCulvertService;
    @Resource
    private BaseTunnelService baseTunnelService;
    @Resource
    private PatrolAssetCheckMapper patrolAssetCheckMapper;
    @Resource
    private TokenService tokenService;
    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private RemoteRouteService remoteRouteService;
    @Resource
    private AssetDataHandlerRegistry handlerRegistry;
    /**
     * 根据数据量阈值决定是否使用并行流
     */
    private static final int PARALLEL_STREAM_THRESHOLD = 1000;
    /**
     * 当分页要求的记录数（limit）小于【过滤数据总数 * TOP_K_OPTIMIZATION_THRESHOLD_RATIO】时，
     * 启用 Top‑K 策略。比如 0.5 表示当 limit 小于过滤后记录一半时启用优化。
     */
    private static final double TOP_K_OPTIMIZATION_THRESHOLD_RATIO = 0.5;
    private static final int MIN_PARTITION_SIZE = 100;
    private static final int MAX_PARTITION_SIZE = 1000;
    /**
     * 分页查询数据的页大小
     */
    private static final int PAGE_SIZE = 1000;
    private static final ExecutorService SHARED_EXECUTOR = Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors(),
            new ThreadFactoryBuilder().setNameFormat("data-cache-pool-%d").build()
    );
    // 添加资产类型到缓存类的映射关系
    private static final Map<AssetType, Class<? extends BaseDataCache>> ASSET_CACHE_MAP = new HashMap<>();

    static {
        ASSET_CACHE_MAP.put(AssetType.BRIDGE, BaseBridgeResponseCache.class);
        ASSET_CACHE_MAP.put(AssetType.CULVERT, BaseCulvertResponseCache.class);
        ASSET_CACHE_MAP.put(AssetType.TUNNEL, BaseTunnelResponseCache.class);
        ASSET_CACHE_MAP.put(AssetType.DEVICE, BaseTunnelResponseCache.class);
    }

    @Autowired
    private ApplicationContext applicationContext;

    @Master
    private <T extends BaseDataCache> List<T> selectCache(AssetBaseDataRequest request, Class<T> type) {
        if (request.getAssetType() == null) {
            throw new RuntimeException("资产信息类型错误");
        }
        this.setDeptIds(request);

        long totalCount = getTotalCount(request);

        // 分片
        List<int[]> partitions = partitionCount((int) totalCount);

        // 自定义线程池
        ExecutorService executor = new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors(),
                Runtime.getRuntime().availableProcessors() * 2,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>()
        );

        List<CompletableFuture<List<T>>> futures = partitions.stream()
                .map(partition -> CompletableFuture.supplyAsync(() -> {
                    Long offset = (long) partition[0];
                    Long limit = (long) partition[1];
                    return queryWithPagination(request, type, offset, limit);
                }, executor))
                .collect(Collectors.toList());

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        List<T> finalList = allOf.thenApply(v -> futures.stream()
                        .flatMap(future -> {
                            try {
                                return future.get().stream();
                            } catch (InterruptedException | ExecutionException e) {
                                log.error("查询失败", e);
                                return Stream.empty();
                            }
                        })
                        .collect(Collectors.toList()))
                .join();

        executor.shutdown();
        return finalList;
    }

    /**
     * 根据当前查询条件获取管理处信息
     *
     * @param request      查询条件
     * @param allowDeptIds 允许的部门ID列表
     * @return 部门ID和名称的映射
     */
    public DeptToSectionDTO getDeptList(AssetBaseDataRequest request, List<Long> allowDeptIds) {
        Map<String, Set<String>> deptNameToSectionNameMap = new HashMap<>();
        Map<Long, String> deptIdNameMap = new HashMap<>();
        // 1. 获取允许的部门ID
        List<Long> finalAllowDeptIds = Optional.ofNullable(allowDeptIds)
                .orElseGet(() -> remoteDeptAuthService.getDeptIdList().getData());

        // 2. 获取基础数据列表
        List<BaseDataCache> baseDataList = getBaseDataList(request);

        // 3. 提取并过滤部门ID
        List<Long> deptIds = extractDeptIds(baseDataList, finalAllowDeptIds);

        // 4. 获取部门信息
        List<SysDept> sysDeptList = remoteDeptAuthService.getByIdList(deptIds).getData();

        // 5. 分类处理部门信息
        Map<Boolean, List<SysDept>> groupedDepts = sysDeptList.stream()
                .collect(Collectors.partitioningBy(dept -> dept.getDeptType() == 3));

        // 6. 处理直接管理处
        deptIdNameMap = groupedDepts.get(true).stream()
                .collect(Collectors.toMap(
                        SysDept::getDeptId,
                        SysDept::getDeptName,
                        (existing, replacement) -> existing,
                        HashMap::new
                ));

        // 7. 处理非管理处的祖先部门
        processNonManagementDepts(groupedDepts.get(false), deptIdNameMap, deptIds);

        // 8. 处理养护段名称
        deptNameToSectionNameMap = new HashMap<>();
        for (BaseDataCache baseDataCache : baseDataList) {
            String managementMaintenanceId = baseDataCache.getManagementMaintenanceId();
            if (managementMaintenanceId == null || managementMaintenanceId.trim().isEmpty()) {
                continue;
            }
            Long deptId = Long.parseLong(managementMaintenanceId.trim());
            String deptName = deptIdNameMap.get(deptId);
            String sectionName = baseDataCache.getMaintenanceSectionName();
            deptNameToSectionNameMap.putIfAbsent(deptName, new HashSet<>());
            deptNameToSectionNameMap.get(deptName).add(sectionName);
        }

        return DeptToSectionDTO.builder()
                .deptIdNameMap(deptIdNameMap)
                .deptNameToSectionNameMap(deptNameToSectionNameMap)
                .build();
    }

    /**
     * 获取基础数据列表
     */
    private <T extends BaseDataCache> List<T> getBaseDataList(AssetBaseDataRequest request) {
        AssetBaseDataRequest copyRequest = new AssetBaseDataRequest();
        BeanUtils.copyProperties(request, copyRequest);
        return selectBaseDataResponseByAssetIds(copyRequest);
    }

    /**
     * 提取并过滤部门ID
     */
    private List<Long> extractDeptIds(List<BaseDataCache> baseDataList, List<Long> allowDeptIds) {
        List<Long> deptIds = baseDataList.stream()
                .map(BaseDataCache::getManagementMaintenanceId)
                .filter(Objects::nonNull)
                .filter(id -> !id.trim().isEmpty())
                .map(id -> {
                    try {
                        return Long.parseLong(id.trim());
                    } catch (NumberFormatException e) {
                        log.warn("无效的部门ID: {}", id);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        deptIds.retainAll(allowDeptIds);
        return deptIds;
    }

    /**
     * 处理非管理处部门的祖先部门
     */
    private void processNonManagementDepts(List<SysDept> nonManagementDepts,
                                           Map<Long, String> idNameMap,
                                           List<Long> existingDeptIds) {
        // 1. 提取并处理祖先ID
        Set<Long> ancestorIds = nonManagementDepts.stream()
                .map(SysDept::getAncestors)
                .flatMap(ancestors -> Arrays.stream(ancestors.split(",")))
                .filter(s -> !s.isEmpty() && !s.equals("0"))
                .map(String::trim)
                .map(id -> {
                    try {
                        return Long.parseLong(id);
                    } catch (NumberFormatException e) {
                        log.warn("无效的祖先ID: {}", id);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .filter(id -> !existingDeptIds.contains(id))
                .collect(Collectors.toSet());

        if (ancestorIds.isEmpty()) {
            return;
        }

        // 2. 查询并过滤祖先部门
        List<SysDept> ancestorDepts = remoteDeptAuthService.getByIdList(new ArrayList<>(ancestorIds))
                .getData()
                .stream()
                .filter(dept -> dept.getDeptType() == 3)
                .toList();

        // 3. 添加到结果Map
        ancestorDepts.forEach(dept ->
                idNameMap.putIfAbsent(dept.getDeptId(), dept.getDeptName())
        );
    }

    /**
     * 查询缓存数据
     *
     * @param request  请求参数
     * @param type     类型
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 缓存数据
     */
    public <T extends BaseDataCache> List<T> selectCache(AssetBaseDataRequest request, Class<T> type, Long pageNum, Long pageSize) {
        if (request.getAssetType() == null) {
            return Collections.emptyList();
        }
        this.setDeptIds(request);
        if (pageNum == null || pageSize == null) {
            return this.selectCache(request, type);
        }
        return this.queryWithPagination(request, type, (pageNum - 1) * pageSize, pageSize);
    }


    /**
     * 权限id设置
     *
     * @param request 资产请求对象
     */
    public void setDeptIds(AssetBaseDataRequest request) {
        // 获取资产类型
        AssetType assetType = Optional.ofNullable(request.getAssetType())
                .orElseGet(() -> Optional.ofNullable(request.getType())
                        .map(InspectionType::getAssetType)
                        .orElse(null));

        if (assetType == null) {
            request.processIdAndExclude();
            return;
        }
        if(request.getDeptId() != null && !request.getDeptId().trim().isEmpty()) {
            R<List<String>> response = remoteDeptAuthService.findAssetIdListByDeptId(
                    Long.valueOf(request.getDeptId()), assetType.getCode());
            if (response.getCode() != 200) {
                String errorMsg = String.format("获取%s权限失败", assetType.getDescription());
                throw new RuntimeException(errorMsg);
            }
            List<String> assetIds = response.getData();
            // 去重后加入ids
            request.setIds(assetIds.stream().distinct().collect(Collectors.toList()));
            request.processIdAndExclude();
            return;
        }
        // 获取数据规则标志，如果为空则默认为true
        boolean dataRule = Optional.ofNullable(request.getDataRule())
                .orElse(true);

        // 如果是管理员或不需要数据规则，直接返回
        if (Optional.ofNullable(getLoginUser())
                .map(LoginUser::getSysUser)
                .map(SysUser::isAdmin)
                .orElse(false) || !dataRule) {
            request.processIdAndExclude();
            return;
        }
        // 获取并处理资产IDs
        Set<String> assetIdsSet = Optional.ofNullable(getAssetIdsByType(assetType, request.getRoleUserId()))
                .map(HashSet::new)
                .orElseGet(() -> Optional.ofNullable(request.getIds())
                        .map(HashSet::new)
                        .orElseGet(HashSet::new));

        // 如果有权限集合且请求中包含IDs，取交集
        if (request.getIds() != null && !request.getIds().isEmpty()) {
            assetIdsSet.retainAll(request.getIds());
        }

        // 设置处理后的IDs
        request.setIds(Optional.of(assetIdsSet)
                .map(ArrayList::new)
                .orElseGet(ArrayList::new));
        if (request.getIds() != null && request.getIds().isEmpty()) {
            throw new RuntimeException(String.format("无%s资产权限查看数据", assetType.getDescription()));
        }
        request.processIdAndExclude();
    }


    private List<int[]> partitionCount(int totalCount) {
        int availableProcessors = Runtime.getRuntime().availableProcessors();

        // 计算每个分片的大小
        int calculatedChunkSize = (totalCount + availableProcessors - 1) / availableProcessors;
        final int chunkSize = Math.min(calculatedChunkSize, 1000);

        // 如果每个分片太小，调整分片大小
        final int adjustedChunkSize = Math.max(chunkSize, 100);

        List<int[]> partitions = new ArrayList<>();
        for (int i = 0; i < totalCount; i += adjustedChunkSize) {
            int[] partition = new int[2];
            partition[0] = i; // offset
            partition[1] = Math.min(adjustedChunkSize, totalCount - i); // limit
            partitions.add(partition);
        }
        return partitions;
    }


    /**
     * 获取总记录数
     *
     * @param request 请求参数
     * @return 总记录数
     */
    public int getTotalCount(AssetBaseDataRequest request) {
        // 根据类型获取相应的服务并查询总记录数
        switch (request.getAssetType()) {
            case BRIDGE -> {
                return baseBridgeResponseCacheService.countAssetBaseData(request);
            }
            case CULVERT -> {
                return baseCulvertResponseCacheService.countAssetBaseData(request);
            }
            case TUNNEL, DEVICE -> {
                return baseTunnelResponseCacheService.countAssetBaseData(request);
            }
            default -> {
                log.warn("不支持的类型 : {}", request.getAssetType());
                return 0;
            }
        }
    }


    private <T> List<T> queryWithPagination(AssetBaseDataRequest request, Class<T> type, Long offset, Long limit) {
        if (request == null) {
            return Collections.emptyList();
        }
        // 资产权限为空时，直接返回空列表
        if (request.getIds() != null && request.getIds().isEmpty()) {
            return Collections.emptyList();
        }
        switch (request.getAssetType()) {
            case BRIDGE -> {
                if (type.equals(BaseBridgeResponseCache.class)) {
                    return castList(baseBridgeResponseCacheService.selectAssetBaseData(request, offset, limit));
                } else {
                    throw new IllegalArgumentException("BRIDGE 类型不匹配");
                }
            }
            case CULVERT -> {
                if (type.equals(BaseCulvertResponseCache.class)) {
                    return castList(baseCulvertResponseCacheService.selectAssetBaseData(request, offset, limit));
                } else {
                    throw new IllegalArgumentException("CULVERT 类型不匹配");
                }
            }
            case TUNNEL, DEVICE -> {
                if (type.equals(BaseTunnelResponseCache.class)) {
                    return castList(baseTunnelResponseCacheService.selectAssetBaseData(request, offset, limit));
                } else {
                    throw new IllegalArgumentException("TUNNEL 类型不匹配");
                }
            }
            default -> {
                log.warn("不支持的类型: {}", request.getAssetType());
                return Collections.emptyList();
            }
        }
    }

    /**
     * 根据资产类型获取对应的资产ID列表
     *
     * @param assetType 资产类型
     * @return List<String> 资产ID列表
     */
    private List<String> getAssetIdsByType(AssetType assetType, Long roleUserId) {
        if (assetType == null) {
            throw new RuntimeException("资产类型不能为空");
        }

        R<List<String>> response = switch (assetType) {
            case BRIDGE -> remoteDeptAuthService.findUserMaintenanceIds(roleUserId);
            case TUNNEL, DEVICE -> remoteDeptAuthService.findUserTunnelIds(roleUserId);
            case CULVERT -> remoteDeptAuthService.findUserCulvertIds(roleUserId);
        };

        if (response.getCode() != 200) {
            String errorMsg = String.format("获取%s权限失败", assetType.getDescription());
            throw new RuntimeException(errorMsg);
        }

        return response.getData();
    }


    /**
     * 根据资产类型查询和缓存基础数据
     */
    @SuppressWarnings("unchecked")
    private void selectBaseDataResponse(AssetType assetType) {
        boolean dataRule = false;
        AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
        assetBaseDataRequest.setType(InspectionType.fromAssetType(assetType, dataRule));
        
        try {
            // 获取对应的资产处理器
            AssetDataHandler<?, ?, ?> handler = handlerRegistry.getHandler(assetType);
            
            // 使用处理器获取数据
            List<?> resultList = remoteBaseDataResponseByAssetIds(assetBaseDataRequest, dataRule);
            
            // 转换数据为缓存对象 - 使用通配符捕获转换结果，保留特定子类的字段信息
            List<? extends BaseDataCache> cacheList = handler.convertToCacheList((List)resultList);
            
            // 使用处理器进行数据增强和保存 - 保持类型信息
            handler.enrichData((List)cacheList);
            handler.saveCache((List)cacheList);
            
        } catch (Exception e) {
            log.error("处理资产类型[{}]数据失败: {}", assetType, e.getMessage(), e);
            throw new RuntimeException("处理资产数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 补充部门名称信息
     *
     * @param dataList 需要补充的数据列表
     * @param <T>      数据类型（必须继承自BaseDataCache）
     * @deprecated 此方法已迁移到DataEnrichmentService类中，但保留此方法是为了确保向后兼容性。
     *             如有现有代码调用此方法，仍可正常工作，但会优先尝试使用DataEnrichmentService。
     *             新代码应该直接使用: DataEnrichmentService.supplementDeptNames()
     */
    @Deprecated
    private <T extends BaseDataCache> void supplementDeptNames(List<T> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 以下是旧的实现逻辑，保留以确保向后兼容
        // 检查是否有需要补充部门名称的数据
        List<T> needSupplementList = dataList.stream()
                .filter(item -> StringUtils.isEmpty(item.getManagementMaintenanceName())
                        || StringUtils.isEmpty(item.getManagementMaintenanceBranchName()))
                .toList();

        // 如果有需要补充的数据，获取部门映射并补充
        if (!needSupplementList.isEmpty()) {
            R<Map<String, String>> deptMapResponse = remoteDeptAuthService.getDeptIdNameMap();
            if (deptMapResponse != null && deptMapResponse.getCode() == 200) {
                Map<String, String> deptIdNameMap = deptMapResponse.getData();

                // 遍历所有数据进行补充
                dataList.forEach(item -> {
                    // 补充管理处名称
                    if (StringUtils.isEmpty(item.getManagementMaintenanceName())
                            && StringUtils.isNotEmpty(item.getManagementMaintenanceId())) {
                        item.setManagementMaintenanceName(
                                deptIdNameMap.get(item.getManagementMaintenanceId()));
                    }

                    // 补充管理分处名称
                    if (StringUtils.isEmpty(item.getManagementMaintenanceBranchName())
                            && StringUtils.isNotEmpty(item.getManagementMaintenanceBranchId())) {
                        item.setManagementMaintenanceBranchName(
                                deptIdNameMap.get(item.getManagementMaintenanceBranchId()));
                    }
                });
            }
        }
    }

    /**
     * 补充路线信息
     *
     * @param dataList 需要补充的数据列表
     * @param <T>      数据类型（必须继承自BaseDataCache）
     * @deprecated 此方法已迁移到DataEnrichmentService类中，但保留此方法是为了确保向后兼容性。
     *             如有现有代码调用此方法，仍可正常工作，但会优先尝试使用DataEnrichmentService。
     *             新代码应该直接使用: DataEnrichmentService.supplementRouteInfo()
     */
    @Deprecated
    private <T extends BaseDataCache> void supplementRouteInfo(List<T> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 以下是旧的实现逻辑，保留以确保向后兼容
        // 检查是否有需要补充路线名称的数据
        List<T> needSupplementList = dataList.stream()
                .filter(item -> (StringUtils.isEmpty(item.getRouteName()) || StringUtils.isEmpty(item.getRouteCode()))
                        && StringUtils.isNotEmpty(item.getRouteId()))
                .toList();

        // 如果有需要补充的数据，获取路线信息并补充
        if (!needSupplementList.isEmpty()) {
            R<List<BaseRouteDTO>> routeListResponse = remoteRouteService.listAll();
            if (routeListResponse != null && routeListResponse.getCode() == 200) {
                List<BaseRouteDTO> routeList = routeListResponse.getData();
                if (routeList == null || routeList.isEmpty()) {
                    return;
                }

                // 创建路线ID到路线信息的映射
                Map<String, BaseRouteDTO> routeIdMap = routeList.stream()
                        .filter(route -> route.getRouteId() != null)
                        .collect(Collectors.toMap(
                                BaseRouteDTO::getRouteId,
                                route -> route,
                                (existing, replacement) -> existing
                        ));

                // 遍历所有数据进行补充
                for (T item : dataList) {
                    String routeId = item.getRouteId();
                    if ((StringUtils.isEmpty(item.getRouteName()) || StringUtils.isEmpty(item.getRouteCode()))
                            && StringUtils.isNotEmpty(routeId)) {
                        BaseRouteDTO route = routeIdMap.get(routeId);
                        if (route != null) {
                            // 补充路线名称
                            if (StringUtils.isEmpty(item.getRouteName())) {
                                item.setRouteName(route.getRouteName());
                            }

                            // 补充路线编码
                            if (StringUtils.isEmpty(item.getRouteCode())) {
                                item.setRouteCode(route.getRouteCode());
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据资产id查询资产数据
     *
     * @param assetBaseDataRequest 查询条件
     * @param dataRule 是否应用数据规则
     * @return List<BaseDataDomain>
     */
    private <T extends BaseDataDomain> List<T> remoteBaseDataResponseByAssetIds(AssetBaseDataRequest assetBaseDataRequest, Boolean dataRule) {
        AssetType assetType;
        if (assetBaseDataRequest.getAssetType() != null) {
            assetType = assetBaseDataRequest.getAssetType();
        } else {
            assetType = assetBaseDataRequest.getType().getAssetType();
        }
        
        // 获取对应的资产处理器
        AssetDataHandler<?, ?, ?> handler = handlerRegistry.getHandler(assetType);
        
        try {
            // 使用泛型通配符确保类型安全
            return fetchDataWithHandler(assetBaseDataRequest, dataRule, handler);
        } catch (Exception e) {
            log.error("获取资产数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取资产数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用资产处理器获取数据
     * 通过类型擦除和通配符实现类型兼容
     *
     * @param assetBaseDataRequest 基础数据请求
     * @param dataRule 是否应用数据规则
     * @param handler 资产处理器
     * @return 资产数据列表
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private <T extends BaseDataDomain> List<T> fetchDataWithHandler(
            AssetBaseDataRequest assetBaseDataRequest,
            Boolean dataRule,
            AssetDataHandler handler) throws Exception {
        
        // 创建请求实例
        BaseDataDomain request = (BaseDataDomain) handler.createRequestInstance();
        
        // 准备请求参数
        handler.prepareRequest(assetBaseDataRequest, request, dataRule.booleanValue());
        
        // 调用分页获取方法
        return fetchPaginatedData(
                assetBaseDataRequest,
                dataRule,
                (Class<T>) handler.getRequestClass(),
                req -> (MTableDataInfo) handler.fetchData(req)
        );
    }

    /**
     * 通用的分页获取数据方法，适用于所有资产类型
     * 包含重试机制，对失败的页面单独重试
     * 
     * @param <REQ> 请求类型
     * @param <RESP> 响应数据类型
     * @param <T> 返回的数据类型
     * @param assetBaseDataRequest 基础资产请求参数
     * @param dataRule 是否应用数据规则
     * @param requestClass 请求类的Class
     * @param dataService 数据服务函数
     * @return 合并后的数据列表
     */
    private <REQ extends BaseDataDomain, RESP, T extends BaseDataDomain> List<T> fetchPaginatedData(
            AssetBaseDataRequest assetBaseDataRequest, 
            Boolean dataRule,
            Class<REQ> requestClass,
            Function<REQ, MTableDataInfo<List<RESP>>> dataService) {
        
        try {
            // 创建请求对象实例
            REQ request = requestClass.getDeclaredConstructor().newInstance();
            baseRequestHandle(assetBaseDataRequest, request, dataRule);
            // 获取第一页数据（带重试）
            MTableDataInfo<List<RESP>> firstPageResponse = fetchPageWithRetry(request, dataService, 1);

            if (firstPageResponse == null || firstPageResponse.getRows() == null || firstPageResponse.getRows().isEmpty()) {
                return new ArrayList<>();
            }
            
            List<RESP> allResults = new ArrayList<>(firstPageResponse.getRows());
            
            // 获取总记录数并计算总页数
            long total = firstPageResponse.getTotal();
            int totalPages = (int) Math.ceil((double) total / PAGE_SIZE);
            
            // 如果只有一页，直接返回
            if (totalPages <= 1) {
                return castList(allResults);
            }
            
            // 获取剩余页的数据
            for (int pageNum = 2; pageNum <= totalPages; pageNum++) {
                // 为每一页创建新的请求对象
                REQ pageRequest = requestClass.getDeclaredConstructor().newInstance();
                baseRequestHandle(assetBaseDataRequest, pageRequest, dataRule);
                pageRequest.setPageNum(pageNum);

                // 获取当前页数据（带重试）
                MTableDataInfo<List<RESP>> pageResponse = fetchPageWithRetry(pageRequest, dataService, pageNum);

                if (pageResponse != null && pageResponse.getRows() != null && !pageResponse.getRows().isEmpty()) {
                    // 直接添加数据，不进行重复检测
                    allResults.addAll(pageResponse.getRows());
                }
            }
            
            return castList(allResults);
        } catch (Exception e) {
            log.error("分页获取数据失败: ", e);
            throw new RuntimeException("分页获取数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 带重试机制的单页数据获取方法
     * 
     * @param <REQ> 请求类型
     * @param <RESP> 响应数据类型
     * @param request 请求对象
     * @param dataService 数据服务函数
     * @param pageNum 当前页码（仅用于日志记录）
     * @return 分页数据响应
     */
    private <REQ extends BaseDataDomain, RESP> MTableDataInfo<List<RESP>> fetchPageWithRetry(
            REQ request,
            Function<REQ, MTableDataInfo<List<RESP>>> dataService,
            int pageNum) {
            
        int maxRetries = 3;
        int attempt = 0;
        Exception lastException = null;
        
        while (attempt < maxRetries) {
            try {
                return dataService.apply(request);
            } catch (Exception e) {
                lastException = e;
                log.error("获取第{}页数据失败，正在重试...(尝试{}/{})", pageNum, attempt + 1, maxRetries, e);
                attempt++;
            }
        }
        
        // 连续三次失败，抛出异常
        String errorMsg = String.format("获取第%d页资产信息失败，重试%d次后仍然失败", pageNum, maxRetries);
        log.error(errorMsg, lastException);
        throw new RuntimeException(errorMsg, lastException);
    }

    private <T extends BaseDataDomain> void baseRequestHandle(AssetBaseDataRequest assetBaseDataRequest, T request, Boolean dataRule) {
        // 设置分页参数
        request.setPageNum(1);
        //
        request.setIfDataRule(dataRule);
        BeanUtils.copyProperties(assetBaseDataRequest, request);
        if (assetBaseDataRequest.getIds() != null) {
            request.setPageSize(assetBaseDataRequest.getIds().size());
        } else {
            request.setPageSize(PAGE_SIZE);
        }

    }


    /**
     * 把获取的selectBaseDataResponseByAssetIds的id提取出来返回List<String>
     */
    public List<String> getBaseDataResponseId(AssetBaseDataRequest assetBaseDataRequest) {
        return this.selectBaseDataResponseByAssetIds(assetBaseDataRequest).stream().map(BaseDataCache::getAssetId).collect(Collectors.toList());
    }


    /**
     * 根据资产id查询 BridgeStaticResponse 会失败重试3次
     *
     * @param assetBaseDataRequest 查询条件
     * @return List 基础数据
     */
    @SuppressWarnings("unchecked")
    @Override
    public <T extends BaseDataCache> List<T> selectBaseDataResponseByAssetIds(
            AssetBaseDataRequest assetBaseDataRequest) {
        return this.selectCache(assetBaseDataRequest,
                (Class<T>) ASSET_CACHE_MAP.get(assetBaseDataRequest.getAssetType()));
    }

    /**
     * 根据条件查询资产信息
     */
    @SuppressWarnings("unchecked")
    @Override
    public <T extends BaseDataCache> List<T> selectBaseDataResponseByAssetIds(
            AssetBaseDataRequest assetBaseDataRequest, Long PageNum, Long PageSize) {
        return this.selectCache(assetBaseDataRequest,
                (Class<T>) ASSET_CACHE_MAP.get(assetBaseDataRequest.getAssetType()), PageNum, PageSize);
    }

    /**
     * 获取分页后的资产id列表
     *
     * @param request  查询请求参数(不包含经纬度)
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param total    返回总记录数
     * @return 资产id列表
     */
    @Override
    public List<String> getBaseDataResponseIdByPage(
            AssetBaseDataRequest request, Long pageNum, Long pageSize, AtomicInteger total) {

        // 记录开始时间并查询缓存数据(带分页)
        List<BaseDataCache> baseDataList = this.selectBaseDataResponseByAssetIds(request,
                pageNum, pageSize);
        // 设置总记录数(从缓存服务获取)
        total.set(this.getTotalCount(request));
        // 检查结果是否为空
        if (baseDataList == null || baseDataList.isEmpty()) {
            return new ArrayList<>();
        }
        return baseDataList.stream().map(BaseDataCache::getAssetId).collect(Collectors.toList());
    }

    /**
     * 从List<BaseDataDomainWithDistance<T>>中提取List<T> baseData
     */
    @Override
    public <T extends BaseDataCache> List<T> extractBaseData(List<BaseDataDomainWithDistance<T>> baseDataList) {
        return baseDataList.stream()
                .map(BaseDataDomainWithDistance::getBaseData)
                .collect(Collectors.toList());
    }


    /**
     * 根据查询条件查询资产列表。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 本方法是资产查询的核心入口，根据请求参数（特别是是否包含经纬度）决定查询策略，
     * 支持按距离排序、检查状态过滤、分页等功能。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li>验证输入参数 {@code assetBaseDataRequest} 的有效性，特别是检查类型不能为空，并为检查时间设置默认值（当前时间）。</li>
     *   <li>判断请求中是否提供了经纬度信息 ({@code request.getLongitude()} 和 {@code request.getLatitude()} 都不为空)。</li>
     *   <li>如果提供了经纬度，则调用 {@link #listByDistance} 方法，该方法会计算每个资产到指定点的距离，并默认按距离升序排序（支持Top-K优化）。</li>
     *   <li>如果没有提供经纬度，则调用 {@link #listByNoDistance} 方法，该方法仅执行过滤和基本分页，不计算距离，不按距离排序。</li>
     *   <li>两个分支方法都会进行数据过滤、转换和分页处理，并通过 {@code total} 参数返回符合条件的总记录数。</li>
     * </ol>
     * </p>
     *
     * <p>
     * <strong>策略:</strong>
     * <ul>
     *   <li><strong>查询策略分离:</strong> 根据有无经纬度分为两种查询路径，优化性能。带距离的查询计算成本较高。</li>
     *   <li><strong>参数校验与默认值:</strong> 保证核心参数存在，提供默认时间戳，增强接口健壮性。</li>
     *   <li><strong>输出型参数:</strong> 使用 {@link AtomicInteger} 作为输出参数返回总数，避免额外查询总数。</li>
     * </ul>
     * </p>
     *
     * <p>
     * <strong>示例 (概念性):</strong>
     * <pre>{@code
     * AssetBaseDataRequest request = new AssetBaseDataRequest();
     * request.setType(someInspectionType);
     * request.setLatitude(30.0);
     * request.setLongitude(120.0);
     * request.setIsCheck(true); // 查询已检查的
     *
     * AtomicInteger total = new AtomicInteger();
     * Long pageNum = 1L;
     * Long pageSize = 10L;
     *
     * List<BaseDataDomainWithDistance<YourAssetData>> assets = assetQueryService.listBy(request, pageNum, pageSize, total);
     * // assets 列表将包含距离用户点(120.0, 30.0)最近的10个已检查资产
     * // total.get() 将返回所有符合条件的已检查资产总数
     * }</pre>
     * </p>
     *
     * @param assetBaseDataRequest 查询请求参数对象，包含检查类型、资产类型、经纬度、检查状态、自定义排序等条件。
     * @param pageNum              请求的页码，从1开始。如果为null或小于等于0，则不进行分页，返回所有符合条件的记录（经过Top-K处理或全量排序后）。
     * @param pageSize             每页显示的记录数。如果为null或小于等于0，则不进行分页。
     * @param total                用于返回查询结果总数的原子整数对象（输出型参数）。调用者应传入一个 {@code AtomicInteger} 实例，方法执行后其值会被设置为符合条件的总记录数。如果传入null，方法内部会创建一个实例，但调用者无法获取总数。
     * @param <T>                  资产数据的具体类型，必须是 {@link BaseDataCache} 的子类。
     * @return 返回一个包含资产数据及其附加信息（如距离、检查状态）的列表 ({@code List<BaseDataDomainWithDistance<T>>})。列表内容根据是否分页和排序策略决定。
     * @throws IllegalArgumentException 如果 {@code assetBaseDataRequest} 中的检查类型（type 和 assetType 都为 null）时抛出。
     */
    public <T extends BaseDataCache> List<BaseDataDomainWithDistance<T>> listBy(
            AssetBaseDataRequest assetBaseDataRequest, Long pageNum, Long pageSize, AtomicInteger total) {
        // 1. 验证必要参数 & 设置默认值
        validateAndPrepareRequest(assetBaseDataRequest);
        // 如果调用者未提供total对象，内部创建一个，主要用于方法内部逻辑，调用者无法拿到总数
        if (total == null) {
            total = new AtomicInteger();
        }
        // 2. 根据是否提供经纬度选择不同的查询策略
        if (isGeoQuery(assetBaseDataRequest)) {
            // 调用包含距离计算和排序的查询方法
            return listByDistance(assetBaseDataRequest, pageNum, pageSize, total);
        } else {
            // 调用不包含距离计算的查询方法
            return listByNoDistance(assetBaseDataRequest, pageNum, pageSize, total);
        }
    }
    /**
     * 验证请求参数的有效性，并设置必要的默认值。
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li>检查 {@code request.getType()} 和 {@code request.getAssetType()} 是否都为 null。如果是，则抛出 {@link IllegalArgumentException}，因为至少需要一个类型信息来确定查询范围或检查逻辑。</li>
     *   <li>检查 {@code request.getCheckTimeHas()} 是否为 null。如果是，则将其设置为当前系统时间 ({@code LocalDateTime.now()})。这个时间通常用于确定检查记录的时间点。</li>
     * </ol>
     * </p>
     *
     * @param request 资产查询请求对象。
     * @throws IllegalArgumentException 当检查类型（type 和 assetType）都未指定时抛出。
     */
    private void validateAndPrepareRequest(AssetBaseDataRequest request) {
        // 检查类型和资产类型至少需要提供一个
        if (Objects.isNull(request.getType()) && Objects.isNull(request.getAssetType())) {
            throw new IllegalArgumentException("检查类型不能为空"); // 抛出异常，因为无法确定检查范围或逻辑
        }
        // 如果请求中没有指定检查时间点，则默认使用当前时间
        if (Objects.isNull(request.getCheckTimeHas())) {
            request.setCheckTimeHas(LocalDateTime.now()); // 设置默认检查时间
        }
    }
    /**
     * 判断查询请求是否包含有效的地理位置信息（经度和纬度）。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 用于决策是否需要执行基于地理位置的查询（例如计算距离、按距离排序）。
     * </p>
     *
     * @param request 资产查询请求对象。
     * @return 如果请求对象中的经度 ({@code getLongitude()}) 和纬度 ({@code getLatitude()}) 字段都不为 null，则返回 {@code true}；否则返回 {@code false}。
     */
    private boolean isGeoQuery(AssetBaseDataRequest request) {
        // 检查经度和纬度是否都已提供
        return request.getLongitude() != null && request.getLatitude() != null;
    }
    /**
     * 执行不计算距离的资产查询。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 当查询请求不包含经纬度时，调用此方法。它主要负责从数据源（可能是缓存或数据库）获取基础数据，
     * 然后进行过滤、转换和分页处理。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li>记录查询开始时间。</li>
     *   <li>调用 {@code selectBaseDataResponseByAssetIds} (这是一个假设的方法，根据上下文推断其作用是获取基础资产数据列表)。</li>
     *   <li>检查返回的数据列表是否为空。如果为空，设置总数为0并返回空列表。</li>
     *   <li>记录本地缓存（或数据源）查询耗时。</li>
     *   <li>调用 {@link #calculateLimit} 计算潜在的 Top-K 限制（虽然在此方法中不直接用于排序，但可能被下游 {@code processAssetData} 用于某些过滤逻辑，或者只是保持接口一致性）。</li>
     *   <li>调用 {@link #processAssetData} 对获取到的原始数据进行处理：
     *      <ul>
     *          <li>过滤：根据请求中的检查状态 ({@code isCheck}) 或其他条件筛选数据。</li>
     *          <li>转换：将原始数据 ({@code T extends BaseDataCache}) 包装成 {@link BaseDataDomainWithDistance} 对象，即使没有距离，其他信息（如检查状态）也需要填充。</li>
     *          <li>排序：在此方法中通常不按距离排序，可能按其他默认或自定义规则排序（如果 {@code processAssetData} 支持）。</li>
     *      </ul>
     *      同时，{@code processAssetData} 会更新 {@code total} 参数的值为过滤后的总记录数。
     *   </li>
     *   <li>调用 {@link #paginate} 对处理后的列表进行分页，根据传入的 {@code pageNum} 和 {@code pageSize} 返回指定页的数据。</li>
     * </ol>
     * </p>
     *
     * @param request  查询请求参数对象。
     * @param pageNum  请求的页码。
     * @param pageSize 每页大小。
     * @param total    用于返回过滤后总记录数的原子整数对象。
     * @param <T>      资产数据的具体类型。
     * @return 返回经过过滤、转换和分页处理的资产列表 ({@code List<BaseDataDomainWithDistance<T>>})。这些对象中的 distance 字段通常为 null。
     */
    private <T extends BaseDataCache> List<BaseDataDomainWithDistance<T>> listByNoDistance(
            AssetBaseDataRequest request, Long pageNum, Long pageSize, AtomicInteger total) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        // 假设此方法从缓存或DB获取与请求相关的原始资产数据列表
        List<T> baseDataList = selectBaseDataResponseByAssetIds(request);
        // 如果没有获取到任何数据，直接返回空列表
        if (baseDataList == null || baseDataList.isEmpty()) {
            total.set(0); // 设置总数为0
            return new ArrayList<>(); // 返回空列表
        }
        log.info("本地缓存查询时间：{}ms", System.currentTimeMillis() - startTime); // 记录数据获取耗时
        // 计算分页处理前可能需要的最大记录数（主要用于Top-K，此处可能影响不大但保持流程一致）
        Integer limit = calculateLimit(pageNum, pageSize);
        // 对原始数据进行过滤、转换和可能的非距离排序，并更新total计数
        List<BaseDataDomainWithDistance<T>> processedList = processAssetData(baseDataList, request, limit, total);
        // 对处理后的完整列表进行分页
        return paginate(processedList, pageNum, pageSize, total.get());
    }
    /**
     * 执行计算距离并按距离排序（或使用 Top-K 优化）的资产查询。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 当查询请求包含经纬度时调用此方法。它不仅执行过滤和转换，还会计算每个资产到指定点的距离，
     * 并默认按距离升序对结果进行排序。如果请求的分页范围较小，可能会启用 Top-K 优化来提高性能。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li>记录查询开始时间。</li>
     *   <li>调用 {@code selectBaseDataResponseByAssetIds} 获取基础资产数据列表。</li>
     *   <li>检查返回的数据列表是否为空。如果为空，设置总数为0并返回空列表。</li>
     *   <li>记录数据获取耗时。</li>
     *   <li>调用 {@link #calculateLimit} 计算需要处理的最大记录数（{@code pageNum * pageSize}）。这对于 Top-K 优化至关重要，因为它确定了需要保留的最优元素的数量。</li>
     *   <li>记录数据处理开始时间。</li>
     *   <li>调用 {@link #processAssetData} 对获取到的原始数据进行处理：
     *      <ul>
     *          <li>过滤：根据请求条件筛选。</li>
     *          <li>计算距离：对每个符合条件的资产计算其与请求中经纬度的距离。</li>
     *          <li>转换：包装成 {@link BaseDataDomainWithDistance} 对象，包含距离信息。</li>
     *          <li>排序/Top-K：默认按距离升序排序。如果 {@code limit} 值相对于总数据量较小（满足特定阈值），则可能使用 {@link #topKSelect} 算法（基于最小堆）直接选出前 K 个最近的记录，避免对整个列表排序。否则，对整个列表进行排序。</li>
     *      </ul>
     *      同时，{@code processAssetData} 会更新 {@code total} 参数的值为过滤后的总记录数。
     *   </li>
     *   <li>记录数据处理耗时。</li>
     *   <li>根据分页参数 {@code pageNum} 和 {@code pageSize} 对 **已经排序或经过 Top-K 选择** 的列表进行手动分页 (使用 {@code subList})。
     *      <ul>
     *          <li>如果分页参数无效（null 或 <= 0），则返回处理后的全部结果（可能是 Top-K 结果或全量排序结果）。</li>
     *          <li>计算分页的起始索引 ({@code fromIndex}) 和结束索引 ({@code toIndex})。</li>
     *          <li>检查计算出的分页范围是否有效（起始索引不能超出列表大小或总记录数）。如果无效，返回空列表。</li>
     *          <li>调整结束索引，确保不超过列表实际大小和过滤后的总记录数。</li>
     *          <li>记录详细的耗时和分页信息。</li>
     *          <li>返回计算出的子列表 ({@code processedList.subList(fromIndex, toIndex)})。</li>
     *      </ul>
     *   </li>
     * </ol>
     * </p>
     *
     * <p>
     * <strong>策略:</strong>
     * <ul>
     *   <li><strong>距离计算与排序:</strong> 核心功能是处理地理位置查询，计算距离并排序。</li>
     *   <li><strong>Top-K 优化:</strong> 当用户只需要结果集的前几页时，使用 Top-K 算法（如最小堆）可以显著减少排序开销，特别是对于大数据集。</li>
     *   <li><strong>手动分页:</strong> 在排序或 Top-K 处理完成后进行手动分页，确保返回的是全局排序后的正确页面。</li>
     *   <li><strong>性能日志:</strong> 记录数据获取和处理的耗时，有助于性能分析。</li>
     * </ul>
     * </p>
     *
     * @param request  查询请求参数对象，必须包含有效的经纬度。
     * @param pageNum  请求的页码。
     * @param pageSize 每页大小。
     * @param total    用于返回过滤后总记录数的原子整数对象。
     * @param <T>      资产数据的具体类型。
     * @return 返回经过过滤、距离计算、排序（或Top-K选择）和分页处理的资产列表 ({@code List<BaseDataDomainWithDistance<T>>})。列表默认按距离升序排列。
     */
    public <T extends BaseDataCache> List<BaseDataDomainWithDistance<T>> listByDistance(
            AssetBaseDataRequest request, Long pageNum, Long pageSize, AtomicInteger total) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        // 假设此方法从缓存或DB获取与请求相关的原始资产数据列表
        List<T> baseDataList = selectBaseDataResponseByAssetIds(request);
        // 如果没有获取到任何数据，直接返回空列表
        if (baseDataList == null || baseDataList.isEmpty()) {
            total.set(0); // 设置总数为0
            return Collections.emptyList(); // 返回空列表
        }
        long cacheQueryTime = System.currentTimeMillis() - startTime; // 计算数据获取耗时
        // 计算需要获取的记录上限 (pageNum * pageSize)，用于Top-K优化判断
        Integer limit = calculateLimit(pageNum, pageSize);
        long processStartTime = System.currentTimeMillis(); // 记录处理开始时间
        // 对原始数据进行过滤、计算距离、转换、排序（或Top-K）处理，并更新total计数
        List<BaseDataDomainWithDistance<T>> processedList = processAssetData(baseDataList, request, limit, total);
        long processTime = System.currentTimeMillis() - processStartTime; // 计算处理耗时
        // 如果分页参数无效，则直接返回处理后的所有结果（可能已是Top-K或全量排序结果）
        if (pageNum == null || pageSize == null || pageNum <= 0 || pageSize <= 0) {
            log.info("缓存查询时间：{}ms, 数据处理时间：{}ms (未分页)", cacheQueryTime, processTime);
            return processedList;
        }
        int totalCount = total.get(); // 获取过滤后的总记录数
        // 计算分页的起始索引（基于0）
        int fromIndex = (int) ((pageNum - 1) * pageSize);
        // 如果请求的页码超出了实际数据范围（或总数范围），返回空列表
        if (fromIndex >= processedList.size() || fromIndex >= totalCount) {
            log.info("缓存查询时间：{}ms, 数据处理时间：{}ms (分页范围超出)", cacheQueryTime, processTime);
            return Collections.emptyList(); // 返回空列表
        }
        // 计算分页的结束索引，不能超过列表实际大小
        int toIndex = Math.min(fromIndex + pageSize.intValue(), processedList.size());
        // 同时，结束索引也不能超过过滤后的总记录数（虽然理论上processedList.size() <= totalCount，但多一层保护）
        toIndex = Math.min(toIndex, totalCount);
        log.info("缓存查询时间：{}ms, 数据处理时间：{}ms (分页: {}/{}, 结果索引: {}-{})",
                cacheQueryTime, processTime, pageNum, pageSize, fromIndex, toIndex);
        // 返回手动分页后的子列表
        return processedList.subList(fromIndex, toIndex);
    }
    /**
     * 计算进行 Top-K 优化时需要获取的记录数上限。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 当进行分页查询时（特别是按距离排序的查询），我们实际上只需要获取到用户请求的最后一页的最后一条记录即可（即第 `pageNum * pageSize` 条记录）。
     * 这个值可以作为 Top-K 算法中的 'K' 值，或者作为全量排序后截取的数量，用于优化处理流程。
     * </p>
     *
     * @param pageNum  请求的页码。
     * @param pageSize 每页大小。
     * @return 如果 {@code pageNum} 和 {@code pageSize} 都是有效的正数，则返回 {@code pageNum * pageSize} 的结果（转换为 int 类型）；否则返回 {@code null}，表示不需要或无法计算限制（例如，当不分页时）。
     */
    private Integer calculateLimit(Long pageNum, Long pageSize) {
        // 检查页码和每页大小是否都有效（大于0）
        if (pageNum != null && pageSize != null && pageNum > 0 && pageSize > 0) {
            // 计算需要获取的总记录数上限
            return (int) (pageNum * pageSize);
        }
        // 如果分页参数无效，返回null表示没有限制
        return null;
    }
    /**
     * 对给定的列表进行通用分页处理。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 这是一个辅助方法，用于从一个（通常是已经处理好、可能已排序的）完整列表中提取指定页的数据。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li>检查分页参数 {@code pageNum} 和 {@code pageSize} 是否有效（不为 null 且大于 0）。如果无效，直接返回原始列表，表示不分页。</li>
     *   <li>计算分页的起始索引 ({@code start})，基于0。</li>
     *   <li>检查计算出的起始索引是否超出了列表的实际大小。如果是，说明请求的页码过大，没有数据，返回空列表。</li>
     *   <li>计算分页的结束索引 ({@code end})。使用 {@code Math.min}确保结束索引不会超过列表的实际大小。</li>
     *   <li>使用 {@link List#subList(int, int)} 方法从原始列表中提取从 {@code start}（包含）到 {@code end}（不包含）的子列表。</li>
     *   <li>返回提取出的子列表。</li>
     * </ol>
     * </p>
     * <p>
     * <strong>注意:</strong> 此方法不依赖传入的 {@code totalCount} 参数进行计算，仅用于接口签名兼容或未来扩展。分页完全基于输入列表 {@code list} 的大小。
     * </p>
     *
     * @param list       需要进行分页的源列表。
     * @param pageNum    请求的页码。
     * @param pageSize   每页大小。
     * @param totalCount 列表的总记录数（当前实现未使用）。
     * @param <T>        列表元素的类型。
     * @return 返回分页后的子列表。如果分页参数无效，返回原始列表。如果请求的页码超出范围，返回空列表。
     */
    private <T> List<T> paginate(List<T> list, Long pageNum, Long pageSize, int totalCount) {
        // 如果分页参数无效，则不进行分页，返回原列表
        if (pageNum == null || pageSize == null || pageNum <= 0 || pageSize <= 0) {
            return list;
        }
        // 计算起始索引（0-based）
        int start = (int) ((pageNum - 1) * pageSize);
        // 如果起始索引已经超出了列表范围，说明请求页无数据
        if (start >= list.size()) {
            return Collections.emptyList(); // 返回空列表
        }
        // 计算结束索引，不能超过列表实际大小
        int end = Math.min(start + pageSize.intValue(), list.size());
        // 返回计算出的子列表
        return list.subList(start, end);
    }
    /**
     * 处理资产数据，包括过滤、转换和排序/Top-K优化。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 这是数据处理的核心协调方法。它接收原始数据列表，并根据请求参数执行一系列操作，
     * 最终返回一个准备好进行分页（或已经完成Top-K选择）的列表。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li><strong>数据过滤 (调用 {@link #filterAssetData}):</strong>
     *      <ul>
     *          <li>根据请求中的 {@code isCheck} 参数（是否查询已检查/未检查资产）进行过滤。</li>
     *          <li>如果未指定 {@code isCheck}，可能只进行基础去重（基于业务主键，如 {@code BaseDataCache#getId()}）。</li>
     *          <li>过滤操作会考虑检查类型 ({@code type})、检查时间点 ({@code checkTimeHas}) 和检查状态 ({@code status})。</li>
     *      </ul>
     *   </li>
     *   <li><strong>更新总数:</strong> 将过滤后的列表大小设置到 {@code total} 原子整数中。</li>
     *   <li><strong>转换与排序/Top-K (调用 {@link #convertAndSortAssetData}):</strong>
     *      <ul>
     *          <li>将过滤后的原始数据对象 ({@code T extends BaseDataCache}) 转换为包含附加信息的 {@link BaseDataDomainWithDistance} 对象。</li>
     *          <li>如果请求包含经纬度，计算每个对象的距离。</li>
     *          <li>根据需要确定每个对象的检查状态 ({@code isChecked} 字段)。如果过滤阶段已经确定，直接使用；否则可能需要再次查询检查记录（特别是当 {@code isCheck} 为 null 时）。</li>
     *          <li>如果请求包含经纬度且没有自定义排序，则按计算出的距离进行排序。根据 {@code limit} 参数和数据量大小，可能会触发 Top-K 优化（使用 {@link #topKSelect}）或进行全量排序。</li>
     *          <li>如果请求不含经纬度或有自定义排序，则不按距离排序（可能按其他字段排序，如果 {@code convertAndSortAssetData} 支持）。</li>
     *      </ul>
     *   </li>
     *   <li>返回经过转换和排序/Top-K处理后的列表。</li>
     * </ol>
     * </p>
     *
     * @param baseDataList 原始的资产数据列表（通常来自缓存或数据库查询）。
     * @param request      查询请求参数对象。
     * @param limit        Top-K 优化的限制数量（通常是 {@code pageNum * pageSize}），如果为 null 则不启用或无意义。
     * @param total        用于返回过滤后总记录数的原子整数对象。
     * @param <T>          资产数据的具体类型。
     * @return 返回处理后的资产列表 ({@code List<BaseDataDomainWithDistance<T>>})，准备好进行最终分页。
     */
    private <T extends BaseDataCache> List<BaseDataDomainWithDistance<T>> processAssetData(
            List<T> baseDataList, AssetBaseDataRequest request, Integer limit, AtomicInteger total) {
        // 1. 过滤处理（调用专门的过滤方法）
        List<T> filteredList = filterAssetData(baseDataList, request);
        // 更新总记录数（过滤后的数量）
        total.set(filteredList.size());
        // 2. 转换 & 排序（或启用 Top-K 优化）
        return convertAndSortAssetData(filteredList, request, limit);
    }
    /**
     * 根据请求条件过滤原始资产数据列表。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 此方法负责根据查询请求中的检查状态 ({@code isCheck}) 来筛选输入的资产列表。
     * 如果指定了检查状态，它会查询实际的检查记录来判断每个资产是否满足条件。
     * 如果未指定检查状态，它主要执行去重操作。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ul>
     *   <li>获取请求中的 {@code isCheck}（是否检查）、{@code type}（检查类型）、{@code checkTimeHas}（检查时间点）、{@code status}（检查状态）参数。</li>
     *   <li>创建一个 {@link ConcurrentHashMap} ({@code assetIdMap})，用于在后续查询检查记录时，可能需要存储资产ID到检查记录ID的映射（例如，用于获取非完成状态的检查ID）。</li>
     *   <li><strong>如果 {@code reqIsCheck} 不为 null:</strong>
     *      <ul>
     *          <li>调用 {@link #processChecked} 方法。该方法会:
     *              a. 查询指定类型、时间点、状态下已检查的资产ID集合 (通过调用 {@link #processCheckedIds})。
     *              b. 遍历输入的 {@code baseDataList}。
     *              c. 判断每个资产是否在已检查ID集合中。
     *              d. 根据 {@code reqIsCheck} 的值（{@code true} 或 {@code false}）保留满足条件的资产。
     *              e. 使用 {@link LinkedHashMap} 对结果进行去重（基于业务主键 {@code item.getId()}），并保持原始相对顺序。
     *          </li>
     *          <li>返回 {@code processChecked} 的结果。</li>
     *      </ul>
     *   </li>
     *   <li><strong>如果 {@code reqIsCheck} 为 null:</strong>
     *      <ul>
     *          <li>使用 Java Stream API 的 {@code distinct()} 方法对输入的 {@code baseDataList} 进行去重。注意：这里的 {@code distinct()} 依赖于 {@code T} 类型正确实现了 {@code equals()} 和 {@code hashCode()} 方法（可能基于资产ID或其他唯一标识）。</li>
     *          <li>返回去重后的列表。</li>
     *          <li>(注释中提到：若资产类型存在，可进一步调用 processCheckedIds 做记录查询。这暗示着即使 {@code isCheck} 为 null，后续的转换阶段 {@link #convertAndSortAssetData} 仍然可能需要查询检查状态来填充 {@code BaseDataDomainWithDistance} 对象的 {@code isChecked} 字段。)</li>
     *      </ul>
     *   </li>
     * </ul>
     * </p>
     *
     * @param baseDataList 原始的、未经过滤的资产数据列表。
     * @param request      查询请求参数对象。
     * @param <T>          资产数据的具体类型。
     * @return 返回根据检查状态过滤（如果需要）并可能去重后的资产列表。
     */
    private <T extends BaseDataCache> List<T> filterAssetData(List<T> baseDataList, AssetBaseDataRequest request) {
        // 获取请求中的相关参数
        Boolean reqIsCheck = request.getIsCheck(); // 是否按检查状态过滤
        InspectionType assetType = request.getType(); // 检查类型
        LocalDateTime checkTimeHas = request.getCheckTimeHas(); // 检查时间点
        Integer status = null;
        if (request.getStatus() != null) {
            status = request.getStatus().getCode(); // 检查状态
        }
        // 用于存储 assetId -> checkId 映射（如果需要）
        Map<String, String> assetIdMap = new ConcurrentHashMap<>();
        // 如果请求明确指定了查询已检查(true)或未检查(false)的资产
        if (reqIsCheck != null) {
            // 调用 processChecked 方法进行过滤和去重
            return processChecked(baseDataList, reqIsCheck, assetType, checkTimeHas, status, assetIdMap);
        } else {
            // 如果未指定 isCheck，则默认只进行去重处理
            // 注意: T 类型需要正确实现 equals/hashCode (通常基于 assetId 或 id)
            List<T> distinctList = baseDataList.stream().distinct().collect(Collectors.toList());
            // 注释提示：即使此处不去检查状态，后续转换步骤可能仍会查询检查状态
            return distinctList;
        }
    }
    /**
     * 将过滤后的资产数据列表转换为包含距离、检查状态等信息的包装对象列表，并根据需要进行排序（距离或桩号）或 Top-K 选择。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 此方法是数据处理流程的后半部分，负责将干净的资产数据 ({@code T}) 转换为更丰富的领域对象 ({@code BaseDataDomainWithDistance})，
     * 并应用排序逻辑。排序可以基于地理距离，也可以基于桩号，同时支持 Top-K 优化以提高性能。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li>提取请求中的相关参数：{@code isCheck}, {@code type}, {@code checkTimeHas}, {@code status}, 用户经纬度 ({@code userLat}, {@code userLon}), 以及是否按桩号排序 ({@code stakeSort})。</li>
     *   <li>判断是否需要计算距离 ({@code calculateDistance})：当用户提供了经纬度时为 true。</li>
     *   <li>判断是否需要排序 ({@code needSort})：当需要计算距离时，或者请求明确要求按桩号排序 ({@code stakeSort == true}) 时为 true。</li>
     *   <li>初始化一个空的 {@code ConcurrentHashMap} ({@code assetIdMap}) 用于存储查询到的检查记录ID（如果需要）。</li>
     *   <li><strong>查询检查状态 (如果需要):</strong>
     *      <ul>
     *          <li>如果请求中没有指定 {@code isCheck} (即 {@code reqIsCheck == null})，但指定了检查类型 ({@code assetType != null})，则意味着需要确定列表中每个资产的检查状态。</li>
     *          <li>提取过滤后列表 {@code filteredList} 中所有资产的 ID ({@code assetId})。</li>
     *          <li>调用 {@link #processCheckedIds} 并行查询这些资产 ID 在指定时间点 ({@code checkTimeHas}) 和状态 ({@code status}) 下是否已完成检查。查询结果是一个包含已完成检查的资产 ID 的集合 ({@code checkedIdsSet})。同时，{@code processCheckedIds} 内部可能会填充 {@code assetIdMap}。</li>
     *      </ul>
     *   </li>
     *   <li>使用 Final 变量保存查询到的 {@code checkedIdsSet} 以便在 Lambda 表达式中使用。</li>
     *   <li><strong>创建处理流 (Stream):</strong>
     *      <ul>
     *          <li>根据过滤后列表的大小 ({@code filteredList.size()}) 与并行阈值 ({@code PARALLEL_STREAM_THRESHOLD}) 的比较，决定使用并行流 ({@code parallelStream()}) 还是串行流 ({@code stream()}) 来处理数据。</li>
     *      </ul>
     *   </li>
     *   <li><strong>映射 (Map) 操作:</strong>
     *      <ul>
     *          <li>对流中的每个资产项 ({@code item}) 执行转换操作：</li>
     *          <li>如果需要计算距离 ({@code calculateDistance})，调用 {@link #computeDistance} 计算用户位置与资产位置之间的距离。</li>
     *          <li>确定资产的检查状态 ({@code itemIsChecked})：基于请求参数 {@code reqIsCheck} 或查询到的 {@code finalCheckedIdsSet}。</li>
     *          <li>从 {@code assetIdMap} 中获取可能存在的检查记录 ID ({@code assetCheckId})。</li>
     *          <li>创建一个新的 {@link BaseDataDomainWithDistance} 实例，包装原始数据、距离、检查状态、检查记录 ID 等信息。</li>
     *      </ul>
     *   </li>
     *   <li><strong>收集与排序/Top-K:</strong>
     *      <ul>
     *          <li><strong>如果需要排序 ({@code needSort} 为 true):</strong>
     *              a. 收集流中的所有 {@code BaseDataDomainWithDistance} 对象到一个临时列表 ({@code collectedList})。
     *              b. 调用 {@link #getBaseDataDomainWithDistanceComparator(Boolean)} 获取合适的比较器, 传入 {@code stakeSort} 参数决定是按桩号排序还是按距离排序。
     *              c. 判断是否启用 Top-K 优化：条件是 {@code limit} 有效且相对于列表大小足够小。
     *              d. 如果启用 Top-K ({@code useTopK is true})：调用 {@link #topKSelect} 方法，使用获取的比较器高效选出前 K 个元素，并返回已排序的结果列表。
     *              e. 如果不启用 Top-K：直接对 {@code collectedList} 使用 {@link List#sort} 和获取的比较器进行全量排序。
     *          </li>
     *          <li><strong>如果不需要排序 ({@code needSort} 为 false):</strong>
     *              a. 直接将流中的元素收集到一个列表 ({@code resultList}) 中，保持流处理后的顺序（可能非确定性，尤其在并行流下）。</li>
     *      </ul>
     *   </li>
     *   <li>返回最终处理好的列表 ({@code resultList})。</li>
     * </ol>
     * </p>
     *
     * @param filteredList 经过 {@link #filterAssetData} 处理后的资产列表。
     * @param request      查询请求参数对象，包含经纬度、检查状态、排序要求（如 stakeSort）等。
     * @param limit        用于 Top-K 优化的限制数量，或用于判断是否启用优化的依据。
     * @param <T>          资产数据的具体类型，必须继承自 {@link BaseDataCache}。
     * @return 返回包含距离、检查状态等信息，并可能按距离或桩号排序（或经过 Top-K 选择）的资产列表。
     */
    private <T extends BaseDataCache> List<BaseDataDomainWithDistance<T>> convertAndSortAssetData(
            List<T> filteredList, AssetBaseDataRequest request, Integer limit) {
        // 提取请求参数，设为 final 以便在 lambda 中使用
        final Boolean reqIsCheck = request.getIsCheck(); // 请求中指定的检查状态
        final InspectionType assetType = request.getType(); // 请求的资产/检查类型
        final LocalDateTime checkTimeHas = request.getCheckTimeHas(); // 请求的检查时间点
        final Integer status;
        if (request.getStatus() != null) {
            status = request.getStatus().getCode(); // 请求的检查记录状态
        } else {
            status = null;
        }
        final Double userLat = request.getLatitude(); // 用户纬度
        final Double userLon = request.getLongitude(); // 用户经度
        final Boolean stakeSort = request.getStakeSort(); // 是否按桩号排序的标志

        // 判断是否需要计算距离：提供了用户经纬度时为 true
        final boolean calculateDistance = (userLat != null && userLon != null);
        // 预存用户坐标值，避免在流处理中重复调用 getter
        final double userLatVal = calculateDistance ? userLat : 0d;
        final double userLonVal = calculateDistance ? userLon : 0d;

        // 判断是否需要排序：需要计算距离 或 请求了按桩号排序
        final boolean needSort = calculateDistance || (stakeSort != null && stakeSort);

        // 用于存储 assetId -> 检查记录ID 的映射，主要用于月度检查等场景
        final Map<String, String> assetIdMap = new ConcurrentHashMap<>();

        // 存储查询到的已检查资产ID集合
        Set<String> checkedIdsSet = null;
        // 如果请求未指定 isCheck 但指定了检查类型，则需要查询数据库来确定每个资产的检查状态
        if (reqIsCheck == null && assetType != null) {
            // 获取过滤后列表中的所有资产ID
            List<String> assetIds = filteredList.stream().map(T::getAssetId).collect(Collectors.toList());
            // 调用 processCheckedIds 并行查询这些 ID 的检查状态，结果存入 checkedIdsSet，并可能填充 assetIdMap
            checkedIdsSet = processCheckedIds(assetIds, assetType, checkTimeHas, status, assetIdMap, SHARED_EXECUTOR);
        }
        // 使用 final 变量以便在 lambda 表达式中捕获 checkedIdsSet
        final Set<String> finalCheckedIdsSet = checkedIdsSet;

        // 根据列表大小选择串行流或并行流以优化性能
        Stream<T> processingStream = filteredList.size() > PARALLEL_STREAM_THRESHOLD
                ? filteredList.parallelStream() // 数据量大时使用并行流
                : filteredList.stream(); // 数据量小时使用串行流

        // 将原始数据 T 映射（转换）为包含距离、检查状态等信息的 BaseDataDomainWithDistance<T> 对象流
        Stream<BaseDataDomainWithDistance<T>> domainStream = processingStream.map(item -> {
            // 如果需要计算距离，则调用 computeDistance 计算
            Double distance = calculateDistance ? computeDistance(userLatVal, userLonVal, item) : null;

            // 确定当前项的检查状态 (itemIsChecked)
            Boolean itemIsChecked;
            if (reqIsCheck != null) {
                // 如果请求中明确指定了 isCheck，则直接使用该值
                itemIsChecked = reqIsCheck;
            } else {
                // 否则，根据查询到的 checkedIdsSet 判断
                if (assetType == null || finalCheckedIdsSet == null) {
                    // 如果没有指定类型或未查询到检查记录，则检查状态未知 (null)
                    itemIsChecked = null;
                } else {
                    // 检查当前资产 ID 是否在已检查集合中
                    itemIsChecked = finalCheckedIdsSet.contains(item.getAssetId());
                }
            }

            // 从 assetIdMap 中获取可能存在的关联检查记录 ID
            String assetCheckId = assetIdMap.get(item.getAssetId());

            // 创建包含所有信息的包装对象 BaseDataDomainWithDistance
            return new BaseDataDomainWithDistance<>(
                    item, // 原始资产数据
                    distance, // 计算出的距离 (可能为 null)
                    itemIsChecked, // 确定的检查状态 (可能为 null)
                    assetCheckId, // 关联的检查记录 ID (可能为 null)
                    assetType, // 检查类型
                    checkTimeHas // 检查时间点
            );
        });

        List<BaseDataDomainWithDistance<T>> resultList;
        // 如果需要进行排序（距离或桩号）
        if (needSort) {
            // 判断是否满足 Top-K 优化条件：limit有效且远小于总数
            boolean useTopK = (limit != null && limit > 0 && limit < filteredList.size() * TOP_K_OPTIMIZATION_THRESHOLD_RATIO);

            // 收集流结果到列表（因为排序和Top-K都需要完整集合才能进行）
            List<BaseDataDomainWithDistance<T>> collectedList = domainStream.collect(Collectors.toList());

            // 获取比较器：传入 stakeSort 标志来决定是按桩号排序还是按距离排序
            Comparator<BaseDataDomainWithDistance<T>> cmp = getBaseDataDomainWithDistanceComparator(stakeSort);

            if (useTopK) {
                // 如果启用 Top-K 优化
                log.debug("启用 Top-K 优化，limit = {}", limit);
                // 使用 topKSelect 方法和获取的比较器，高效选出前 K 个元素，并已排序
                resultList = topKSelect(
                        collectedList,
                        limit,
                        cmp // 使用动态获取的比较器 (桩号或距离)
                );
            } else {
                // 如果不启用 Top-K，则对整个列表进行全量排序
                resultList = collectedList;
                // 使用获取的比较器（桩号或距离）对列表进行排序
                resultList.sort(cmp);
            }
        } else {
            // 如果不需要排序，直接将流中的元素收集到列表中
            resultList = domainStream.collect(Collectors.toList());
        }
        // 返回最终处理好的列表（可能已排序或经过Top-K选择）
        return resultList;
    }

    /**
     * 根据传入的 stakeSort 标志决定并返回用于排序 {@link BaseDataDomainWithDistance} 对象的比较器。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 该方法封装了排序逻辑的选择。它可以根据传入的布尔值 {@code stakeSort} 是 true 还是 false/null，来生成按桩号排序或按距离排序的 {@link Comparator}。
     * </p>
     *
     * <p>
     * <strong>处理流程/策略:</strong>
     * <ol>
     *   <li>检查传入的布尔标志 {@code stakeSort}。</li>
     *   <li>如果 {@code stakeSort} 不为 null 且为 true，则创建一个按中心桩号 ({@code baseData.getCenterStake()}) 升序排列的比较器。使用 {@link Comparator#nullsLast(Comparator)} 确保没有桩号的项排在最后。</li>
     *   <li>否则（{@code stakeSort} 为 false 或 null），创建一个按距离 ({@code getDistance()}) 升序排列的比较器。同样使用 {@link Comparator#nullsLast(Comparator)} 确保没有计算距离的项（distance 为 null）排在最后。</li>
     *   <li>返回创建的比较器。</li>
     * </ol>
     * </p>
     *
     * <p>
     * <strong>示例:</strong><br>
     *   - 如果 `stakeSort` 为 `true`，则返回的比较器会优先比较两个对象的 `baseData.getCenterStake()` 值。
     *   - 如果 `stakeSort` 为 `false` 或 `null`，并且计算了距离，则返回的比较器会优先比较两个对象的 `distance` 值。
     * </p>
     *
     * @param stakeSort 一个布尔值，指示是否应按桩号排序。如果为 true，则按桩号排序；否则（为 false 或 null），按距离排序。
     * @param <T>       资产数据的具体类型，继承自 {@link BaseDataCache}。
     * @return 一个 {@link Comparator} 实例，用于对 {@link BaseDataDomainWithDistance} 对象列表进行排序。
     */
    private static <T extends BaseDataCache> Comparator<BaseDataDomainWithDistance<T>> getBaseDataDomainWithDistanceComparator(Boolean stakeSort) {
        Comparator<BaseDataDomainWithDistance<T>> cmp;
        // 检查 stakeSort 标志是否为 true
        if (stakeSort != null && stakeSort) {
            // 如果 stakeSort 为 true，创建按桩号升序排序的比较器
            cmp = Comparator.comparing(
                    // 提取比较键：基础数据中的中心桩号
                    o -> o.getBaseData().getCenterStake(),
                    // 定义比较规则：使用自然顺序比较，并将 null 值放在最后
                    Comparator.nullsLast(Comparator.naturalOrder())
            );
        } else {
            // 否则（stakeSort 为 false 或 null），默认创建按距离升序排序的比较器
            cmp = Comparator.comparing(
                    // 提取比较键：包装对象中的距离值
                    BaseDataDomainWithDistance::getDistance,
                    // 定义比较规则：使用自然顺序比较，并将 null 值（未计算距离的项）放在最后
                    Comparator.nullsLast(Comparator.naturalOrder())
            );
        }
        // 返回根据 stakeSort 标志选择的比较器
        return cmp;
    }

    /**
     * 根据指定的检查状态 ({@code isCheck}) 过滤资产列表，并进行去重。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 当用户明确要求查询已检查 ({@code isCheck=true}) 或未检查 ({@code isCheck=false}) 的资产时，此方法被调用。
     * 它通过查询检查记录来确定每个资产的状态，然后根据要求进行筛选，并确保结果中没有重复的资产（基于业务主键 {@code id}）。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li>提取输入列表 {@code baseDataList} 中所有资产的 ID ({@code assetId})。</li>
     *   <li>调用 {@link #processCheckedIds} 方法，传入资产 ID 列表、检查类型 ({@code type})、检查时间点 ({@code now})、检查状态 ({@code status}) 以及用于存储 ID 映射的 {@code assetIdMap}。此调用会并行查询数据库，获取在指定条件下已完成检查的资产 ID 集合 ({@code checkedIdsSet})。</li>
     *   <li>创建一个 {@link LinkedHashMap} ({@code uniqueMap})。使用 {@code LinkedHashMap} 的目的是在去重的同时保持元素插入的顺序（即原始列表中的相对顺序）。键是资产的业务主键 ({@code item.getId()})，值是资产对象本身 ({@code item})。</li>
     *   <li>遍历输入的原始资产列表 ({@code baseDataList})。</li>
     *   <li>对于列表中的每个资产项 ({@code item})：
     *      <ul>
     *          <li>检查其资产 ID ({@code item.getAssetId()}) 是否存在于之前查询到的 {@code checkedIdsSet} 中，得到一个布尔值 {@code hasChecked}。</li>
     *          <li>比较 {@code hasChecked} 的值与请求的检查状态 {@code isCheck} 是否相等。</li>
     *          <li>如果相等（即，请求查询已检查且该资产确实已检查，或者请求查询未检查且该资产确实未检查），则尝试将该资产添加到 {@code uniqueMap} 中。</li>
     *          <li>使用 {@link Map#putIfAbsent(Object, Object)} 方法添加。如果 map 中尚不存在以 {@code item.getId()} 为键的条目，则添加该条目；如果已存在，则不做任何操作。这确保了对于具有相同业务主键 {@code id} 的重复资产，只有第一次遇到的会被保留。</li>
     *      </ul>
     *   </li>
     *   <li>遍历结束后，{@code uniqueMap} 中包含了所有满足检查状态条件且不重复的资产记录，并保持了它们在原始列表中的相对顺序。</li>
     *   <li>将 {@code uniqueMap} 的值集合（即去重并过滤后的资产对象列表）转换为一个新的 {@link ArrayList} 并返回。</li>
     * </ol>
     * </p>
     *
     * @param baseDataList 原始的资产数据列表。
     * @param isCheck      布尔值，指示是查询已检查 (true) 还是未检查 (false) 的资产。
     * @param type         检查类型，用于 {@code processCheckedIds} 查询。
     * @param now          检查时间点，用于 {@code processCheckedIds} 查询。
     * @param status       检查状态，用于 {@code processCheckedIds} 查询。
     * @param assetIdMap   一个 Map，传递给 {@code processCheckedIds}，可能用于存储 assetId 到 checkId 的映射。
     * @param <T>          资产数据的具体类型。
     * @return 返回一个列表，包含满足 {@code isCheck} 条件且根据业务主键 {@code id} 去重后的资产对象。列表保持原始相对顺序。
     */
    private <T extends BaseDataCache> List<T> processChecked(List<T> baseDataList, Boolean isCheck,
                                                             InspectionType type, LocalDateTime now,
                                                             Integer status, Map<String, String> assetIdMap) {
        // 提取所有资产ID，用于查询检查状态
        List<String> assetIds = baseDataList.stream().map(T::getAssetId).collect(Collectors.toList());
        // 调用 processCheckedIds 查询指定条件下已完成检查的资产ID集合
        Set<String> checkedIdsSet = processCheckedIds(assetIds, type, now, status, assetIdMap, SHARED_EXECUTOR);
        // 使用 LinkedHashMap 进行去重，并保持原始相对顺序
        // Key: 资产的业务主键 (item.getId())， Value: 资产对象 (item)
        Map<String, T> uniqueMap = new LinkedHashMap<>();
        // 遍历原始列表
        for (T item : baseDataList) {
            // 判断当前资产是否在已检查集合中
            boolean hasChecked = checkedIdsSet.contains(item.getAssetId());
            // 如果资产的检查状态与请求的 isCheck 状态一致
            if (isCheck == hasChecked) {
                // 使用 putIfAbsent 添加到 map 中，实现基于业务主键 id 的去重
                // 如果 id 已存在，则不会覆盖，保留第一次出现的记录
                uniqueMap.putIfAbsent(item.getId(), item);
            }
        }
        // 返回 map 中所有值（即去重和过滤后的资产对象）组成的列表
        return new ArrayList<>(uniqueMap.values());
    }
    /**
     * 并行查询指定资产 ID 列表在特定时间点前是否已完成检查。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 该方法负责与数据库交互（通过 {@code patrolAssetCheckMapper}），查询一批资产 ID 的检查完成情况。
     * 为了提高效率，它使用了并行处理策略，将输入的 ID 列表分区，并为每个分区启动一个异步任务来执行数据库查询。
     * 它还会根据检查类型（日检或月检）选择不同的查询逻辑，并处理数据源切换（切换到从库）。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li><strong>前置检查:</strong> 检查输入的资产 ID 列表 ({@code ids})、检查类型 ({@code type}) 或检查时间点 ({@code now}) 是否为空。如果任一为空或 ID 列表为空，则无法执行查询，直接返回空集合。</li>
     *   <li><strong>分区:</strong> 调用 {@link #partitionList} 方法将输入的 {@code ids} 列表分割成多个较小的子列表（分区）。分区的数量和大小基于可用处理器核心数和预设的最小/最大分区大小，目的是平衡并行任务的开销和粒度。</li>
     *   <li><strong>创建并行任务:</strong> 对每个分区（子列表）执行以下操作：
     *      <ul>
     *          <li>使用 {@link java.util.concurrent.CompletableFuture#supplyAsync(java.util.function.Supplier, java.util.concurrent.Executor)} 创建一个异步任务，提交到共享的线程池 ({@code executor}) 中执行。</li>
     *          <li><strong>任务内部逻辑:</strong>
     *              a. <strong>设置数据源:</strong> 调用 {@code DynamicDataSourceContextHolder.push("slave")} 将当前线程的数据源切换到从库，以减轻主库压力。
     *              b. <strong>执行查询 (try-finally 块):</strong>
     *                 i.  根据检查类型 {@code type} 的标志 ({@code type.getFlag()}) 判断是日检还是月检：
     *                     - <strong>日检 ({@code type.getFlag() is true}):</strong> 调用 {@code patrolAssetCheckMapper.selectCheckedDaysAssetIds} 方法，传入当前分区 ID、检查日期 ({@code now.toLocalDate()})、状态、类型和表名。该方法预计返回已完成日检的资产 ID 列表。将返回的列表转换为 {@link HashSet}。
     *                     - <strong>月检 ({@code type.getFlag() is false}):</strong>
     *                        - 计算检查月份的第一天 ({@code yearMonth})。
     *                        - 根据资产类型 ({@code type.getAssetType()}) 进一步判断：
     *                           - 如果是设备类型 ({@code AssetType.DEVICE}): 调用 {@code patrolAssetCheckMapper.selectCheckedMonthAssetIdsByJD} (推测是针对特定设备类型的月检查询)，返回已完成月检的资产 ID 列表。转换为 {@link HashSet}。
     *                           - 如果是其他类型：调用 {@code patrolAssetCheckMapper.selectCheckedMonthAssetIds}，该方法返回 {@link PatrolAssetCheck} 对象列表，可能包含所有状态的检查记录。
     *                              - 遍历返回的 {@code PatrolAssetCheck} 记录。对于那些检查阶段 ({@code record.getStage()}) 不是 {@code StageType.COMPLETED} 的记录，将其资产 ID ({@code record.getAssetId()}) 和检查记录 ID ({@code record.getId()}) 存入传入的 {@code assetIdMap} 中。这用于后续可能需要获取未完成检查的详情。
     *                              - 使用 Stream API 过滤出检查阶段为 {@code StageType.COMPLETED} 的记录，提取它们的资产 ID ({@code PatrolAssetCheck::getAssetId})，并收集到一个 {@link HashSet} 中。
     *                 ii. 返回计算得到的已完成检查的资产 ID 集合 ({@code Set<String>})。
     *              c. <strong>清理数据源 (finally 块):</strong> 调用 {@code DynamicDataSourceContextHolder.clear()} 清除当前线程的数据源设置，恢复默认或避免影响后续操作。
     *          </li>
     *      </ul>
     *      将所有创建的 {@code CompletableFuture<Set<String>>} 对象收集到一个列表 ({@code futures}) 中。
     *   </li>
     *   <li><strong>等待所有任务完成:</strong> 调用 {@link CompletableFuture#allOf(CompletableFuture...)}{@code .join()}。这将阻塞当前线程，直到 {@code futures} 列表中的所有异步任务都执行完毕（无论是正常完成还是异常结束）。</li>
     *   <li><strong>合并结果:</strong> 使用 Stream API 遍历 {@code futures} 列表：
     *      <ul>
     *          <li>对每个 {@code CompletableFuture} 调用 {@code .join()} 获取其结果（即对应分区的已检查资产 ID 集合）。</li>
     *          <li>使用 {@link Stream#flatMap(Function)} 将所有分区的集合流合并成一个单一的资产 ID 流。</li>
     *          <li>将合并后的流收集到一个最终的 {@link HashSet} 中，这个集合包含了所有输入 ID 中已完成检查的资产 ID。</li>
     *      </ul>
     *   </li>
     *   <li>返回合并后的已检查资产 ID 集合。</li>
     * </ol>
     * </p>
     *
     * <p>
     * <strong>策略:</strong>
     * <ul>
     *   <li><strong>并行处理:</strong> 利用多核 CPU 并行执行数据库查询，加速对大量 ID 的检查状态获取。</li>
     *   <li><strong>数据源分离:</strong> 查询操作在从库执行，避免影响主库性能。</li>
     *   <li><strong>逻辑分离:</strong> 根据日检/月检及资产类型选择不同的 Mapper 方法。</li>
     *   <li><strong>状态映射:</strong> 对于月检，特殊处理未完成记录，将其信息存入 {@code assetIdMap}。</li>
     *   <li><strong>分区优化:</strong> 通过 {@link #partitionList} 动态调整分区大小，平衡并行开销。</li>
     * </ul>
     * </p>
     *
     * @param ids        需要查询检查状态的资产 ID 列表。
     * @param type       检查类型，决定查询逻辑（日/月）和关联的表。
     * @param now        检查时间点，用于确定查询的日期或月份。
     * @param status     检查状态，传递给 Mapper 查询。
     * @param assetIdMap 一个并发安全的 Map，用于收集非完成状态的月度检查记录的 assetId -> checkId 映射（输出型）。
     * @param executor   用于执行并行查询任务的线程池。
     * @return 返回一个包含所有在指定条件下已完成检查的资产 ID 的集合 ({@code Set<String>})。如果输入无效或没有符合条件的记录，则返回空集合。
     */
    private Set<String> processCheckedIds(List<String> ids, InspectionType type, LocalDateTime now,
                                          Integer status, Map<String, String> assetIdMap, ExecutorService executor) {
        // 基本参数校验，如果ids为空或类型、时间为空，则无法查询，返回空集
        if (ids == null || ids.isEmpty() || type == null || now == null) {
            return Collections.emptySet();
        }
        // 将ID列表分区，以便并行处理
        List<List<String>> partitions = partitionList(ids);
        // 为每个分区创建一个异步查询任务
        List<CompletableFuture<Set<String>>> futures = partitions.stream()
                .map(partition -> CompletableFuture.supplyAsync(() -> {
                    // 在异步任务中执行数据库查询
                    // 设置数据源为从库
                    DynamicDataSourceContextHolder.push("slave");
                    try {
                        // 根据检查类型标志判断是日检还是月检
                        if (type.getFlag()) { // 日检逻辑
                            LocalDate nowDate = now.toLocalDate();
                            // 调用Mapper查询指定日期完成检查的资产ID
                            List<String> list = patrolAssetCheckMapper.selectCheckedDaysAssetIds(
                                    partition, nowDate, status, type, type.getAssetType().getTableName());
                            // 将结果列表转换为Set
                            return new HashSet<>(list);
                        } else { // 月检逻辑
                            LocalDate yearMonth = now.toLocalDate().withDayOfMonth(1); // 获取检查月份的第一天
                            // 根据资产类型进一步区分月检查询逻辑
                            if (AssetType.DEVICE.equals(type.getAssetType())) { // 特定设备类型的月检查询
                                List<String> list = patrolAssetCheckMapper.selectCheckedMonthAssetIdsByJD(
                                        partition, yearMonth, status, type, type.getAssetType().getTableName());
                                return new HashSet<>(list);
                            } else { // 其他类型的通用月检查询
                                // 查询返回 PatrolAssetCheck 对象列表，可能包含非完成状态
                                List<PatrolAssetCheck> records = patrolAssetCheckMapper.selectCheckedMonthAssetIds(
                                        partition, yearMonth, status, type, type.getAssetType().getTableName());
                                // 遍历记录，将非完成状态的记录ID存入 assetIdMap
                                for (PatrolAssetCheck record : records) {
                                    if (record.getStage() != null && record.getStage() != StageType.COMPLETED) {
                                        // 使用 put （因为是 ConcurrentHashMap，线程安全）
                                        assetIdMap.put(record.getAssetId(), record.getId());
                                    }
                                }
                                // 过滤出已完成 (COMPLETED) 的记录，提取它们的 assetId，并收集到 Set 中
                                return records.stream()
                                        .filter(record -> record.getStage() != null && record.getStage() == StageType.COMPLETED)
                                        .map(PatrolAssetCheck::getAssetId)
                                        .collect(Collectors.toSet());
                            }
                        }
                    } finally {
                        // 无论查询成功与否，都要清除数据源设置
                        DynamicDataSourceContextHolder.clear();
                    }
                }, executor)) // 使用传入的线程池执行任务
                .collect(Collectors.toList()); // 将所有 CompletableFuture 收集到列表
        // 等待所有并行任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        // 合并所有任务的结果 (Set<String>) 到一个最终的 Set<String> 中
        return futures.stream()
                .flatMap(future -> future.join().stream()) // 使用 flatMap 将多个 Set 流合并成一个流
                .collect(Collectors.toSet()); // 收集最终结果到 Set
    }
    /**
     * 计算用户指定位置与资产项之间的地理距离。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 这是一个辅助方法，用于计算两个地理坐标点之间的球面距离（或称弧线距离）。
     * 它依赖于 {@link GeoUtils#arcDistance(double, double, double, double)} 方法来实现具体的计算逻辑。
     * </p>
     *
     * @param userLat 用户位置的纬度。
     * @param userLon 用户位置的经度。
     * @param item    包含地理坐标（纬度和经度）的资产数据对象 ({@code BaseDataCache} 或其子类)。
     * @return 返回计算出的距离（单位通常是米，取决于 {@code GeoUtils.arcDistance} 的实现）。如果资产项 ({@code item}) 的纬度或经度为 null，则无法计算距离，返回 {@code null}。
     */
    private Double computeDistance(double userLat, double userLon, BaseDataCache item) {
        // 获取资产的纬度和经度
        Double lat = item.getLatitude();
        Double lon = item.getLongitude();
        // 如果资产的经纬度信息不完整，则无法计算距离
        if (lat == null || lon == null) {
            return null; // 返回 null 表示距离未知
        }
        // 调用 GeoUtils 工具类计算两点间的弧线距离
        return GeoUtils.arcDistance(userLat, userLon, lat, lon);
    }
    /**
     * 使用 Top-K 选择算法（基于最大堆）从列表中选出按指定比较器排序的前 K 个元素。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 当需要从一个大列表中找出排序后最靠前的 K 个元素（例如，距离最近的 K 个点）时，
     * 使用 Top-K 算法通常比对整个列表进行排序然后取前 K 个更高效。
     * 此方法使用一个固定大小为 K 的最大堆来实现该策略。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li>创建一个优先队列 ({@link PriorityQueue}) 作为最大堆。
     *      <ul>
     *          <li>堆的大小限制为 {@code k}。</li>
     *          <li>堆的排序逻辑使用传入的比较器 {@code cmp} 的反向 ({@code cmp.reversed()})。这意味着堆顶元素将是当前堆中"最大"的元素（根据 {@code cmp} 的定义）。例如，如果 {@code cmp} 是升序比较器，则最大堆会保持 K 个"最大"的元素，堆顶是其中最大的。如果需要 Top-K 最小元素，传入升序比较器 {@code cmp}，则最大堆会保持 K 个最小的元素，堆顶是这 K 个中最大的。</li>
     *      </ul>
     *   </li>
     *   <li>遍历输入的列表 ({@code list}) 中的每一个元素 ({@code item})。</li>
     *   <li><strong>对于每个元素 {@code item}:</strong>
     *      <ul>
     *          <li><strong>如果堆的大小小于 K:</strong> 直接将 {@code item} 加入堆中 ({@code maxHeap.offer(item)})。</li>
     *          <li><strong>如果堆的大小等于 K:</strong>
     *              <ul>
     *                  <li>获取当前堆顶元素 ({@code maxHeap.peek()})。</li>
     *                  <li>使用原始比较器 {@code cmp} 比较 {@code item} 和堆顶元素。</li>
     *                  <li>如果 {@code item} 比堆顶元素"小"（即 {@code cmp.compare(item, maxHeap.peek()) < 0}），说明 {@code item} 比当前 K 个元素中"最大"的那个还要"小"，因此 {@code item} 有资格进入 Top-K 集合。</li>
     *                  <li>在这种情况下，先将堆顶元素移除 ({@code maxHeap.poll()})，然后将 {@code item} 加入堆中 ({@code maxHeap.offer(item)})。这样可以保持堆的大小为 K，并且始终包含当前遍历过的元素中"最小"的 K 个。</li>
     *              </ul>
     *          </li>
     *      </ul>
     *   </li>
     *   <li>遍历结束后，最大堆 {@code maxHeap} 中包含了原始列表中按 {@code cmp} 排序的 Top-K 个元素。但是，它们在堆中的顺序不是最终需要的顺序（堆只保证堆顶是"最大"的）。</li>
     *   <li>将堆中的所有元素转移到一个新的 {@link ArrayList} ({@code topKList}) 中。</li>
     *   <li>使用原始比较器 {@code cmp} 对 {@code topKList} 进行排序，得到最终按 {@code cmp} 指定顺序（例如升序）排列的 Top-K 结果。</li>
     *   <li>返回排序后的 Top-K 列表 ({@code topKList})。</li>
     * </ol>
     * </p>
     *
     * <p>
     * <strong>策略:</strong>
     * <ul>
     *   <li><strong>最大堆:</strong> 利用最大堆的性质，在 O(N log K) 的时间复杂度内找到 Top-K 元素，其中 N 是列表大小。这通常优于全排序的 O(N log N)。</li>
     *   <li><strong>空间换时间:</strong> 需要额外的 O(K) 空间来存储堆。</li>
     * </ul>
     * </p>
     *
     * @param list 要从中选择 Top-K 元素的源列表。
     * @param k    需要选择的元素数量 (Top-K)。
     * @param cmp  定义元素排序规则的比较器。例如，要获取距离最近的 K 个，应传入按距离升序的比较器。
     * @param <T>  列表元素的类型，必须继承自 {@link BaseDataCache}。这里泛型约束可能过于具体，如果方法通用性更强，可以移除或调整。
     * @return 返回一个包含按 {@code cmp} 排序的 Top-K 个元素的列表。列表大小最多为 {@code k}。
     */
    private <T extends BaseDataCache> List<BaseDataDomainWithDistance<T>> topKSelect(
            List<BaseDataDomainWithDistance<T>> list, int k,
            Comparator<BaseDataDomainWithDistance<T>> cmp) {
        // 创建一个最大堆，容量为k，比较器使用传入cmp的反向（使得堆顶是当前k个中"最大"的）
        PriorityQueue<BaseDataDomainWithDistance<T>> maxHeap = new PriorityQueue<>(k, cmp.reversed());
        // 遍历输入列表
        for (BaseDataDomainWithDistance<T> item : list) {
            // 如果堆未满k个元素
            if (maxHeap.size() < k) {
                maxHeap.offer(item); // 直接加入堆
            } else {
                // 如果堆已满，比较当前元素与堆顶元素（堆中"最大"的元素）
                // 使用原始比较器cmp，如果 item 比堆顶元素 "小"
                if (cmp.compare(item, maxHeap.peek()) < 0) {
                    maxHeap.poll(); // 移除堆顶（最大的）
                    maxHeap.offer(item); // 将更小的 item 加入堆
                }
            }
        }
        // 此时 maxHeap 中包含 Top-K 个元素，但顺序是堆序
        // 将堆中元素转存到 List 中
        List<BaseDataDomainWithDistance<T>> topKList = new ArrayList<>(maxHeap);
        // 对 List 使用原始比较器 cmp 进行排序（如升序）
        topKList.sort(cmp);
        // 返回排序后的 Top-K 列表
        return topKList;
    }
    /**
     * 将一个列表分割成多个子列表（分区）。
     *
     * <p>
     * <strong>说明:</strong><br>
     * 此静态辅助方法用于将一个大列表分解为若干个小列表，常用于并行处理场景，
     * 以便将任务分配给多个线程或处理器核心。分区的策略会考虑系统可用的处理器数量，
     * 并有最小和最大分区大小的限制，以避免分区过小（开销大）或过大（并行度不足）。
     * </p>
     *
     * <p>
     * <strong>处理流程:</strong>
     * <ol>
     *   <li><strong>处理空列表:</strong> 如果输入的列表 ({@code list}) 为 null 或为空，直接返回一个空的列表集合。</li>
     *   <li><strong>计算分区大小 ({@code chunkSize}):</strong>
     *      <ul>
     *          <li>获取当前运行时可用的处理器核心数 ({@code availableProcessors})。</li>
     *          <li>获取列表的总大小 ({@code totalSize})。</li>
     *          <li>初步计算理想的块大小 ({@code calculatedChunkSize})，目标是让每个处理器大致处理一个块：{@code (totalSize + availableProcessors - 1) / availableProcessors} （向上取整）。</li>
     *          <li>最终确定实际使用的分区大小 ({@code chunkSize})：
     *              <ul>
     *                  <li>取 {@code calculatedChunkSize} 和预设的最大分区大小 ({@code MAX_PARTITION_SIZE}) 中的较小值。</li>
     *                  <li>再取上一步结果和预设的最小分区大小 ({@code MIN_PARTITION_SIZE}) 中的较大值。</li>
     *                  <li>这样确保了 {@code chunkSize} 在 [{@code MIN_PARTITION_SIZE}, {@code MAX_PARTITION_SIZE}] 范围内，并且尽可能接近理想值。</li>
     *              </ul>
     *          </li>
     *      </ul>
     *   </li>
     *   <li><strong>计算分区数量 ({@code numPartitions}):</strong> 根据总大小和确定的块大小计算需要多少个分区：{@code (totalSize + chunkSize - 1) / chunkSize} （向上取整）。</li>
     *   <li><strong>生成分区:</strong>
     *      <ul>
     *          <li>使用 {@link IntStream#range(int, int)} 生成一个从 0 到 {@code numPartitions - 1} 的整数序列，代表每个分区的索引。</li>
     *          <li>对每个索引 {@code i}，使用 {@link List#subList(int, int)} 从原始列表 {@code list} 中提取对应的子列表。子列表的范围是从 {@code i * chunkSize} (包含) 到 {@code Math.min((i + 1) * chunkSize, totalSize)} (不包含)。使用 {@code Math.min} 确保最后一个分区的结束索引不会超出原始列表的界限。</li>
     *          <li>使用 {@code .mapToObj(...)} 将整数索引映射为对应的子列表对象。</li>
     *          <li>使用 {@code .filter(subList -> !subList.isEmpty())} 过滤掉可能产生的空子列表（理论上在计算正确的情况下不应出现，但作为防御性编程）。</li>
     *          <li>将所有非空子列表收集到一个新的列表 ({@code List<List<String>>}) 中。</li>
     *      </ul>
     *   </li>
     *   <li>返回包含所有分区的列表。</li>
     * </ol>
     * </p>
     *
     * @param list 需要进行分区的原始列表 (元素类型为 String，但方法逻辑适用于任何类型的列表，这里类型固定可能只是特定场景)。
     * @return 返回一个包含原始列表各个分区的列表 ({@code List<List<String>>})。如果原始列表为空，返回空列表。
     */
    private static List<List<String>> partitionList(List<String> list) {
        // 如果输入列表为空，则无需分区
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        // 获取可用处理器核心数
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        // 获取列表总大小
        int totalSize = list.size();
        // 粗略计算每个分区的大小，尽量让每个处理器处理一个分区
        int calculatedChunkSize = (totalSize + availableProcessors - 1) / availableProcessors; // 向上取整
        // 确定最终的分区大小，要在预设的最小和最大值之间，并尽量接近计算值
        final int chunkSize = Math.max(MIN_PARTITION_SIZE, Math.min(calculatedChunkSize, MAX_PARTITION_SIZE));
        // 计算总共需要多少个分区
        int numPartitions = (totalSize + chunkSize - 1) / chunkSize; // 向上取整
        // 使用 IntStream 生成分区索引，并映射为子列表
        return IntStream.range(0, numPartitions)
                .mapToObj(i ->
                        // 获取第 i 个分区对应的子列表
                        list.subList(i * chunkSize, Math.min((i + 1) * chunkSize, totalSize))
                )
                // 过滤掉空子列表（理论上不应发生，防御性）
                .filter(subList -> !subList.isEmpty())
                // 将所有子列表收集到一个 List<List<String>> 中
                .collect(Collectors.toList());
    }

    /**
     * 将数据保存到缓存中
     * 
     * @param baseDataList 要保存的数据列表
     * @param clazz 数据类型
     * @param <T> 数据类型
     * @deprecated 使用AssetDataHandler.saveCache方法替代
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public <T> void saveCache(List<T> baseDataList, Class<T> clazz) {
        if (baseDataList == null || baseDataList.isEmpty()) {
            return;
        }
        
        AssetType assetType = null;
        
        if (clazz.equals(BaseBridgeResponseCache.class)) {
            assetType = AssetType.BRIDGE;
        } else if (clazz.equals(BaseCulvertResponseCache.class)) {
            assetType = AssetType.CULVERT;
        } else if (clazz.equals(BaseTunnelResponseCache.class)) {
            assetType = AssetType.TUNNEL;
        }
        
        if (assetType != null && handlerRegistry.hasHandler(assetType)) {
            AssetDataHandler<?, ?, ?> handler = handlerRegistry.getHandler(assetType);
            handler.saveCache((List<BaseDataCache>) baseDataList);
        } else {
            log.warn("未找到适合的处理器，使用旧的保存方法");
            legacySaveCache(baseDataList, clazz);
        }
    }

    /**
     * 旧的保存方法，仅用于向后兼容
     */
    @SuppressWarnings("unchecked")
    private <T> void legacySaveCache(List<T> baseDataList, Class<T> clazz) {
        if (clazz.equals(BaseBridgeResponseCache.class)) {
            baseBridgeResponseCacheService.clearTable();
            baseBridgeResponseCacheService.insertBatch(castList(baseDataList, BaseBridgeResponseCache.class));
        } else if (clazz.equals(BaseCulvertResponseCache.class)) {
            baseCulvertResponseCacheService.clearTable();
            baseCulvertResponseCacheService.insertBatch(castList(baseDataList, BaseCulvertResponseCache.class));
        } else if (clazz.equals(BaseTunnelResponseCache.class)) {
            baseTunnelResponseCacheService.clearTable();
            baseTunnelResponseCacheService.insertBatch(castList(baseDataList, BaseTunnelResponseCache.class));
        }
    }

    /**
     * 将数据列表类型转换为指定类型
     */
    @SuppressWarnings("unchecked")
    private <T> List<T> castList(List<?> list, Class<T> clazz) {
        return (List<T>) list;
    }

    /**
     * 对象类型转换
     */
    @SuppressWarnings("unchecked")
    private <T> List<T> castList(Object obj) {
        return (List<T>) obj;
    }

    /**
     * 传入查询条件PatrolAssetCheckRequest，查询PatrolAssetCheckDTO
     */
    @Override
    //    @Async
    public void selectBaseType(AssetType assetType) {
        List<AssetType> assetTypeList = new ArrayList<>();
        if (assetType != null) {
            assetTypeList.add(assetType);
        } else {
            assetTypeList = Arrays.asList(AssetType.values());
        }
        for (AssetType type : assetTypeList) {
            selectBaseDataResponse(type);
        }
        // 使用线程池并行执行
        //        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        //
        //        for (AssetType type : assetTypeList) {
        //            executorService.submit(() -> selectBaseDataResponse(type));
        //        }
        //
        //        executorService.shutdown();
        //        try {
        //            // 等待所有任务完成
        //            if (!executorService.awaitTermination(10, TimeUnit.MINUTES)) {
        //                executorService.shutdownNow();
        //            }
        //        } catch (InterruptedException e) {
        //            executorService.shutdownNow();
        //            Thread.currentThread().interrupt();
        //        }
    }
}
