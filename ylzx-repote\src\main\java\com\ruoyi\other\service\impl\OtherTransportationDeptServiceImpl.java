package com.ruoyi.other.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.other.domain.OtherTransportation;
import com.ruoyi.other.domain.OtherTransportationDept;
import com.ruoyi.other.domain.dto.OtherTransportationDTO;
import com.ruoyi.other.mapper.OtherTransportationDeptMapper;
import com.ruoyi.other.service.OtherTransportationDeptService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 大件运输Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class OtherTransportationDeptServiceImpl extends ServiceImpl<OtherTransportationDeptMapper, OtherTransportationDept> implements OtherTransportationDeptService {
    @Resource
    private OtherTransportationDeptMapper otherTransportationDeptMapper;

    @Override
    public List<OtherTransportationDept> findListByParam(Map<String, Object> params) {
        return otherTransportationDeptMapper.findListByParam(params);
    }

    @Override
    public List<String> getManagementDepartmentIdsByTransportationId(String id) {
        return otherTransportationDeptMapper.getManagementDepartmentIdsByTransportationId(id);
    }
    @Override
    public boolean updateDepartmentByTransportationId(String transportationId, String deptId, String deptName) {
        // 创建要更新的对象，并设置要更新的字段
        OtherTransportationDept updateDept = new OtherTransportationDept();
        updateDept.setDeptId(deptId);
        updateDept.setDeptName(deptName);

        // 构建查询条件，匹配 transportationId
        QueryWrapper<OtherTransportationDept> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("transportation_id", transportationId);

        // 执行更新操作
        int result = otherTransportationDeptMapper.update(updateDept, queryWrapper);

        // 返回更新操作是否成功
        return result > 0;
    }

    @Override
    public boolean removeByTransportationId(String transportationId) {

        return otherTransportationDeptMapper.deleteByTransportationIdAndDeptId(transportationId) > 0;
    }

    @Override
    public List<String> getTransportationList(List<String> userDeptIdList) {
        return otherTransportationDeptMapper.getTransportationDeptByIds(userDeptIdList);
    }


}
