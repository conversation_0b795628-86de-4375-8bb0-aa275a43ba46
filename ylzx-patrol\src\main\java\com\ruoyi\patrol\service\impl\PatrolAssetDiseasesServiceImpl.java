package com.ruoyi.patrol.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.patrol.domain.PatrolAssetDiseases;
import com.ruoyi.patrol.mapper.PatrolAssetDiseasesMapper;
import com.ruoyi.patrol.service.PatrolAssetDiseasesService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 资产病害信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
@Master
public class PatrolAssetDiseasesServiceImpl extends ServiceImpl<PatrolAssetDiseasesMapper, PatrolAssetDiseases>
        implements PatrolAssetDiseasesService {

    /**
     * 根据部件类型查询病害信息
     * @param partsType 部件类型
     * @return 病害信息
     */
    public List<PatrolAssetDiseases> selectPatrolPartsInfoByPartsType(String partsType){
        return baseMapper.selectPatrolPartsInfoByPartsType(partsType);
    }

}
