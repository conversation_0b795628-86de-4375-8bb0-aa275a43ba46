package com.ruoyi.patrol.service.handler;

import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.enums.AssetType;

import java.util.List;

/**
 * 资产数据处理器接口
 * 定义不同类型资产数据获取和转换的标准行为
 * 
 * @param <REQ> 请求类型
 * @param <RESP> 响应类型
 * @param <CACHE> 缓存类型
 */
public interface AssetDataHandler<REQ extends BaseDataDomain, RESP, CACHE extends BaseDataCache> {
    
    /**
     * 获取当前处理器支持的资产类型
     * 
     * @return 资产类型
     */
    AssetType getAssetType();
    
    /**
     * 获取请求类Class
     * 
     * @return 请求类的Class对象
     */
    Class<REQ> getRequestClass();
    
    /**
     * 获取缓存类Class
     * 
     * @return 缓存类的Class对象
     */
    Class<CACHE> getCacheClass();
    
    /**
     * 创建请求实例
     * 
     * @return 新的请求实例
     * @throws Exception 反射异常
     */
    REQ createRequestInstance() throws Exception;
    
    /**
     * 获取分页数据
     * 
     * @param request 请求对象
     * @return 分页数据
     */
    MTableDataInfo<List<RESP>> fetchData(REQ request);
    
    /**
     * 将响应数据转换为缓存对象
     * 
     * @param responses 响应数据列表
     * @return 缓存对象列表
     */
    List<CACHE> convertToCacheList(List<RESP> responses);
    
    /**
     * 处理资产基础数据请求
     * 适配基础数据请求到具体请求对象
     * 
     * @param assetRequest 基础数据请求
     * @param request 具体请求对象
     * @param dataRule 是否应用数据规则
     */
    void prepareRequest(AssetBaseDataRequest assetRequest, REQ request, boolean dataRule);
    
    /**
     * 将数据保存到缓存
     * 
     * @param dataList 要保存的数据列表
     * @param <T> 数据类型
     */
    <T extends BaseDataCache> void saveCache(List<T> dataList);
    
    /**
     * 增强数据（补充部门和路线信息等）
     * 
     * @param dataList 要增强的数据列表
     * @param <T> 数据类型
     */
    <T extends BaseDataCache> void enrichData(List<T> dataList);
} 