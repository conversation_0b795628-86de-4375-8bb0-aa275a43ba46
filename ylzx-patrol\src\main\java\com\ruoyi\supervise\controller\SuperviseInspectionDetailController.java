package com.ruoyi.supervise.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.supervise.domain.SuperviseInspectionDetail;
import com.ruoyi.supervise.service.SuperviseInspectionDetailService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 督查详情Controller
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
@Api(tags = "督查详情" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/superviseDetail")
public class SuperviseInspectionDetailController extends BaseController {
    final private SuperviseInspectionDetailService superviseInspectionDetailService;


    /**
     * 查询督查详情列表(分页)
     */
    @ApiOperation("查询督查详情列表")
//    //@RequiresPermissions("supervise:detail:list")
    @GetMapping("/list")
    public TableDataInfo list(SuperviseInspectionDetail superviseInspectionDetail) {
        QueryWrapper<SuperviseInspectionDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        qw.setEntity(superviseInspectionDetail);
        startPage();
        List<SuperviseInspectionDetail> list = superviseInspectionDetailService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询督查详情列表(不分页)
     */
    @ApiOperation("查询督查详情列表(不分页)")
//    //@RequiresPermissions("supervise:detail:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll(SuperviseInspectionDetail superviseInspectionDetail) {
        QueryWrapper<SuperviseInspectionDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        qw.setEntity(superviseInspectionDetail);
        List<SuperviseInspectionDetail> list = superviseInspectionDetailService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询督查详情数据
     */
    @ApiOperation("根据id查询督查详情数据")
//    //@RequiresPermissions("supervise:detail:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            SuperviseInspectionDetail superviseInspectionDetail = superviseInspectionDetailService.getById(id);
        if (superviseInspectionDetail == null) {
            return error("未查询到【督查详情】记录");
        }
        return success(superviseInspectionDetail);
    }

    /**
     * 新增督查详情
     */
    @ApiOperation("新增督查详情")
//    //@RequiresPermissions("supervise:detail:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody SuperviseInspectionDetail superviseInspectionDetail) {
        boolean save = superviseInspectionDetailService.save(superviseInspectionDetail);
        return success(save ? superviseInspectionDetail : null);
    }

    /**
     * 修改督查详情
     */
    @ApiOperation("修改督查详情")
//    //@RequiresPermissions("supervise:detail:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody SuperviseInspectionDetail superviseInspectionDetail) {
        return toAjax(superviseInspectionDetailService.updateById(superviseInspectionDetail));
    }

    /**
     * 删除督查详情
     */
    @ApiOperation("删除督查详情")
//    //@RequiresPermissions("supervise:detail:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(superviseInspectionDetailService.removeById(id));
    }

    /**
     * 导出督查详情列表
     */
    @ApiOperation("导出督查详情列表")
//    //@RequiresPermissions("supervise:detail:export")
    @Log(title = "督查详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<SuperviseInspectionDetail> list = superviseInspectionDetailService.list();
        ExcelUtil<SuperviseInspectionDetail> util = new ExcelUtil<SuperviseInspectionDetail>(SuperviseInspectionDetail.class);
        util.exportExcel(response, list, "督查详情数据");
    }

}
