package com.ruoyi.patrol.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 病害处置对象 base_disease_advices
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="病害处置")
@TableName("patrol_disease_advices")
@Data
public class PatrolDiseaseAdvices extends BaseTableEntity {
    private static final long serialVersionUID = 1L;

    /** 编码 */
    @Excel(name = "编码")
    @ApiModelProperty(value = "编码")
    private String code;

    /** 名称 */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称")
    private String name;

    /** 排序 */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /** 病害id */
    @Excel(name = "病害id")
    @ApiModelProperty(value = "病害id")
    private String diseaseId;


    /** 病害名称 */
    @TableField(exist = false)
    private String diseaseName;

}
