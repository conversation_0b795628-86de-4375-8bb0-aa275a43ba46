package com.ruoyi.patrol.domain;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.utils.handler.StakeNumberHandler;
import com.ruoyi.patrol.enums.*;
import com.ruoyi.manage.api.domain.BaseDataDomain;
import com.ruoyi.system.api.RemoteDeptAuthService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import javax.annotation.Resource;
import java.time.YearMonth;
import java.time.LocalTime;
import java.time.LocalDate;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 资产巡检查主表对象 patrol_asset_check
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="资产巡检查主表")
@TableName("patrol_asset_check")
@Data
@NoArgsConstructor
public class PatrolAssetCheck extends PatrolCheckBase {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 检查类型 1.桥梁经常检查 2.涵洞经常检查 3.涵洞定期检查 */
    @Excel(name = "检查类型",
            readConverterExp =
                    "BRIDGE_DAILY_INSPECTION=桥梁日常巡查,BRIDGE_REGULAR_INSPECTION=桥梁经常检查," +
                            "CULVERT_REGULAR_INSPECTION=涵洞日常检查,CULVERT_FREQUENT_INSPECTION=涵洞经常检查," +
                            "TUNNEL_DAILY_INSPECTION=隧道日常巡查,TUNNEL_REGULAR_INSPECTION=隧道经常巡查," +
                            "DEVICE_DAILY_INSPECTION=隧道机电日常检查,DEVICE_REGULAR_INSPECTION=隧道机电经常检查")
    @ApiModelProperty(value = "检查类型 1.桥梁经常检查 2.涵洞经常检查 3.涵洞定期检查")
    @EnumValue
    private InspectionType type;

    /** 巡查类别 1.常规巡查 2.雨季巡查 3.专项巡查 4.其他巡查  */
    @Excel(name = "巡查类别", readConverterExp = "REGULAR_PATROL=常规巡查,RAINY_SEASON_PATROL=雨季巡查," +
            "SPECIAL_PATROL=专项巡查,OTHER_PATROL=其他巡查")
    @ApiModelProperty(value = "巡查类别 1.常规巡查 2.雨季巡查 3.专项巡查 4.其他巡查 ")
    @EnumValue
    private PatrolCategory category = PatrolCategory.REGULAR_PATROL;

    /** 负责人id */
    @ApiModelProperty(value = "负责人id")
    private String kahunaId;

    /** 负责人 */
    @Excel(name = "负责人名称")
    @ApiModelProperty(value = "负责人名称")
    private String kahunaName;

    /** 负责人签名ownerId */
    @ApiModelProperty(value = "负责人签名ownerId")
    private String kahunaSign;

    @ApiModelProperty(value = "负责人签名url")
    @TableField(exist = false)
    private String kahunaSignUrl;

    @TableField(exist = false)
    private List<String> kahunaSignList;

    /** 记录人ID */
    @ApiModelProperty(value = "记录人Id")
    private String oprUserId;

    /** 记录人 */
    @Excel(name = "记录人名称")
    @ApiModelProperty(value = "记录人名称")
    private String oprUserName;

    /** 记录人ownerId */
    @ApiModelProperty(value = "记录人ownerId")
    private String oprUserSign;

    @TableField(exist = false)
    private List<String> oprUserSignList;

    @ApiModelProperty(value = "记录人签名url")
    @TableField(exist = false)
    private String oprUserSignUrl;

    /** 检查时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检查时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "检查时间")
    private Date checkTime;

    /** 检查开始时间 */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkStartTime;

    /** 检查结束时间 */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkEndTime;

    /** 状态 审核状态 */
    @Excel(name = "状态 审核状态",readConverterExp = "SUBMIT=待提交,PENDING=待审核,APPROVED=审核通过,REJECTED=审核未通过")
    @ApiModelProperty(value = "状态 审核状态")
    @EnumValue
    private AuditStatusType status;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    /** 图像 */
    @Excel(name = "图像")
    @ApiModelProperty(value = "图像")
    private String image;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 删除标识 */
    @ApiModelProperty(value = "删除标识")
    @EnumValue
    private DeleteFlagType delFlag = DeleteFlagType.NOT_DELETED;

    /** 资产id 当类型为桥梁检查时，记录桥梁id，为涵洞时，记录涵洞id */
    @Excel(name = "资产id 当类型为桥梁检查时，记录桥梁id，为涵洞时，记录涵洞id")
    @ApiModelProperty(value = "资产id 当类型为桥梁检查时，记录桥梁id，为涵洞时，记录涵洞id")
    private String assetId;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /** 资产编码 */
    @Excel(name = "资产编码")
    @ApiModelProperty(value = "资产编码")
    private String assetCode;

    /** 路线ID */
    @ApiModelProperty(value = "路线ID")
    private String routeId;

    /** 路线名称 */
    @Excel(name = "路线名称")
    @ApiModelProperty(value = "路线名称")
    private String routeName;

    /** 路线编码 */
    @Excel(name = "路线编码")
    @ApiModelProperty(value = "路线编码")
    private String routeCode;

    /** 权属单位Id */
    @ApiModelProperty(value = "权属单位Id")
    private String propertyUnitId;

    /** 权属单位 */
    @Excel(name = "权属单位")
    @ApiModelProperty(value = "权属单位")
    private String propertyUnitName;

    /** 养护单位Id */
    @ApiModelProperty(value = "养护单位Id")
    private String maintainUnitId;

    /** 养护单位 */
    @Excel(name = "养护单位")
    @ApiModelProperty(value = "养护单位")
    private String maintainUnitName;

    /** 养护路段id */
    @ApiModelProperty(value = "养护路段id")
    private String maintenanceSectionId;

    /** 养护路段名称 */
    @Excel(name = "养护路段名称")
    @ApiModelProperty(value = "养护路段名称")
    private String maintenanceSectionName;

    /** 桩号 */
    @Excel(name = "桩号", handler = StakeNumberHandler.class)
    @ApiModelProperty(value = "桩号")
    private BigDecimal centerStake;

    /** 桩号format */
    @ApiModelProperty(value = "桩号format")
    @TableField(exist = false)
    private String stakeFormat;

    /** 天气 */
    @Excel(name = "天气")
    @ApiModelProperty(value = "天气")
    private String weather;

    @Excel(name = "到期时间")
    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expiry;

    @Excel(name = "周期(天)")
    @ApiModelProperty(value = "周期(天)")
    private Integer frequency;

    @Excel(name = "阶段", readConverterExp = "IN_PERIOD=在期,COMPLETED=完成,EXPIRED=过期")
    @ApiModelProperty(value = "阶段(经常检查)0:在期,1:完成,2过期")
    @EnumValue
    private StageType stage;

    @Excel(name = "病害数量")
    @ApiModelProperty(value = "病害数量")
    private Integer diseaseCount=0;

    /** 巡查内容 */
    @TableField(exist = false)
    List<PatrolAssetCheckDetail> patrolCheckDetailList;

    public PatrolAssetCheck(String id, InspectionType type, int frequency, Date from) {
        this.assetId = id;
        this.type = type;
        this.frequency = frequency;
        this.checkTime = from;
    }
    
    /**
     * 根据资产信息创建一个新的PatrolAssetCheck
     * 
     * @param assetResponse 资产基础数据
     * @param frequency 频率
     * @param nowYearMonth 当前年月
     * @param inspectionType 检查类型
     */
    public PatrolAssetCheck(BaseDataDomain assetResponse, Integer frequency, YearMonth nowYearMonth, InspectionType inspectionType) {
        //雪花id
        this.setId(IdWorker.getIdStr());
        //检查类型
        this.setType(inspectionType);
        //巡查类别
        this.setCategory(PatrolCategory.REGULAR_PATROL);
        //审核状态
        this.setStatus(AuditStatusType.SUBMIT);
        //删除标识
        this.setDelFlag(DeleteFlagType.NOT_DELETED);
        
        //资产id
        this.setAssetId(assetResponse.getAssetId());
        //资产名称
        this.setAssetName(assetResponse.getAssetName());
        //资产编码
        this.setAssetCode(assetResponse.getAssetCode());
        //路线id
        this.setRouteId(assetResponse.getRouteId());
        //路线名称
        this.setRouteName(assetResponse.getRouteName());
        //路线编码
        this.setRouteCode(assetResponse.getRouteCode());
        //权属单位Id
        this.setPropertyUnitId(assetResponse.getManagementMaintenanceBranchId());
        //权属单位
        this.setPropertyUnitName(assetResponse.getManagementMaintenanceBranchName());
        //养护单位Id
        this.setMaintainUnitId(assetResponse.getManagementMaintenanceId());
        //养护单位
        this.setMaintainUnitName(assetResponse.getManagementMaintenanceName());
        //养护路段Id
        this.setMaintenanceSectionId(assetResponse.getMaintenanceSectionId());
        //养护路段
        this.setMaintenanceSectionName(assetResponse.getMaintenanceSectionName());
        //桩号
        this.setCenterStake(assetResponse.getCenterStake());
        
        //到期时间
        this.setExpiry(nowYearMonth.atEndOfMonth().atTime(LocalTime.of(23, 59, 59)));
        //频率
        this.setFrequency(frequency);
        //阶段
        this.setStage(StageType.IN_PERIOD);
        
        LocalDateTime now = LocalDateTime.now();
        //create_time
        this.setCreateTime(now);
        //update_time
        this.setUpdateTime(now);
        String userBy = "system";
        //create_by
        this.setCreateBy(userBy);
        //update_by
        this.setUpdateBy(userBy);
    }
    
    /**
     * 根据资产信息和巡检日志创建一个新的PatrolAssetCheck
     * 
     * @param assetResponse 资产基础数据
     * @param patrolInspectionLogs 巡检日志
     * @param frequency 频率
     * @param nowDate 当前日期
     * @param inspectionType 检查类型
     * @param userName 用户名
     */
    public PatrolAssetCheck(BaseDataDomain assetResponse, PatrolInspectionLogs patrolInspectionLogs, 
                           Integer frequency, LocalDate nowDate, InspectionType inspectionType, String userName) {
        //雪花id
        this.setId(IdWorker.getIdStr());
        //检查类型
        this.setType(inspectionType);
        //巡查类别
        this.setCategory(PatrolCategory.REGULAR_PATROL);

        //负责人id
        this.setKahunaId(null);
        //负责人
        this.setKahunaName(null);
        //负责人签名ownerId
        this.setKahunaSign(null);
        
        //记录人id
        this.setOprUserId(patrolInspectionLogs.getUserIdStr());
        //记录人
        this.setOprUserName(patrolInspectionLogs.getNickNames());
        //记录人ownerId
        this.setOprUserSign(patrolInspectionLogs.getSignIds());
        
        //检查时间
        this.setCheckTime(patrolInspectionLogs.getCollectTime());
        //审核状态
        this.setStatus(AuditStatusType.PENDING);
        //审核时间
        this.setAuditTime(null);
        //图形
        this.setImage(null);
        //备注
        this.setRemark(null);
        //删除标识
        this.setDelFlag(DeleteFlagType.NOT_DELETED);
        
        //资产id
        this.setAssetId(assetResponse.getAssetId());
        //资产名称
        this.setAssetName(assetResponse.getAssetName());
        //资产编码
        this.setAssetCode(assetResponse.getAssetCode());
        //路线id
        this.setRouteId(assetResponse.getRouteId());
        //路线名称
        this.setRouteName(assetResponse.getRouteName());
        //路线编码
        this.setRouteCode(assetResponse.getRouteCode());

        //权属单位Id
        this.setPropertyUnitId(patrolInspectionLogs.getMaintenanceUnitId());
        //权属单位
        this.setPropertyUnitName(patrolInspectionLogs.getMaintenanceUnitName());
        
        //养护单位Id
        this.setMaintainUnitId(assetResponse.getManagementMaintenanceId());
        //养护单位
        this.setMaintainUnitName(assetResponse.getManagementMaintenanceName());
        
        //养护路段Id
        this.setMaintenanceSectionId(assetResponse.getMaintenanceSectionId());
        //养护路段
        this.setMaintenanceSectionName(assetResponse.getMaintenanceSectionName());
        
        //桩号
        this.setCenterStake(assetResponse.getCenterStake());
        
        //天气
        this.setWeather(patrolInspectionLogs.getWeather());
        
        //到期时间
        this.setExpiry(nowDate.atTime(LocalTime.of(23, 59, 59)));
        
        //频率
        this.setFrequency(frequency);
        
        //阶段
        this.setStage(StageType.IN_PERIOD);
        
        LocalDateTime now = LocalDateTime.now();
        //create_time
        this.setCreateTime(now);
        //update_time
        this.setUpdateTime(now);
        String userBy = userName != null ? userName : "system";
        //create_by
        this.setCreateBy(userBy);
        //update_by
        this.setUpdateBy(userBy);
    }
}
