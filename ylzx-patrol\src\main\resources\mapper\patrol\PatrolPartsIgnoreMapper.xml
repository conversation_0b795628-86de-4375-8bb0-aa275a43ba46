<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.PatrolPartsIgnoreMapper">

    <!-- 根据资源ID列表查询检查项排除记录 -->
    <select id="selectByAssetIds" resultType="com.ruoyi.patrol.domain.PatrolPartsIgnore">
        SELECT * FROM patrol_parts_ignore 
        WHERE asset_id IN 
        <foreach collection="list" item="assetId" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </select>

</mapper>