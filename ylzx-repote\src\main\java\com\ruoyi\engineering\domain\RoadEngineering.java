package com.ruoyi.engineering.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import org.apache.poi.hpsf.Decimal;

/**
 * 涉路工程对象 road_engineering
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
@ApiModel(value="涉路工程")
@TableName("road_engineering")
@Data
public class RoadEngineering extends BaseTableEntity {
    private static final long serialVersionUID = 1L;

    /** 来文名称 */
    @Excel(name = "来文名称")
    @ApiModelProperty(value = "来文名称")
    private String documentReceived;

    /** 项目状态 */
    @Excel(name = "项目状态")
    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    /** 赔付情况 */
    @Excel(name = "赔付情况")
    @ApiModelProperty(value = "赔付情况")
    private String compensationSituation;

    /** 管养单位 */
    @Excel(name = "管养单位")
    @ApiModelProperty(value = "管养单位")
    private String managementUnit;

    /** 管养单位Id */
    @Excel(name = "管养单位Id")
    @ApiModelProperty(value = "管养单位Id")
    private String managementUnitId;

    /** 养护路段 */
    @Excel(name = "养护路段")
    @ApiModelProperty(value = "养护路段")
    private String maintenanceSection;

    /** 养护路段Id */
    @Excel(name = "养护路段Id")
    @ApiModelProperty(value = "养护路段Id")
    private String maintenanceSectionId;

    /** 路线编码 */
    @Excel(name = "路线编码")
    @ApiModelProperty(value = "路线编码")
    private String roadCode;

    /** 来文时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "来文时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "来文时间")
    private Date submissionTime;

    /** 来文单位 */
    @Excel(name = "来文单位")
    @ApiModelProperty(value = "来文单位")
    private String reportingUnit;

    /** 起点桩号 */
    @Excel(name = "起点桩号")
    @ApiModelProperty(value = "起点桩号")
    private Long startPoint;

    /** 终点桩号 */
    @Excel(name = "终点桩号")
    @ApiModelProperty(value = "终点桩号")
    private Long endPoint;

    /** 申请内容 */
    @Excel(name = "申请内容")
    @ApiModelProperty(value = "申请内容")
    private String applicationContent;

    /** 拟开工日期
 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "拟开工日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "拟开工日期")
    private Date plannedStartDate;

    /** 拟竣工日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "拟竣工日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "拟竣工日期")
    private Date plannedCompletionDate;

    /** 是否交通管制 */
    @Excel(name = "是否交通管制")
    @ApiModelProperty(value = "是否交通管制")
    private String isTrafficControl;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    /** 电话 */
    @Excel(name = "电话")
    @ApiModelProperty(value = "电话")
    private Long phoneNumber;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /** 复函文件编码 */
    @Excel(name = "复函文件编码")
    @ApiModelProperty(value = "复函文件编码")
    private String replyDocumentCode;

    /** 复函时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "复函时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "复函时间")
    private Date replyTime;

    /** 复函意见 */
    @Excel(name = "复函意见")
    @ApiModelProperty(value = "复函意见")
    private String replyOpinions;

    /** 涉路施工协议编号 */
    @Excel(name = "涉路施工协议编号")
    @ApiModelProperty(value = "涉路施工协议编号")
    private String agreementNumber;

    /** 路产协议编号 */
    @Excel(name = "路产协议编号")
    @ApiModelProperty(value = "路产协议编号")
    private String roadPropertyAgreementNumber;

    /** 协议金额 */
    @Excel(name = "协议金额")
    @ApiModelProperty(value = "协议金额")
    private Double agreementAmount;

    /** 方向 */
    @Excel(name = "方向")
    @ApiModelProperty(value = "方向")
    private String direction;

    /** 建设单位 */
    @Excel(name = "建设单位")
    @ApiModelProperty(value = "建设单位")
    private String constructionUnit;

    /** 设计单位 */
    @Excel(name = "设计单位")
    @ApiModelProperty(value = "设计单位")
    private String designUnit;

    /** 施工单位 */
    @Excel(name = "施工单位")
    @ApiModelProperty(value = "施工单位")
    private String constructionCompany;

    /** 监理单位 */
    @Excel(name = "监理单位")
    @ApiModelProperty(value = "监理单位")
    private String supervisionUnit;


    /** 实施内容 */
    @ApiModelProperty(value = "实施内容")
    private String implementationContent;
}
