package com.ruoyi.patroltidb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.patroltidb.domain.PatrolDeviceFaultRecord;
import com.ruoyi.patroltidb.mapper.PatrolDeviceFaultRecordMapper;
import com.ruoyi.patroltidb.service.PatrolDeviceFaultRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 机电故障上报Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-29
 */
@Service
@Slave
public class PatrolDeviceFaultRecordServiceImpl extends ServiceImpl<PatrolDeviceFaultRecordMapper, PatrolDeviceFaultRecord> implements PatrolDeviceFaultRecordService {

    @Autowired
    private PatrolDeviceFaultRecordMapper patrolDeviceFaultRecordMapper;

    @Override
    public List<PatrolDeviceFaultRecord> findListByParam(Map<String, Object> params) {
        return patrolDeviceFaultRecordMapper.findListByParam(params);
    }


}
