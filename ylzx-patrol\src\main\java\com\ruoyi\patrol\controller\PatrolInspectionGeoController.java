package com.ruoyi.patrol.controller;/**
 *
 */

import com.ruoyi.common.core.context.SecurityContextHolder;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.map.Paramap;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.patrol.domain.PatrolInspectionGeo;
import com.ruoyi.patrol.domain.PatrolInspectionLogs;
import com.ruoyi.patrol.domain.PatrolInspectionUser;
import com.ruoyi.patrol.domain.dto.PatrolInspectionGeoDto;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.vo.RealCoordinateLogVO;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.mapper.PatrolInspectionGeoMapper;
import com.ruoyi.patrol.service.PatrolInspectionGeoService;
import com.ruoyi.patrol.service.PatrolInspectionLogsService;
import com.ruoyi.patrol.service.PatrolInspectionUserService;
import com.ruoyi.patrol.utils.GeoUtils;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.core.web.domain.AjaxResult.success;
import static com.ruoyi.common.security.utils.SecurityUtils.getLoginUser;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月26日 16:15
 */
@Api(tags = "巡查日志地理数据接口")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/inspectionGeo")
public class PatrolInspectionGeoController {

    @Resource
    private PatrolInspectionGeoService patrolInspectionGeoService;
    @Resource
    private RedisService redisService;
    @Resource
    private RemoteDeptAuthService deptAuthService;
    @Resource
    private RemoteUserService userService;

    @Resource
    private PatrolInspectionLogsService patrolInspectionLogsService;
    @Resource
    private PatrolInspectionUserService patrolInspectionUserService;

    @Resource
    private PatrolInspectionGeoMapper patrolInspectionGeoMapper;

    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;

    @ApiOperation("查询巡查日志地理数据")
    @GetMapping("/getGeo/{id}")
    public AjaxResult getGeo(@PathVariable("id") String id) {
        PatrolInspectionGeo patrolInspectionGeo = patrolInspectionGeoService.selectGeoById(id);
        return success(patrolInspectionGeo);
    }

    @ApiOperation("新增或修改巡查日志地理数据")
    @PostMapping("/saveTrace")
    public AjaxResult saveOrUpdate(@RequestBody PatrolInspectionGeoDto patrolInspectionGeoDto) {
        PatrolInspectionGeo patrolInspectionGeo = new PatrolInspectionGeo();
        BeanUtils.copyProperties(patrolInspectionGeoDto, patrolInspectionGeo);

        Optional<PatrolInspectionLogs> optionalLogs = Optional.ofNullable(patrolInspectionLogsService.getById(patrolInspectionGeo.getLogId()));

        if (optionalLogs.isEmpty()) {
            return AjaxResult.error("巡查日志不存在");
        }

        PatrolInspectionLogs patrolInspectionLogs = optionalLogs.get();
        if (patrolInspectionLogs.getStatus() != 0) {
            return AjaxResult.error("巡查日志已经完成，不能再修改轨迹");
        }

        if (!Objects.isNull(patrolInspectionGeo.getShape()) && !patrolInspectionGeo.getShape().isEmpty()) {
            log.info("保存巡查日志地理数据: {}", patrolInspectionGeo.getShape());
            
            // 清理可能包含的非法字符，保留所有坐标点
            String cleanedShape = patrolInspectionGeo.getShape().replaceAll("[^\\d\\s.,()-]", "");
            
            // 检查数据格式，确保以LINESTRING开头且括号完整
            if (!cleanedShape.trim().toUpperCase().startsWith("LINESTRING")) {
                cleanedShape = "LINESTRING " + cleanedShape;
            }
            
            if (!cleanedShape.trim().endsWith(")")) {
                cleanedShape = cleanedShape.trim() + ")";
            }
            
            if (!cleanedShape.contains("(")) {
                cleanedShape = cleanedShape.replace("LINESTRING", "LINESTRING (");
            }
            
            log.info("清理后的地理数据: {}", cleanedShape);
            
            patrolInspectionGeo.setShape(cleanedShape);
            
            // 在清理后验证WKT格式
            if (!GeoUtils.isValidLineStringWkt(patrolInspectionGeo.getShape())) {
                log.error("清理后的地理数据格式仍然无效: {}", patrolInspectionGeo.getShape());
                return AjaxResult.error("地理数据格式无效，请检查坐标数据");
            }
            
            try {
                boolean isSaved = patrolInspectionGeoService.saveTrace(patrolInspectionGeo);
                if (!isSaved) {
                    return AjaxResult.error("保存失败");
                }
            } catch (Exception e) {
                log.error("保存巡查日志地理数据时发生异常", e);
                return AjaxResult.error("保存巡查地理数据失败: " + e.getMessage());
            }
            
            //  每次轨迹上传，存储校验后的最后一个点坐标
            String lastPoint = GeoUtils.getLastPointFromLineString(patrolInspectionGeo.getShape());
            List<PatrolInspectionUser> userList = patrolInspectionUserService.listByMap(Paramap.create().put("patrol_id", patrolInspectionGeo.getLogId()));
            String nickNames = userList.stream().map(PatrolInspectionUser::getNickName).collect(Collectors.joining(","));
            patrolInspectionLogs.setNickNames(nickNames);
            
            // 添加巡查车坐标信息等
            try {
                RealCoordinateLogVO logVO = new RealCoordinateLogVO();
                logVO.setLogId(patrolInspectionLogs.getId());
                R<SysDept> parentById = deptAuthService.getParentById(Long.valueOf(patrolInspectionLogs.getMaintenanceUnitId()));
                if (parentById.getCode() == 200 && parentById.getData() != null) {
                    SysDept dept = parentById.getData();
                    logVO.setDeptId(dept.getDeptId());
                    logVO.setDeptName(dept.getDeptName());
                } else {
                    logVO.setDeptId(Long.valueOf(patrolInspectionLogs.getMaintenanceUnitId()));
                    logVO.setDeptName(patrolInspectionLogs.getMaintenanceUnitName());
                }
                List<Long> userIds = userList.stream().map(PatrolInspectionUser::getUserId).collect(Collectors.toList());
                if (!userIds.isEmpty()) {
                    R<List<SysUser>> sysUserList = userService.findListParam(userIds);
                    if (sysUserList.getCode() == 200 && sysUserList.getData() != null) {
                        List<String> phoneList = sysUserList.getData().stream().map(SysUser::getPhonenumber).filter(Objects::nonNull).toList();
                        if (!phoneList.isEmpty()) logVO.setPhoneNumber(phoneList.get(0));
                    }
                }
                logVO.setMaintenanceSectionName(patrolInspectionLogs.getMaintenanceSectionName());
                logVO.setCarNum(patrolInspectionLogs.getCarNum());
                logVO.setStartTime(patrolInspectionLogs.getStartTime());
                logVO.setStatus(patrolInspectionLogs.getStatus());
                logVO.setPersonnelName(nickNames);
                logVO.setPatrolMileage(patrolInspectionLogs.getPatrolMileage());
                logVO.setCatType(1);
                logVO.setLastPoint(lastPoint);
                redisService.setCacheObject("REAL_COORDINATE:" + patrolInspectionGeo.getLogId(), logVO, 15L, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("保存坐标缓存时出错", e);
                // 忽略这个错误，不影响主流程
            }
        }

        boolean isUpdated = false;

        List<Integer> list = null;
        if (patrolInspectionGeoDto.getStatus() == 1) {
            patrolInspectionLogs.setStatus(patrolInspectionGeoDto.getStatus());
            patrolInspectionLogs.setEndTime(new Date());
            list = patrolInspectionGeoDto.getList();
            isUpdated = true;
        }
        if (patrolInspectionGeoDto.getPatrolMileage() != null) {
            patrolInspectionLogs.setPatrolMileage(patrolInspectionGeoDto.getPatrolMileage());
            isUpdated = true;
        }
        if (patrolInspectionGeoDto.getRemark() != null) {
            patrolInspectionLogs.setRemark(patrolInspectionGeoDto.getRemark());
            isUpdated = true;
        }

        if (isUpdated) {
            patrolInspectionLogsService.updateById(patrolInspectionLogs);
        }

        final List<Integer> assetTypes = list;
        LoginUser loginUser = getLoginUser();
        final Long loginUserId = loginUser.getUserid();
        final String username = loginUser.getUsername();
        if (assetTypes != null && !assetTypes.isEmpty()) {
            final Map<String, Object> localMap = SecurityContextHolder.getLocalMap();
            // 执行生成检查记录
            for (Integer assetType : assetTypes) {
                // 只要1,2
                if (assetType != 1 && assetType != 2) {
                    continue;
                }
                AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
                assetBaseDataRequest.setRoleUserId(loginUserId);
                assetBaseDataRequest.setUserName(username);
                AssetType type = AssetType.fromCode(assetType);
                if (type == null) {
                    continue;
                }
                InspectionType inspectionType = InspectionType.fromAssetType(type, true);
                assetBaseDataRequest.setType(inspectionType);
                assetBaseDataRequest.setLogId(patrolInspectionLogs.getId());
                try {
                    patrolAssetCheckService.generateCheckByCondition(assetBaseDataRequest);
                } catch (Exception e) {
                    log.error("生成检查记录失败", e);
                    throw e;  // 抛出异常以便事务回滚
                }
            }
        }
//        if (assetTypes != null && !assetTypes.isEmpty()) {
//            final Map<String,Object> localMap = SecurityContextHolder.getLocalMap();
//            // 异步执行生成检查记录
//            CompletableFuture.runAsync(() -> {
//                for (Integer assetType : assetTypes) {
//                    // 只要1,2
//                    if (assetType != 1 && assetType != 2) {
//                        continue;
//                    }
//                    SecurityContextHolder.setLocalMap(localMap);
//                    AssetBaseDataRequest assetBaseDataRequest = new AssetBaseDataRequest();
//                    assetBaseDataRequest.setRoleUserId(loginUserId);
//                    assetBaseDataRequest.setUserName(username);
////                    assetBaseDataRequest.setMaintenanceSectionId(patrolInspectionLogs.getMaintenanceSectionId());
////                    List<LocalDate> dateList = new ArrayList<>(Collections.singleton(
////                            patrolInspectionLogs.getCollectTime()
////                                    .toInstant()
////                                    .atZone(ZoneId.systemDefault()) // 指定时区
////                                    .toLocalDate()
////                    ));
////                    assetBaseDataRequest.setNowDateList(dateList);
//                    AssetType type = AssetType.fromCode(assetType);
//                    if (type == null) {
//                        continue;
//                    }
//                    InspectionType inspectionType = InspectionType.fromAssetType(type, true);
//                    assetBaseDataRequest.setType(inspectionType);
//                    assetBaseDataRequest.setLogId(patrolInspectionLogs.getId());
//                    try {
//                        patrolAssetCheckService.generateCheckByCondition(assetBaseDataRequest);
//                    } catch (Exception e) {
//                        log.error("异步生成检查记录失败", e);
//                    }
//                }
//            });
//        }

        return success(patrolInspectionLogs);
    }

    @ApiOperation("删除巡查日志地理数据")
    @DeleteMapping("/deleteGeo/{id}")
    public AjaxResult deleteGeo(@PathVariable("id") String id) {
        if (patrolInspectionGeoMapper.deleteById(id) > 0) {
            return success();
        }
        return AjaxResult.error("删除失败");
    }


}
