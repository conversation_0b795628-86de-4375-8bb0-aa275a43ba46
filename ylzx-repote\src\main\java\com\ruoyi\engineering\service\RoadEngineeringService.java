package com.ruoyi.engineering.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.engineering.domain.RoadEngineering;
import com.ruoyi.engineering.domain.dto.RoadEngineeringDTO;

/**
 * 涉路工程Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
public interface RoadEngineeringService extends IService<RoadEngineering> {

    /**
     * 根据条件查询涉路工程车道数据列表
     * @param params
     */
    List<RoadEngineering> findListByParam(Map<String, Object> params);

    /**
     * 根据条件查询大件运输列表
     * @param params
     */
    List<RoadEngineeringDTO> findAll(Map<String, Object> params);

    /**
     * 根据条件查询用户权限的涉路工程车道数据列表
     * @param params
     */
    List<RoadEngineering> getUserPermissions(Map<String, Object> params);
}
