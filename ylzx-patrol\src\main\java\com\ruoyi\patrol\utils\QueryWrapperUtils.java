package com.ruoyi.patrol.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.Map;

public class QueryWrapperUtils {

    /**
     * 将前端请求参数转换为QueryWrapper对象
     * @param params 前端请求参数
     * @return QueryWrapper对象
     */
    public static <T> QueryWrapper<T> convert(Map<String, Object> params) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value != null && !"".equals(value)
                    //排除分页参数
                    && !key.equals("pageNum") && !key.equals("pageSize")) {
                if (value instanceof String) {
                    queryWrapper.like(key, value.toString());
                } else {
                    queryWrapper.eq(key, value);
                }
            }
        }
        return queryWrapper;
    }

    /**
     * 将前端请求参数转换为分页对象
     * @param params 前端请求参数
     * @return 分页对象
     */
//    public static <T> IPage<T> convertPage(Map<String, Object> params) {
//        int current = Integer.parseInt(params.get("current").toString());
//        int size = Integer.parseInt(params.get("size").toString());
//        IPage<T> page = new Page<>(current, size);
//        return page;
//    }


}
