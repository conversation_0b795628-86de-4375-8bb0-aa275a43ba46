package com.ruoyi.patrol.domain.dto;

import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: QD
 * @date: 2024年11月28日 17:04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssetTimesDTO {
    // 本月应检查次数
    private Integer monthRequiredCount;
    // 本月已检查次数
    private Integer monthCheckedCount;
    // 本季度应检查次数
    private Integer quarterRequiredCount;
    // 本季度已检查次数
    private Integer quarterCheckedCount;
    // 本年应检查次数
    private Integer yearRequiredCount;
    // 本年已检查次数
    private Integer yearCheckedCount;
}
