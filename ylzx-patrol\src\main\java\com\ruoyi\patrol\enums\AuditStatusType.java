package com.ruoyi.patrol.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 审核状态
 */
@Getter
@RequiredArgsConstructor
public enum AuditStatusType implements IEnum<Integer> {
    SUBMIT(1, "待提交"),
    PENDING(2, "待审核"),
    APPROVED(3, "审核通过"),
    REJECTED(4, "审核未通过");

    private final Integer code;
    private final String description;

    @Override
    public Integer getValue() {
        return this.code;
    }

    @JsonValue
    public Integer getCode() {
        return this.code;
    }
    
    @JsonCreator
    public static AuditStatusType fromCode(Integer code) {
        // 如果传入的 code 为 null，则返回 null
        if (code == null) {
            return null;
        }
        
        for (AuditStatusType type : AuditStatusType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}
