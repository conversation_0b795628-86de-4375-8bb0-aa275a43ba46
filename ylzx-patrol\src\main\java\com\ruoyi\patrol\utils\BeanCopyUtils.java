package com.ruoyi.patrol.utils;

import org.springframework.beans.BeanUtils;

/**
 * @Description:
 * @author: QD
 * @date: 2024年06月20日 15:10
 */
public class BeanCopyUtils {

    public static <T> T toBean(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }

        // 创建目标类的实例
        T targetInstance;
        try {
            targetInstance = targetClass.getDeclaredConstructor().newInstance();

            // 使用Spring的BeanUtils复制属性
            BeanUtils.copyProperties(source, targetInstance);
        } catch (Exception e) {
            throw new RuntimeException("Could not transform bean", e);
        }

        return targetInstance;
    }
}