package com.ruoyi.patroltidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.manage.api.domain.MTableDataInfo;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheck;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheckDetail;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.patroltidb.service.PatrolBridgeCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolBridgeCheckService;
import com.ruoyi.system.api.RemoteMaintenanceSectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 桥梁巡检查记录Controller
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Api(tags = "桥梁巡检查记录" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/patrolBridgeCheck")
public class PatrolBridgeCheckController extends BaseController {
    final private PatrolBridgeCheckService patrolBridgeCheckService;

    final private PatrolBridgeCheckDetailService patrolBridgeCheckDetailService;

    @Resource
    private RemoteMaintenanceSectionService maintenanceSectionService;

    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;


    /**
     * 分页查询所有数据
     *
     * @param page              分页对象
     * @param patrolBridgeCheck 查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    //@RequiresPermissions("patrol:assetCheck:list")
    @RequestMapping(value = "/selectAll", method = RequestMethod.POST)
    public AjaxResult selectAll(Page<PatrolBridgeCheck> page, @RequestBody PatrolBridgeCheck patrolBridgeCheck,Integer pageNum,Integer pageSize) {
        if (pageNum != null && pageSize != null) {
            page.setCurrent(pageNum);
            page.setSize(pageSize);
        }
        return success(this.patrolBridgeCheckService.page(page, new QueryWrapper<>(patrolBridgeCheck) {{
            this.in(patrolBridgeCheck.getStatusList() != null, "status", patrolBridgeCheck.getStatusList()).
                    ge(patrolBridgeCheck.getCheckStartTime() != null, "check_time", patrolBridgeCheck.getCheckStartTime()).
                    le(patrolBridgeCheck.getCheckEndTime() != null, "check_time", patrolBridgeCheck.getCheckEndTime()).orderByDesc("check_time");
        }}));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @RequestMapping(value = "/selectOne/{id}", method = RequestMethod.GET)
    public AjaxResult selectOne(@PathVariable Serializable id) {
        return success(this.patrolBridgeCheckService.getById(id));
    }

    @ApiOperation("通过主键查询单条数据")
    @RequestMapping(value = "/selectRequestOne", method = RequestMethod.GET)
    public AjaxResult selectRequestOne(@RequestParam String id) {
        PatrolBridgeCheck patrolBridgeCheck = this.patrolBridgeCheckService.getPatrolBridgeCheckById(id);
        try{
            patrolAssetCheckService.setStack(patrolBridgeCheck);
            patrolAssetCheckService.setSignUrl(patrolBridgeCheck);
        }catch (Exception e){
            log.error("获取签名图片失败",e);
        }
        return success(patrolBridgeCheck);
    }

    /**
     * 新增数据
     *
     * @param patrolBridgeCheck 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public AjaxResult insert(@RequestBody PatrolBridgeCheck patrolBridgeCheck) {
        if(patrolBridgeCheck.getAssetId() == null){
            return error("资产ID不能为空");
        }
        return success(this.patrolBridgeCheckService.save(patrolBridgeCheck));
    }

    /**
     * 修改数据
     *
     * @param patrolBridgeCheck 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public AjaxResult update(@RequestBody PatrolBridgeCheck patrolBridgeCheck) {
        return success(this.patrolBridgeCheckService.updateById(patrolBridgeCheck));
    }

    /**
     * 删除数据
     *
     * @param idList 主键集合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @RequestMapping(value = "/delete", method = RequestMethod.DELETE)
    public AjaxResult delete(@RequestBody List<String> idList) {
        return success(this.patrolBridgeCheckService.removeByIds(idList));
    }

    /**
     * 查询数据列表
     *
     * @param patrolBridgeCheck 查询实体
     * @return 数据列表
     */
    @ApiOperation("查询数据列表")
    @RequestMapping(value = "/getListByEntity", method = RequestMethod.POST)
    public AjaxResult getListByEntity(@RequestBody PatrolBridgeCheck patrolBridgeCheck) {
        return success(this.patrolBridgeCheckService.list(new QueryWrapper<>(patrolBridgeCheck)));
    }


    /**
     * @param bridgeId
     * @param type
     * @param year
     * @return
     */
    @ApiOperation("查询所有数据(带是否异常)")
    @RequestMapping(value = "/selectWithResult", method = RequestMethod.GET)
    public AjaxResult selectResult(@RequestParam() String bridgeId , @ApiParam("巡查类型：'1':日常巡查,'2':经常检查，默认1") @RequestParam() String type, @RequestParam() String year) {

        List<PatrolBridgeCheck> list = this.patrolBridgeCheckService.list(new QueryWrapper<>() {{
            this.eq(bridgeId != null, "asset_id", bridgeId)
                    .like(year != null, "check_time", year)
                    .eq(type != null, "type", type)
                    .orderByDesc("check_time");
        }});

        for (PatrolBridgeCheck check : list) {

            QueryWrapper<PatrolBridgeCheckDetail> qw = new QueryWrapper<>();
            qw.eq("check_id", check.getId());
            List<PatrolBridgeCheckDetail> detailList = patrolBridgeCheckDetailService.list(qw);

            List<PatrolBridgeCheckDetail> badDetailList = detailList.stream().filter(i -> (!i.getDefect().equals("未见异常")) && (!i.getDefect().equals("/"))).collect(Collectors.toList());

            check.setIsException(badDetailList.size() > 0);
        }

        return success(list);
    }
    /**
     * 查询资产列表(分页)
     */
    @ApiOperation("查询巡查频率配置列表")
//    //@RequiresPermissions("patrol:frequencySettings:list")
    @GetMapping("/list")
    public MTableDataInfo list(@RequestParam Map<String, Object> params) {
//        R<List<DeptMaintenanceDTO>> listR = maintenanceSectionService.findUserDeptMaintenanceList(null, null);
//        System.out.println(listR);
        return patrolBridgeCheckService.getListMTableDataInfo(params);
    }


}
