package com.ruoyi.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackagePart;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.jdom2.Element;
import org.jdom2.Document;
import org.jdom2.input.SAXBuilder;
import org.jdom2.JDOMException;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Component
public class ExcelUtilService {


    /**
     * 读取excel表头
     *
     * @param headerRow 表头所在行，从1开始
     * @param file      文件
     * @return 数据
     */
    @SneakyThrows
    public List<String> readExcelHeaderFromFile(int headerRow, File file) {
        try (ExcelReader reader = ExcelUtil.getReader(file)) {
            return reader.readRow(headerRow - 1).stream()
                    .map(Object::toString)
                    .dropWhile(String::isEmpty)  // 删除末尾的空字符串
                    .collect(Collectors.toList());
        }
    }


    /**
     * 读取excel数据
     *
     * @param headers   表头
     * @param headerRow 表头所在行，从1开始
     * @param file      文件
     * @return 数据
     */
    @SneakyThrows
    public List<List<Object>> readExcelDataFromFile(List<String> headers, int headerRow, File file) {
        try (ExcelReader reader = ExcelUtil.getReader(file)) {
            int totalRows = reader.getSheet().getLastRowNum() + 1;
            List<List<Object>> dataList = new ArrayList<>();

            for (int i = headerRow; i < totalRows; i++) {
                List<Object> row = reader.readRow(i);
                List<Object> rowData = new ArrayList<>();

                if (row == null || row.isEmpty()) {
                    rowData.addAll(Collections.nCopies(headers.size(), null));
                } else {
                    for (int j = 0; j < headers.size(); j++) {
                        Object cellValue = (j < row.size()) ? row.get(j) : null;
                        if (cellValue != null && cellValue.toString().startsWith("=DISPIMG")) {
                            cellValue = null;
                        }
                        rowData.add(cellValue);
                    }
                }
                dataList.add(rowData);
            }
            return dataList;
        }
    }



    /**
     * 写入数据到模板excel
     *
     * @param startRow  从第几行开始写入，默认从1开始
     * @param writeData 写入数据
     * @param writer     写入文件
     */
    public void writeExcelData(int startRow, List<List<Object>> writeData, ExcelWriter writer) {
        writer.setCurrentRow(startRow);
        writer.write(writeData);
    }


    /**
     * 从远程读取url文件地址，存储到本地磁盘
     *
     * @param urlString 远程地址
     */
    @SneakyThrows
    public File writeFileFromUrl(String urlString) {
        URL url = new URL(urlString);
        URLConnection connection = url.openConnection();
        InputStream inputStream = connection.getInputStream();
        String tempDir = System.getProperty("java.io.tmpdir");
        log.info("临时存储目录：{}", tempDir);
        FileUtil.mkdir(tempDir);
        String savePath = tempDir + File.separator + UUID.randomUUID() + ".xlsx";
        log.info("临时存储文件路径：{}", savePath);
        File saveFile = new File(savePath);
        BufferedInputStream bis = new BufferedInputStream(inputStream);
        FileOutputStream fos = new FileOutputStream(saveFile);
        byte[] buffer = new byte[1024];
        int count;
        while ((count = bis.read(buffer)) != -1) {
            fos.write(buffer, 0, count);
        }
        bis.close();
        fos.close();
        return saveFile;
    }

    /**
     * 将File转换为MultipartFile
     *
     * @param file 文件
     */
    @SneakyThrows
    public MultipartFile getMultipartFileByFile(File file) {
        return new MockMultipartFile("file", file.getName(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", new FileInputStream(file));
    }


    public static void zipFiles(String sourcePath, String destinationPath) {
        try (ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(destinationPath))) {
            File sourceFile = new File(sourcePath);

            if (sourceFile.isDirectory()) {
                zipDirectory(sourceFile, sourceFile.getName(), zipOut);
            } else {
                zipFile(sourceFile, zipOut);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void zipDirectory(File directory, String basePath, ZipOutputStream zipOut) throws IOException {
        for (File file : directory.listFiles()) {
            if (file.isDirectory()) {
                zipDirectory(file, basePath + "/" + file.getName(), zipOut);
            } else {
                zipFile(file, basePath + "/" + file.getName(), zipOut);
            }
        }
    }

    private static void zipFile(File file, ZipOutputStream zipOut) throws IOException {
        zipFile(file, file.getName(), zipOut);
    }

    private static void zipFile(File file, String entryName, ZipOutputStream zipOut) throws IOException {
        try (FileInputStream fileIn = new FileInputStream(file)) {
            ZipEntry entry = new ZipEntry(entryName);
            zipOut.putNextEntry(entry);

            byte[] buffer = new byte[1024];
            int len;
            while ((len = fileIn.read(buffer)) > 0) {
                zipOut.write(buffer, 0, len);
            }

            zipOut.closeEntry();
        }
    }



    /**
     * @param writer      ExcelWriter
     * @param row         行
     * @param column      列
     * @param pictureData 图片数据
     * @param picType     图片格式
     */
    public void writeExcelPicData(ExcelWriter writer, int row, int column, byte[] pictureData, int picType) {
        Sheet sheet = writer.getSheet();
        Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
        // 设置图片单元格位置
        ClientAnchor anchor = drawingPatriarch.createAnchor(0, 0, 0, 0, column, row, column + 1, row + 1);
        // 随单元格改变位置和大小
        anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
        // 添加图片
        int pictureIndex = sheet.getWorkbook().addPicture(pictureData, picType);
        drawingPatriarch.createPicture(anchor, pictureIndex);
    }


    /**
     * 读取图片
     *
     * @param file   文件
     * @param writer ExcelWriter
     * @param height 高度
     */
    public void readPic(File file, ExcelWriter writer, Integer height) throws Exception {
        try (FileInputStream fis = new FileInputStream(file); XSSFWorkbook book = new XSSFWorkbook(fis)) {
            for (Sheet sheet : book) {
                if (!(sheet instanceof XSSFSheet xssSheet)) continue;

                XSSFDrawing drawing = xssSheet.getDrawingPatriarch();
                if (drawing == null) continue;

                List<XSSFShape> shapes = drawing.getShapes();
                for (XSSFShape shape : shapes) {
                    if (!(shape instanceof XSSFPicture pic)) continue;

                    XSSFClientAnchor anchor = (XSSFClientAnchor) shape.getAnchor();
                    XSSFPictureData picData = pic.getPictureData();
                    byte[] data = picData.getData();
                    short c1 = anchor.getCol1();
                    int r1 = anchor.getRow1();
                    writeExcelPicData(writer, r1 + height, c1, data, picData.getPictureType());
                }
            }
        }
    }


    /**
     * 读取嵌入图片
     *
     * @param file   文件
     * @param writer ExcelWriter
     * @param height 高度
     */
    public void readEmbed(File file, ExcelWriter writer, Integer height) throws JDOMException, IOException, ParserConfigurationException, SAXException, InvalidFormatException {
        // 包管理工具打开压缩包
        try (OPCPackage opc = OPCPackage.open(new FileInputStream(file))) {
            // 获取所有包文件
            List<PackagePart> parts = opc.getParts();
            Map<Integer, Map<String, PackagePart>> picturePath = getEmbedPictures(parts);

            for (Map.Entry<Integer, Map<String, PackagePart>> entry : picturePath.entrySet()) {
                Integer key = entry.getKey();
                Map<String, PackagePart> crMap = entry.getValue();

                for (Map.Entry<String, PackagePart> crEntry : crMap.entrySet()) {
                    String crKey = crEntry.getKey();
                    PackagePart part = crEntry.getValue();

                    if (part != null) {
                        try (InputStream imgIs = part.getInputStream()) {
                            byte[] imgData = IOUtils.toByteArray(imgIs);
                            String[] coordinates = crKey.split("_");
                            int row = Integer.parseInt(coordinates[1]) + height;
                            int col = Integer.parseInt(coordinates[0]);

                            // 传递图片数据到 writeExcelPicData 方法
                            writeExcelPicData(writer, row, col, imgData, Workbook.PICTURE_TYPE_PNG);
                        }
                    }
                }
            }
        } catch (IOException | InvalidFormatException e) {
            // 处理异常
            log.error("读取嵌入图片失败", e);
        }
    }


    private static Map<Integer, Map<String, PackagePart>> getEmbedPictures(List<PackagePart> parts)
            throws JDOMException, IOException, ParserConfigurationException, SAXException {

        Map<String, Set<String>> mapImg = new ConcurrentHashMap<>();
        Map<String, String> mapImgPath = new ConcurrentHashMap<>();
        Map<Integer, Map<String, String>> crDataMap = new ConcurrentHashMap<>();

        parts.parallelStream().forEach(part -> {
            try {
                String name = part.getPartName().getName();

                if ("/xl/cellimages.xml".equals(name)) {
                    parseCellImages(part, mapImg);
                } else if ("/xl/_rels/cellimages.xml.rels".equals(name)) {
                    parseImageRelationships(part, mapImgPath);
                } else if (name.contains("/xl/worksheets/sheet")) {
                    parseSheet(part, crDataMap, name);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        Map<String, String> imgMap = mapImg.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream().map(id -> Map.entry(id, mapImgPath.get(entry.getKey()))))
                .collect(Collectors.toConcurrentMap(Map.Entry::getKey, Map.Entry::getValue));

        crDataMap.forEach((key, crMap) -> crMap.replaceAll((crKey, imgId) -> imgMap.getOrDefault(imgId, imgId)));

        return crDataMap.entrySet().stream()
                .collect(Collectors.toConcurrentMap(Map.Entry::getKey, entry -> createPartMap(entry.getValue(), parts)));
    }

    private static void parseCellImages(PackagePart part, Map<String, Set<String>> mapImg)
            throws JDOMException, IOException {
        SAXBuilder builder = new SAXBuilder();
        Document doc = builder.build(part.getInputStream());
        Element root = doc.getRootElement();

        for (Element imgEle : root.getChildren()) {
            Element xdrPic = imgEle.getChildren().get(0);
            Element xdrNvPicPr = xdrPic.getChildren().get(0);
            Element xdrBlipFill = xdrPic.getChildren().get(1);
            Element aBlip = xdrBlipFill.getChildren().get(0);
            String imgId = xdrNvPicPr.getChildren().get(0).getAttributeValue("name");
            String id = aBlip.getAttributes().get(0).getValue();

            mapImg.computeIfAbsent(id, k -> Collections.newSetFromMap(new ConcurrentHashMap<>())).add(imgId);
        }
    }

    private static void parseImageRelationships(PackagePart part, Map<String, String> mapImgPath)
            throws JDOMException, IOException {
        SAXBuilder builder = new SAXBuilder();
        Document doc = builder.build(part.getInputStream());
        Element root = doc.getRootElement();

        for (Element relationship : root.getChildren()) {
            String id = relationship.getAttributeValue("Id");
            String target = relationship.getAttributeValue("Target");
            mapImgPath.put(id, target);
        }
    }

    private static void parseSheet(PackagePart part, Map<Integer, Map<String, String>> crDataMap, String name)
            throws ParserConfigurationException, SAXException, IOException {
        int sheetNo = Integer.parseInt(name.replace("/xl/worksheets/sheet", "").replace(".xml", "")) - 1;
        SAXParserFactory factory = SAXParserFactory.newInstance();
        SAXParser parser = factory.newSAXParser();
        MySAXParserHandler handler = new MySAXParserHandler();
        parser.parse(part.getInputStream(), handler);
        crDataMap.put(sheetNo, handler.getCRMap());
    }

    private static Map<String, PackagePart> createPartMap(Map<String, String> crMap, List<PackagePart> parts) {
        Map<String, PackagePart> partMap = new ConcurrentHashMap<>();
        crMap.forEach((crKey, path) -> {
            if (StringUtils.isNotEmpty(path)) {
                parts.parallelStream()
                        .filter(part -> part.getPartName().getName().contains(path))
                        .findFirst()
                        .ifPresent(part -> partMap.put(crKey, part));
            } else {
                partMap.put(crKey, null);
            }
        });
        return partMap;
    }

}
