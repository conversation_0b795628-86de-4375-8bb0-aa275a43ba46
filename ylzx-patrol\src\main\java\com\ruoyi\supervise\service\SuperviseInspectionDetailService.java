package com.ruoyi.supervise.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.supervise.domain.SuperviseInspectionDetail;

/**
 * 督查详情Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface SuperviseInspectionDetailService extends IService<SuperviseInspectionDetail> {

    /**
     * 根据条件查询督查详情数据列表
     * @param params
     */
    List<SuperviseInspectionDetail> findListByParam(Map<String, Object> params);

}
