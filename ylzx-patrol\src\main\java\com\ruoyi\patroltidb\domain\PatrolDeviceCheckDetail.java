package com.ruoyi.patroltidb.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 隧道机电巡查明细对象 patrol_device_check_detail
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="隧道机电巡查明细")
@TableName("patrol_device_check_detail")
@Data
public class PatrolDeviceCheckDetail extends PatrolDeviceBase {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *  递增序号
     */
    @TableField(exist = false)
    @Excel(name = "序号", sort = 0)
    private Integer orderNumber;

    /** 内容 */
    @Excel(name = "检查内容")
    @ApiModelProperty(value = "内容")
    private String content;

    /** 结果（1正常，2异常，3异常且严重） */
    @Excel(name = "检查结果", readConverterExp = "1=正常,2=异常,3=异常且严重")
    @ApiModelProperty(value = "结果（1正常，2异常，3异常且严重）")
    private Long result;

    /** 图片路径 */
//    @Excel(name = "图片路径")
    @ApiModelProperty(value = "图片路径")
    private String picPaths;

    @ApiModelProperty(value = "图片路径URL")
    @TableField(exist = false)
    private String picPathsUrl;

    /** 车牌 */
    @Excel(name = "车牌号")
    @ApiModelProperty(value = "车牌")
    private String carLicense;

    /** 数量 */
    @Excel(name = "车辆台数")
    @ApiModelProperty(value = "数量")
    private Long nums;

    /** 备注 */
    @TableField(exist = false)
    private String remark;

}
