package com.ruoyi.repote.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.repote.domain.RepoteAnnex;
import com.ruoyi.repote.service.RepoteAnnexService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 上传附件Controller
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Api(tags = "上传附件" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/annex")
public class RepoteAnnexController extends BaseController {
    final private RepoteAnnexService repoteAnnexService;


    /**
     * 查询上传附件列表(分页)
     */
    @ApiOperation("查询上传附件列表")
    //@RequiresPermissions("repote:annex:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<RepoteAnnex> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<RepoteAnnex> list = repoteAnnexService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询上传附件列表(不分页)
     */
    @ApiOperation("查询上传附件列表(不分页)")
    //@RequiresPermissions("repote:annex:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<RepoteAnnex> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<RepoteAnnex> list = repoteAnnexService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询上传附件数据
     */
    @ApiOperation("根据id查询上传附件数据")
    //@RequiresPermissions("repote:annex:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            RepoteAnnex repoteAnnex = repoteAnnexService.getById(id);
        if (repoteAnnex == null) {
            return error("未查询到【上传附件】记录");
        }
        return success(repoteAnnex);
    }

    /**
     * 新增上传附件
     */
    @ApiOperation("新增上传附件")
    //@RequiresPermissions("repote:annex:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody RepoteAnnex repoteAnnex) {
        return toAjax(repoteAnnexService.save(repoteAnnex));
    }

    /**
     * 修改上传附件
     */
    @ApiOperation("修改上传附件")
    //@RequiresPermissions("repote:annex:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody RepoteAnnex repoteAnnex) {
        return toAjax(repoteAnnexService.updateById(repoteAnnex));
    }

    /**
     * 删除上传附件
     */
    @ApiOperation("删除上传附件")
    //@RequiresPermissions("repote:annex:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(repoteAnnexService.removeById(id));
    }

    /**
     * 导出上传附件列表
     */
    @ApiOperation("导出上传附件列表")
    //@RequiresPermissions("repote:annex:export")
    @Log(title = "上传附件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<RepoteAnnex> list = repoteAnnexService.list();
        ExcelUtil<RepoteAnnex> util = new ExcelUtil<RepoteAnnex>(RepoteAnnex.class);
        util.exportExcel(response, list, "上传附件数据");
    }


}
