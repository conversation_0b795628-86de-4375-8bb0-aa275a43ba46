<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.other.mapper.OtherTransportationDeptMapper">

    <resultMap type="com.ruoyi.other.domain.OtherTransportationDept" id="OtherTransportationDeptResult">
        <result property="id" column="id"/>
        <result property="transportationId" column="transportation_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="base_column">
        id, transportation_id, dept_name, dept_id, create_by, create_time, update_by,update_time   </sql>

    <sql id="where_column">
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="transportationId != null and transportation_id != ''">
            AND transportation_id = #{transportation_id}
        </if>
        <if test="deptId != null and deptId != ''">
            AND dept_id = #{deptId}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name = #{deptName}
        </if>
    </sql>

    <sql id="set_column">
        <if test="transportationId != null">
            transportation_id = #{transportationId},
        </if>
        <if test="deptName != null">
            dept_name = #{deptName},
        </if>
        <if test="deptId != null">
            dept_id = #{deptId},
        </if>

    </sql>

    <!-- 根据 transportationId 更新部门信息 -->
    <update id="updateDepartmentByTransportationId">
        UPDATE other_transportation_dept
        SET
            dept_id = #{deptId},
            dept_name = #{deptName}
        WHERE
            transportation_id = #{transportationId}
    </update>
    <delete id="deleteByTransportationIdAndDeptId">
        DELETE FROM other_transportation_dept
        WHERE transportation_id = #{transportationId}
    </delete>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="OtherTransportationDeptResult">
        SELECT
        <include refid="base_column"/>
        FROM other_transportation_dept
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="OtherTransportationDeptResult">
        SELECT
        <include refid="base_column"/>
        FROM other_transportation_dept
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
    </select>
    <!-- 通过ID查找管理处List -->
    <select id="getManagementDepartmentIdsByTransportationId" resultType="java.lang.String">
        SELECT
            dept_id
        FROM
            other_transportation_dept
        WHERE
            transportation_id = #{id}
    </select>

    <!-- 根据传入的部门ID列表查询管理处ID -->
    <select id="getTransportationDeptByIds" resultType="java.lang.String">
        SELECT transportation_id
        FROM other_transportation_dept
        WHERE dept_id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>