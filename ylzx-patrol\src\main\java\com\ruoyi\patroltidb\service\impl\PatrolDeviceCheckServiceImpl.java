package com.ruoyi.patroltidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.file.MiniOStorage;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.service.BaseTunnelResponseCacheService;
import com.ruoyi.patrol.utils.file.ExcelUtilService;
import com.ruoyi.patroltidb.domain.*;
import com.ruoyi.patroltidb.domain.excel.DeviceMonthlyReportExport;
import com.ruoyi.patroltidb.mapper.PatrolDeviceCheckMapper;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.patroltidb.service.PatrolDeviceCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolDeviceCheckService;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.ImageTextRequest;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.vo.SysFileSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ruoyi.common.core.utils.PageUtils.startPage;
import static com.ruoyi.patrol.enums.InspectionType.DEVICE_REGULAR_INSPECTION;

/**
 * 隧道机电日常巡查Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
@Service
@Slave
public class PatrolDeviceCheckServiceImpl extends ServiceImpl<PatrolDeviceCheckMapper, PatrolDeviceCheck> implements PatrolDeviceCheckService {

//    @Resource
//    private PatrolDeviceCheckMapper patrolDeviceCheckMapper;
    @Resource
    private RemoteDeptAuthService deptAuthService;
    @Resource
    private RemoteUserService userService;
    @Resource
    private RemoteFileService remoteFileService;
    @Resource
    private PatrolDeviceCheckDetailService patrolDeviceCheckDetailService;
    @Resource
    private ExcelUtilService excelUtil;
    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;
    @Resource
    private BaseTunnelResponseCacheService baseTunnelResponseCacheService;
    @Resource
    private RemoteUserService remoteUserService;
    @Autowired
    private PatrolDeviceCheckMapper patrolDeviceCheckMapper;

    @Override
    public List<PatrolDeviceCheck> findListByParam(Map<String, Object> params) {
        startPage();
        List<PatrolDeviceCheck> list = baseMapper.findListByParam(params);
//        Set<Long> domainIds = list.stream().map(PatrolDeviceCheck::getDomainId).collect(Collectors.toSet());
//        R<List<SysDept>> listR = deptAuthService.getByDeptIdList(domainIds.stream().toList());
//        for (PatrolDeviceCheck deviceCheck : list) {
//            if (null != listR.getData()){
//                for (SysDept sysDept : listR.getData()) {
//                    if (null != deviceCheck.getDomainId() && deviceCheck.getDomainId().equals(sysDept.getDeptId())) {
//                        deviceCheck.setDeptName(sysDept.getDeptName());
//                    }
//                }
//            }
//        }
        return list;
    }

    @Override
    public List<PatrolDeviceCheckDetail> findDetailListByParam(Map<String, Object> params) {
        List<PatrolDeviceCheckDetail> list = baseMapper.findDetailListByParam(params);
        this.setDeptName(list);
//        this.setCheckerUrl(list, new HashMap<>());
//        this.picPathsUrl(list, new HashMap<>());
//        this.updateUrl(list);
        return list;
    }

    @Override
    public List<PatrolDeviceCheckDetail> findDetailListByParam(Map<String, Object> params, Map<String, String> signUrlMap) {
        List<PatrolDeviceCheckDetail> list = baseMapper.findDetailListByParam(params);
        this.setDeptName(list);
        this.setCheckerUrl(list, signUrlMap);
        this.picPathsUrl(list, signUrlMap);
//        this.updateUrl(list);
        return list;
    }

    @Override
    public List<PatrolDeviceFaultRecord> findFaultListByParam(Map<String, Object> params, Map<String, String> signUrlMap) {
        List<PatrolDeviceFaultRecord> list = baseMapper.findFaultListByParam(params);
        this.setDeptName(list);
        this.setCheckerUrl(list, signUrlMap);
        return list;
    }

    @Override
    public List<PatrolDeviceFaultRecord> findFaultListByParam(Map<String, Object> params) {
        List<PatrolDeviceFaultRecord> list = baseMapper.findFaultListByParam(params);
        this.setDeptName(list);
//        this.setCheckerUrl(list, new HashMap<>());
        return list;
    }

    @Override
    public Long findFaultListByParamCount(Map<String, Object> params) {
        return baseMapper.findFaultListByParamCount(params);
    }

    @Override
    public Long findDetailListByParamCount(Map<String, Object> params) {
        return baseMapper.findDetailListByParamCount(params);
    }

    @Override
    public List<DeviceMonthlyReportExport> listMonthlyReport(Map<String, Object> params) {
        List<DeviceMonthlyReportExport> list = baseMapper.listMonthlyReport(params);
        // 查询部门名称
        Set<Long> domainIds = list.stream().map(DeviceMonthlyReportExport::getDomainId).collect(Collectors.toSet());
        R<List<SysDept>> listR = deptAuthService.getByDeptIdList(domainIds.stream().toList());
        for (DeviceMonthlyReportExport deviceCheck : list) {
            if (null != listR.getData()){
                for (SysDept sysDept : listR.getData()) {
                    if (null != deviceCheck.getDomainId() && deviceCheck.getDomainId().equals(sysDept.getDeptId())) {
                        deviceCheck.setDeptName(sysDept.getDeptName());
                    }
                }
            }
        }
        return list;
    }

    @Override
    public List<PatrolDeviceFaultRecord> getByAssetIdFault(Map<String, Object> params) {
        List<PatrolDeviceFaultRecord> list = baseMapper.getByAssetIdFault(params);
        this.setDeptName(list);
        return list;
    }

    private <T extends PatrolDeviceBase> void setDeptName(List<T> list) {
        // 查询部门名称
        Set<Long> domainIds = list.stream().map(T::getDomainId).collect(Collectors.toSet());
        R<List<SysDept>> listR = deptAuthService.getByDeptIdList(domainIds.stream().toList());
        for (T deviceCheck : list) {
            if (null != listR.getData()){
                for (SysDept sysDept : listR.getData()) {
                    if (null != deviceCheck.getDomainId() && deviceCheck.getDomainId().equals(sysDept.getDeptId())) {
                        deviceCheck.setDeptName(sysDept.getDeptName());
                    }
                }
            }
        }
    }


    private <T extends PatrolDeviceBase> void setCheckerUrl(List<T> list, Map<String, String> signUrlMap) {
        // 查询检查人、记录人签名图片
        List<Long> userIdList = Stream.concat(list.stream().map(PatrolDeviceBase::getCheckerId)
                        , list.stream().map(PatrolDeviceBase::getRecorderId))
                .filter(Objects::nonNull).distinct().toList();
        if (!userIdList.isEmpty()) {
            List<SysUser> userList = userService.findListParam(userIdList).getData();
            if (!userList.isEmpty()) {
                List<String> signIds = userList.stream().map(SysUser::getSignId).toList();
                List<String> names = userList.stream().map(SysUser::getNickName).toList();
                // 批量查询文件信息
                if (!signIds.isEmpty()) {
                    try {
                        // 获取签名URL
                        ImageTextRequest imageTextRequest = new ImageTextRequest();
                        imageTextRequest.setOwnerIdList(new ArrayList<>(signIds));
                        imageTextRequest.setNameList(new ArrayList<>(names));
                        R<Map<String,String>> resultMap = remoteFileService.getUserSign(imageTextRequest);
                        if(resultMap.getCode() != 200){
                            log.error("获取签名失败");
                        }
                        // 获取签名URL映射
                        signUrlMap.putAll(resultMap.getData());
                        for (T t : list) {
                            for (SysUser sysUser : userList) {
                                if (sysUser.getUserId().equals(t.getCheckerId())) {
                                    t.setCheckerUrl(signUrlMap.get(sysUser.getSignId()));
                                    t.setKahunaSignList(List.of(sysUser.getSignId()));
                                }
                                if (sysUser.getUserId().equals(t.getRecorderId())) {
                                    t.setRecorderUrl(signUrlMap.get(sysUser.getSignId()));
                                    t.setOprUserSignList(List.of(sysUser.getSignId()));
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("批量获取文件URL失败, signIds: {}");
                    }
                }
            }

        }
    }

    private void picPathsUrl(List<PatrolDeviceCheckDetail> list, Map<String, String> signUrlMap) {
        // 查询经常检查详情图片URL
        Set<String> picPaths = list.stream().map(PatrolDeviceCheckDetail::getPicPaths).collect(Collectors.toSet());
        // 批量查询文件信息
        final Map<String, String> signIdToUrlMap = new HashMap<>();
        if (!picPaths.isEmpty()) {
            try {
                R<List<SysFile>> filesResult = remoteFileService.findFilesByOwnerIds(new ArrayList<>(picPaths));
                if (filesResult.getCode() == 200 && filesResult.getData() != null) {
                    signIdToUrlMap.putAll(filesResult.getData().stream()
                            .collect(Collectors.toMap(
                                    SysFile::getOwnerId,
                                    SysFile::getUrl,
                                    (v1, v2) -> v1
                            )));
                    signUrlMap.putAll(signIdToUrlMap);
                    list.forEach(t -> t.setPicPathsUrl(signIdToUrlMap.get(t.getPicPaths())));
                }
            } catch (Exception e) {
                log.error("批量获取文件URL失败, signIds: {}");
            }
        }

    }

    /**
     * 重新储存带有中文路径的图片，并返回新的路径
     * @param list
     */
    private void updateUrl(List<PatrolDeviceCheckDetail> list) {
        for (PatrolDeviceCheckDetail detail : list) {
            updateUrlIfChinese(detail::getCheckerUrl, detail::setCheckerUrl);
            updateUrlIfChinese(detail::getRecorderUrl, detail::setRecorderUrl);
            updateUrlIfChinese(detail::getPicPathsUrl, detail::setPicPathsUrl);
        }
    }

    private void updateUrlIfChinese(Supplier<String> urlGetter, Consumer<String> urlSetter) {
        String url = urlGetter.get();
        if (isChineseRegex(url)) {
            File modeFile = excelUtil.writeFileFromUrl(url);
            SysFileSearchVO sysFileSearchVO = createFileSearchVO();
            R<SysFile> r = remoteFileService.upload(excelUtil.getMultipartFileByFile(modeFile), sysFileSearchVO);
            if (r.getCode() == 200) {
                urlSetter.accept(r.getData().getUrl());
            }
        }
    }

    private SysFileSearchVO createFileSearchVO() {
        SysFileSearchVO sysFileSearchVO = new SysFileSearchVO();
        String ownerId = IdWorker.getIdStr();
        sysFileSearchVO.setOwnerId(ownerId);
        sysFileSearchVO.setPlatform(MiniOStorage.MINIO2.getStorage());
        sysFileSearchVO.setStoragePath("/ylzx-patrol/device");
        return sysFileSearchVO;
    }


    /**
     * 使用正则表达式检测字符串中是否包含中文字符
     * @param str 待检测字符串
     * @return 包含中文返回true，否则返回false
     */
    public static boolean isChineseRegex(String str) {
        if (str == null) return false;
        return Pattern.compile("[\u4E00-\u9FA5]").matcher(str).find();
    }


    /**
     * 删除（带子表）
     * @param list
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<?> list) {
        QueryWrapper<PatrolDeviceCheckDetail> qw;
        qw = new QueryWrapper<>();
        qw.in("id", list);
        List<PatrolDeviceCheckDetail> detailList = patrolDeviceCheckDetailService.list(qw);
        boolean a = patrolDeviceCheckDetailService.removeByIds(list);//删除子表
        Set<String> checkIds = detailList.stream().map(PatrolDeviceCheckDetail::getCheckId).collect(Collectors.toSet());
        //查询子表关联的主表是否可删除
        for (String checkId : checkIds) {
            qw = new QueryWrapper<>();
            qw.eq("check_id", checkId);
            long count = patrolDeviceCheckDetailService.count(qw);
            if (count <= 0L) super.removeById(checkId);
        }
        return a;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddAll(PatrolDeviceCheck patrolDeviceCheck) {
        // 根据assetIds分出批量主表
        if (!patrolDeviceCheck.getAssetIds().isEmpty() && patrolDeviceCheck.getRecordType() == 2) {
            AssetBaseDataRequest request = AssetBaseDataRequest.builder()
                    .ids(patrolDeviceCheck.getAssetIds())
//                    .dataRule(true)
                    .build();
            List<BaseTunnelResponseCache> baseData = baseTunnelResponseCacheService.selectAssetBaseData(request, null, null);
            PatrolDeviceCheck entity;
            // 获取要添加的每一天
            List<Date> checkTimes = getDatesBetween(patrolDeviceCheck.getOprTimes(), patrolDeviceCheck.getOprTimee());
            List<SysUser> userList = new ArrayList<>();
            Random random = new Random();
            for (BaseTunnelResponseCache tunnel : baseData) {
                if (tunnel.getAssetId() != null) {
                    //根据资产ID查询资产所属部门（仅隧管站），并获取隧管站下面的用户
                    R<List<SysDept>> deptR = deptAuthService.getSubByAssetId(tunnel.getAssetId());
                    if (deptR.getCode() == 200 && deptR.getData() != null) {
                        List<Long> deptIdList = deptR.getData().stream().map(SysDept::getDeptId).toList();
                        R<List<SysUser>> listR = remoteUserService.findListByDeptId(deptIdList);
                        if (listR.getCode() == 200) {
                            userList = listR.getData();
                        }
                    }
                }
                for (Date checkTime : checkTimes) {
                    entity = new PatrolDeviceCheck();
                    BeanUtils.copyProperties(patrolDeviceCheck, entity);
                    entity.setAssetId(tunnel.getAssetId());
                    entity.setAssetCode(tunnel.getAssetCode());
                    entity.setAssetName(tunnel.getAssetName());
                    entity.setRouteCode(tunnel.getRouteCode());
                    entity.setRouteName(tunnel.getMaintenanceSectionName());//养护路段
                    entity.setDeptName(tunnel.getManagementMaintenanceName());//管养单位
                    if (tunnel.getManagementMaintenanceId() != null)
                        entity.setDomainId(Long.valueOf(tunnel.getManagementMaintenanceId()));

                    entity.setCheckTime(checkTime);

                    entity.setType(DEVICE_REGULAR_INSPECTION);
                    patrolAssetCheckService.setExpiryAndFrequency(entity);

                    //随机取一个用户用于记录人
                    if (!userList.isEmpty()) {
                        int randomIndex = random.nextInt(userList.size());
                        SysUser sysUser = userList.get(randomIndex);
                        if (sysUser != null) {
                            entity.setRecorderId(sysUser.getUserId());
                            entity.setRecorder(sysUser.getNickName());
                        }
                    }

                    patrolDeviceCheckMapper.insert(entity);
                    if (!entity.getDomains().isEmpty()) {
                        String checkId = entity.getId();
                        entity.getDomains().forEach((item) -> {
                            item.setId(IdWorker.getIdStr());
                            item.setCheckId(checkId);
                        });
                        patrolDeviceCheckDetailService.saveBatch(entity.getDomains());
                    }
                }
            }
        }
    }

    /**
     * 获取两个Date之间的所有日期（包含起止日期）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期列表
     */
    public static List<Date> getDatesBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return List.of(new Date());
        }
        // 转换为LocalDate
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算天数并生成日期流
        long numOfDays = java.time.temporal.ChronoUnit.DAYS.between(start, end) + 1;

        return Stream.iterate(start, date -> date.plusDays(1))
                .limit(numOfDays)
                .map(date -> Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                .collect(Collectors.toList());
    }

}
