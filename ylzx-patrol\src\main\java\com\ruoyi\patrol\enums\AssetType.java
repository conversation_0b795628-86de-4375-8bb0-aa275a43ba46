package com.ruoyi.patrol.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Getter
@RequiredArgsConstructor
@Slf4j
public enum AssetType  implements IEnum<Integer> {
    BRIDGE(1, "桥梁", "patrol_bridge_check","patrol_bridge_check_detail",107L),
    TUNNEL(2, "隧道" , "patrol_tunnel_check","patrol_tunnel_check_detail",145L),
    CULVERT(3, "涵洞" , "patrol_culvert_check","patrol_culvert_check_detail",108L),
    DEVICE(4, "机电" , "patrol_device_check","patrol_device_check_detail",-1L),;


    private final Integer code;
    private final String description;
    private final String tableName;
    private final String detailTableName;
    private final Long sysAssetType;

    @Override
    public Integer getValue() {
        return this.code;
    }

    @JsonValue
    public Integer getCode() {
        return this.code;
    }
    @JsonCreator
    public static AssetType fromCode(Integer code) {
        for (AssetType type : AssetType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        log.warn("未知的: {}", code);
        return null;
    }

    public static AssetType fromSysAssetType(Long sysAssetType) {
        for (AssetType type : AssetType.values()) {
            if (type.getSysAssetType().equals(sysAssetType)) {
                return type;
            }
        }
        log.warn("未知的: {}", sysAssetType);
        return null;
    }
}
