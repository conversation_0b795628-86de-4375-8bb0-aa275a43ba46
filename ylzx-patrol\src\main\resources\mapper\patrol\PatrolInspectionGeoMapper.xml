<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.PatrolInspectionGeoMapper">


    <resultMap id="BaseResultMap" type="com.ruoyi.patrol.domain.PatrolInspectionGeo">
        <id column="log_Id" property="logId" />
        <result column="shape" property="shape" jdbcType="VARCHAR" typeHandler="com.ruoyi.patrol.utils.handler.GeometryTypeHandler"/>
    </resultMap>

    <sql id="Base_Column_List">
        log_Id, shape
    </sql>

    <select id="selectGeoById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM patrol_inspection_geo
        WHERE log_id = #{logId}
    </select>
    <!-- Other CRUD operations -->
</mapper>
