package com.ruoyi.patrol.task;

import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.patrol.domain.PatrolInspectionGeo;
import com.ruoyi.patrol.domain.PatrolInspectionLogs;
import com.ruoyi.patrol.mapper.PatrolInspectionLogsMapper;
import com.ruoyi.patrol.service.PatrolInspectionGeoService;
import com.ruoyi.patrol.utils.GeoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class PatrolGeoSyncTask {
    
//    @Resource
//    private RedisService redisService;
//
//    @Resource
//    private PatrolInspectionGeoService patrolGeoService;
//
//    @Resource
//    private PatrolInspectionLogsMapper patrolInspectionLogsMapper;
//
//    private static final int BATCH_SIZE = 10; // 每次处理的批次大小
//    private static final int MIN_POINTS_THRESHOLD = 80; // 设置最小轨迹点数阈值
//
//    /**
//     * 每5分钟执行一次
//     */
//    @Scheduled(cron = "37 */2 * * * ?")
//    public void syncGeoData() {
//        try {
//            String pattern = "patrol:geo:*";
//            List<String> keys = new ArrayList<>(redisService.keys(pattern));
//            if (keys.isEmpty()) {
//                return;
//            }
//
//            // 随机打乱键的顺序，避免每次都以相同顺序处理
//            Collections.shuffle(keys);
//
//            // 分批处理数据
//            for (int i = 0; i < keys.size(); i += BATCH_SIZE) {
//                int end = Math.min(i + BATCH_SIZE, keys.size());
//                List<String> batchKeys = keys.subList(i, end);
//
//                processBatch(batchKeys);
//
//                // 每批处理完后稍作延迟，避免集中处理
//                if (end < keys.size()) {
//                    Thread.sleep(1000);
//                }
//            }
//        } catch (Exception e) {
//            log.error("同步巡查地理数据失败", e);
//        }
//    }
//
//    private void processBatch(List<String> keys) {
//        List<PatrolInspectionLogs> logs = new ArrayList<>();
//        for (String key : keys) {
//            try {
//                String wkt = redisService.getCacheObject(key);
//                if (wkt != null) {
//                    // 简单统计轨迹点数量（通过计算逗号数量）
//                    int pointCount = wkt.split(",").length;
//
//                    // 当轨迹点数量达到阈值时进行保存
//                    if (pointCount >= MIN_POINTS_THRESHOLD) {
//                        log.info("key[{}]的轨迹点数[{}]达到阈值，执行保存", key, pointCount);
//                        String logId = key.substring(key.lastIndexOf(":") + 1);
//                        PatrolInspectionGeo geo = new PatrolInspectionGeo();
//                        geo.setLogId(logId);
//                        geo.setShape(wkt);
//                        BigDecimal length =
//                        patrolGeoService.mergeAndSaveToDb(geo);
//                        PatrolInspectionLogs log = new PatrolInspectionLogs();
//                        log.setId(logId);
//                        log.setPatrolMileage(length);
//                        logs.add(log);
//                        // 保存后删除redis中的数据
//                        redisService.deleteObject(key);
//                    }
//                }
//            } catch (Exception e) {
//                log.error("处理key[{}]失败", key, e);
//            }
//        }
//        if(!logs.isEmpty()){
//            patrolInspectionLogsMapper.batchUpdateMileage(logs);
//        }
//    }
} 