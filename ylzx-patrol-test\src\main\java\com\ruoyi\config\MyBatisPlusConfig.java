package com.ruoyi.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.model.LoginUser;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Configuration
public class MyBatisPlusConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        //分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        //乐观锁支持@Version
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        //防全表更新或删除插件
//        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
        return interceptor;
    }

    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {

            @Override
            public void insertFill(MetaObject metaObject) {
                setFieldValByName("createTime", LocalDateTime.now(), metaObject);
                setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
//                setFieldValByName("deleteFlag", Integer.valueOf(0), metaObject);

                LoginUser loginUser = SecurityUtils.getLoginUser();
                if (loginUser != null) {
                    setFieldValByName("createBy", loginUser.getUsername(), metaObject);
                    setFieldValByName("updateBy", loginUser.getUsername(), metaObject);
                }

            }

            @Override
            public void updateFill(MetaObject metaObject) {
                setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
                LoginUser loginUser = SecurityUtils.getLoginUser();
                if (loginUser != null) {
                    setFieldValByName("updateBy", loginUser.getUsername(), metaObject);
                }
            }

        };
    }

}
