package com.ruoyi.patroltidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.patroltidb.domain.PatrolDeviceCheckDetail;
import com.ruoyi.patroltidb.service.PatrolDeviceCheckDetailService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 隧道机电巡查明细Controller
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */
@Api(tags = "隧道机电巡查明细" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/deviceCheckDetail")
public class PatrolDeviceCheckDetailController extends BaseController {
    final private PatrolDeviceCheckDetailService patrolDeviceCheckDetailService;


    /**
     * 查询隧道机电巡查明细列表(分页)
     */
    @ApiOperation("查询隧道机电巡查明细列表")
    //@RequiresPermissions("patrol:deviceCheckDetail:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<PatrolDeviceCheckDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<PatrolDeviceCheckDetail> list = patrolDeviceCheckDetailService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询隧道机电巡查明细列表(不分页)
     */
    @ApiOperation("查询隧道机电巡查明细列表(不分页)")
    //@RequiresPermissions("patrol:deviceCheckDetail:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolDeviceCheckDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolDeviceCheckDetail> list = patrolDeviceCheckDetailService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询隧道机电巡查明细数据
     */
    @ApiOperation("根据id查询隧道机电巡查明细数据")
    //@RequiresPermissions("patrol:deviceCheckDetail:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            PatrolDeviceCheckDetail patrolDeviceCheckDetail = patrolDeviceCheckDetailService.getById(id);
        if (patrolDeviceCheckDetail == null) {
            return error("未查询到【隧道机电巡查明细】记录");
        }
        return success(patrolDeviceCheckDetail);
    }

    /**
     * 新增隧道机电巡查明细
     */
    @ApiOperation("新增隧道机电巡查明细")
    //@RequiresPermissions("patrol:deviceCheckDetail:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolDeviceCheckDetail patrolDeviceCheckDetail) {
        return toAjax(patrolDeviceCheckDetailService.save(patrolDeviceCheckDetail));
    }

    /**
     * 修改隧道机电巡查明细
     */
    @ApiOperation("修改隧道机电巡查明细")
    //@RequiresPermissions("patrol:deviceCheckDetail:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolDeviceCheckDetail patrolDeviceCheckDetail) {
        return toAjax(patrolDeviceCheckDetailService.updateById(patrolDeviceCheckDetail));
    }

    /**
     * 删除隧道机电巡查明细
     */
    @ApiOperation("删除隧道机电巡查明细")
    //@RequiresPermissions("patrol:deviceCheckDetail:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolDeviceCheckDetailService.removeById(id));
    }

    /**
     * 导出隧道机电巡查明细列表
     */
    @ApiOperation("导出隧道机电巡查明细列表")
    //@RequiresPermissions("patrol:deviceCheckDetail:export")
    @Log(title = "隧道机电巡查明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolDeviceCheckDetail> list = patrolDeviceCheckDetailService.list();
        ExcelUtil<PatrolDeviceCheckDetail> util = new ExcelUtil<PatrolDeviceCheckDetail>(PatrolDeviceCheckDetail.class);
        util.exportExcel(response, list, "隧道机电巡查明细数据");
    }


}
