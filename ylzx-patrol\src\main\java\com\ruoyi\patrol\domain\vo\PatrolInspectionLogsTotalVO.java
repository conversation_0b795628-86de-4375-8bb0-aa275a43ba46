package com.ruoyi.patrol.domain.vo;


import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 巡查日志对象 patrol_inspection_logs
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PatrolInspectionLogsTotalVO extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    @Excel( name = "养护路段")
    private String maintenanceSectionName;

    @Excel( name = "管理处")
    private String maintenanceUnitName;

    @Excel( name = "分处")
    private String maintenanceSubUnitName;

    @Excel( name = "巡查单位")
    private String patrolUnitName;

    @Excel( name = "巡查日期")
    private String startTime;

    @Excel( name = "巡查次数")
    private String count;

    @Excel( name = "巡查里程")
    private String patrolMileage;

    @Excel( name = "桥梁巡查记录(异常/总数)")
    private String bridgeRecordNum;

    @Excel( name = "隧道巡查记录(异常/总数)")
    private String tunnelRecordNum;

    @Excel( name = "病害数量")
    private String diseaseNum;

    private String ids;

}
