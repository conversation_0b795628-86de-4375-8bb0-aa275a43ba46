package com.ruoyi.patrol.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.BaseTunnelResponseCache;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * base_tunnel_response(BaseTunnelResponseCache)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-21 11:34:18
 */
public interface BaseTunnelResponseCacheMapper extends BaseMapper<BaseTunnelResponseCache> {


    @Delete("TRUNCATE TABLE base_tunnel_response_cache")
    void truncateTable();
    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BaseTunnelResponseCache> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BaseTunnelResponseCache> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BaseTunnelResponseCache> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BaseTunnelResponseCache> entities);

    /**
     * 获取request条件下的总数
     * @param request 请求参数
     * @return 总数
     */
    int countAssetBaseData(@Param("request") AssetBaseDataRequest request);

    /**
     * 获取request条件下的数据
     * @param request 请求参数
     * @return 数据
     */
    List<BaseTunnelResponseCache> selectAssetBaseData(@Param("request") AssetBaseDataRequest request, @Param("offset") Long offset, @Param("pageSize") Long pageSize);
}

