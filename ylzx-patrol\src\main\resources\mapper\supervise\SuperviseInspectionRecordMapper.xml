<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.supervise.mapper.SuperviseInspectionRecordMapper">

     <resultMap type="com.ruoyi.supervise.domain.SuperviseInspectionRecord" id="SuperviseInspectionRecordResult">
            <result property="inspectionUnitId" column="inspection_unit_id"/>
            <result property="inspectionUnitName" column="inspection_unit_name"/>
            <result property="inspectedUnitId" column="inspected_unit_id"/>
            <result property="inspectedUnitName" column="inspected_unit_name"/>
            <result property="inspectionTime" column="inspection_time"/>
            <result property="supervisorId" column="supervisor_id"/>
            <result property="supervisorName" column="supervisor_name"/>
            <result property="receiveTime" column="receive_time"/>
            <result property="contents" column="contents"/>
            <result property="suggestions" column="suggestions"/>
            <result property="status" column="status"/>
            <result property="rectifierId" column="rectifier_id"/>
            <result property="rectifierName" column="rectifier_name"/>
            <result property="remark" column="remark"/>
    </resultMap>

    <sql id="base_column">
 inspection_unit_id, inspection_unit_name, inspected_unit_id, inspected_unit_name, inspection_time, supervisor_id, supervisor_name, receive_time, contents, suggestions, status, rectifier_id, rectifier_name, remark    </sql>

    <sql id="where_column">
        <if test="inspectionUnitId != null and inspectionUnitId != ''">
            AND inspection_unit_id = #{inspectionUnitId}
        </if>
        <if test="inspectionUnitIdLike != null and inspectionUnitIdLike != ''">
            AND inspection_unit_id like CONCAT('%', #{inspectionUnitIdLike}, '%')
        </if>
        <if test="inspectionUnitName != null and inspectionUnitName != ''">
            AND inspection_unit_name = #{inspectionUnitName}
        </if>
        <if test="inspectedUnitId != null and inspectedUnitId != ''">
            AND inspected_unit_id = #{inspectedUnitId}
        </if>
        <if test="inspectedUnitName != null and inspectedUnitName != ''">
            AND inspected_unit_name = #{inspectedUnitName}
        </if>
        <if test="inspectionTime != null and inspectionTime != ''">
            AND inspection_time = #{inspectionTime}
        </if>
        <if test="inspectionTimes != null and inspectionTimes != ''">
            AND date_format(inspection_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(#{inspectionTimes}, '%Y-%m-%d')
        </if>
        <if test="inspectionTimee != null and inspectionTimee != ''">
            AND date_format(inspection_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(#{inspectionTimee}, '%Y-%m-%d')
        </if>
        <if test="supervisorId != null and supervisorId != ''">
            AND supervisor_id = #{supervisorId}
        </if>
        <if test="supervisorName != null and supervisorName != ''">
            AND supervisor_name = #{supervisorName}
        </if>
        <if test="receiveTime != null and receiveTime != ''">
            AND receive_time = #{receiveTime}
        </if>
        <if test="contents != null and contents != ''">
            AND contents = #{contents}
        </if>
        <if test="suggestions != null and suggestions != ''">
            AND suggestions = #{suggestions}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="rectifierId != null and rectifierId != ''">
            AND rectifier_id = #{rectifierId}
        </if>
        <if test="rectifierName != null and rectifierName != ''">
            AND rectifier_name = #{rectifierName}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}
        </if>
    </sql>

    <sql id="set_column">
            <if test="inspectionUnitId != null">
                inspection_unit_id = #{inspectionUnitId},
            </if>
            <if test="inspectionUnitName != null">
                inspection_unit_name = #{inspectionUnitName},
            </if>
            <if test="inspectedUnitId != null">
                inspected_unit_id = #{inspectedUnitId},
            </if>
            <if test="inspectedUnitName != null">
                inspected_unit_name = #{inspectedUnitName},
            </if>
            <if test="inspectionTime != null">
                inspection_time = #{inspectionTime},
            </if>
            <if test="supervisorId != null">
                supervisor_id = #{supervisorId},
            </if>
            <if test="supervisorName != null">
                supervisor_name = #{supervisorName},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime},
            </if>
            <if test="contents != null">
                contents = #{contents},
            </if>
            <if test="suggestions != null">
                suggestions = #{suggestions},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="rectifierId != null">
                rectifier_id = #{rectifierId},
            </if>
            <if test="rectifierName != null">
                rectifier_name = #{rectifierName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="SuperviseInspectionRecordResult">
        SELECT
        <include refid="base_column"/>
        FROM supervise_inspection_record
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="SuperviseInspectionRecordResult">
        SELECT
        <include refid="base_column"/>
        FROM supervise_inspection_record
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

</mapper>