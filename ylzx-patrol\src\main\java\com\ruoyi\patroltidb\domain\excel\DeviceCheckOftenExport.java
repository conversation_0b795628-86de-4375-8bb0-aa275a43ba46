package com.ruoyi.patroltidb.domain.excel;


import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 隧道机电经常巡查导出清单
 * 
 * <AUTHOR>
 * &#064;date  2025-04-15
 */
@ApiModel(value="隧道机电经常巡查导出清单")
@Data
public class DeviceCheckOftenExport implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    /** 递增序号 */
    @Excel(name = "序号", sort = 0)
    private Integer orderNumber;

    /** 路线编码 */
    @Excel(name = "路线编码", type = Excel.Type.EXPORT, sort = 1)
    private String routeCode;

    /** 路线名称 */
    @Excel(name = "路线名称", type = Excel.Type.EXPORT, sort = 2)
    private String routeName;

    /** 资产编码 */
    @Excel(name = "隧道编码", type = Excel.Type.EXPORT, sort = 3)
    private String assetCode;

    /** 资产名称 */
    @Excel(name = "隧道名称", type = Excel.Type.EXPORT, sort = 4)
    private String assetName;

    /** 管养单位名称 */
    @Excel(name = "养护机构", type = Excel.Type.EXPORT, sort = 5)
    private String deptName;

    /** 操作时间 */
    @Excel(name = "检查日期", dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 6)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /** 天气 */
    @Excel(name = "天气")
    private String weather;

    /** 检查人 */
    @Excel(name = "检查人")
    private String checker;

    /** 记录人 */
    @Excel(name = "记录人")
    @TableField(exist = false)
    private String recorder;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String devName;

    /** 位置 */
    @Excel(name = "检查位置")
    private String location;

    /** 内容 */
    @Excel(name = "检查内容")
    private String content;

    /** 结果（1正常，2异常，3异常且严重） */
    @Excel(name = "检查结果", readConverterExp = "1=正常,2=异常,3=异常且严重")
    private Long result;

    /** 描述 */
    @Excel(name = "异常描述")
    private String describe;

    /** 措施 */
    @Excel(name = "应急措施")
    private String measures;

}
