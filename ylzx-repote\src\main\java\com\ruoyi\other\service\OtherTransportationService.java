package com.ruoyi.other.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.other.domain.OtherTransportation;
import com.ruoyi.other.domain.dto.OtherTransportationDTO;


/**
 * 大件运输Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface OtherTransportationService extends IService<OtherTransportation> {

    /**
     * 根据条件查询大件运输列表
     * @param params
     */
    List<OtherTransportation> findListByParam(Map<String, Object> params);

    /**
     * 根据条件查询大件运输列表
     * @param params
     */
    List<OtherTransportationDTO> findAll(Map<String, Object> params);


//    /**
//     * 根据权限条件查询大件运输列表
//     * @param transportationIds
//     */
//    List<OtherTransportationDTO> findByTransportationIds(List<String> transportationIds);


    /**
     * 根据权限条件查询大件运输列表
     * @param params
     */
    List<OtherTransportationDTO> findByTransportationIds(Map<String, Object> params);
}
