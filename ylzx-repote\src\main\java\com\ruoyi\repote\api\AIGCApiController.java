package com.ruoyi.repote.api;


import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.CommonResult;
import com.ruoyi.repote.api.vo.QaVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 填报单位Controller
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Api(tags = "AI问答" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/aigc")
public class AIGCApiController extends BaseController {

    //获取会话ID
    private static final String getIdUrl = "https://ai.glyhgl.com/api/application/b1e8ef18-b390-11ef-8267-0242ac110002/chat/open";
    //问答
    private static final String qaUrl = "https://ai.glyhgl.com/api/application/chat_message";

    private static final String AUTHORIZATION = "application-8ea22e0773077ff2ff6ecd47287909c1";


    @ApiOperation("AI问答转发")
    @PostMapping("/chat")
    public CommonResult chat(@RequestBody QaVO qa) {
        try {
            //获取会话ID
            String authorization = HttpRequest.get(getIdUrl)
                    .header("Authorization", AUTHORIZATION)
                    .execute().body();
            ObjectMapper objectMapper = new ObjectMapper();
            CommonResult responseBean = objectMapper.readValue(authorization, CommonResult.class);
            // 问答
            String body = HttpRequest.post(qaUrl + "/" + responseBean.getData())
                    .header("Authorization", AUTHORIZATION)
                    .body(JSON.toJSONString(qa))
                    .timeout(300000)//超时 毫秒 5分钟
                    .execute().charset("UTF-8").body();
            CommonResult responseVO = objectMapper.readValue(body, CommonResult.class);
            return CommonResult.success(responseVO);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return CommonResult.fail(e.getMessage());
        }
    }

    public static void main(String[] args) throws JsonProcessingException {
        //获取会话ID
        String authorization = HttpRequest.get(getIdUrl)
                .header("Authorization", AUTHORIZATION)
                .execute().body();
        ObjectMapper objectMapper = new ObjectMapper();
        CommonResult responseBean = objectMapper.readValue(authorization, CommonResult.class);
        System.out.println(responseBean);
        System.out.println("url: " + qaUrl + "/" + responseBean.getData());
        // 问答
        QaVO qa = new QaVO();
        qa.setMessage("帮我看看养护联系人是谁");
        String body = HttpRequest.post(qaUrl + "/" + responseBean.getData())
                .header("Authorization", AUTHORIZATION)
                .body(JSON.toJSONString(qa))
//                .timeout(300000)//超时 毫秒 5分钟
                .execute().charset("UTF-8").body();
        CommonResult responseVO = objectMapper.readValue(body, CommonResult.class);
        System.out.println(responseVO);

    }







}
