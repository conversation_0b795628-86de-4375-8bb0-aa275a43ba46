package com.ruoyi.patroltidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheck;
import com.ruoyi.patroltidb.domain.PatrolTunnelCheckDetail;
import com.ruoyi.patroltidb.service.PatrolTunnelCheckDetailService;
import com.ruoyi.patroltidb.service.PatrolTunnelCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 隧道巡检查子表Controller
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Api(tags = "隧道巡检查子表" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/detail")
public class PatrolTunnelCheckDetailController extends BaseController {
    final private PatrolTunnelCheckDetailService patrolTunnelCheckDetailService;
    final private PatrolTunnelCheckService patrolTunnelCheckService;


    /**
     * 查询隧道巡检查子表列表(分页)
     */
    @ApiOperation("查询隧道巡检查子表列表")
    //@RequiresPermissions("patrol:detail:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<PatrolTunnelCheckDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<PatrolTunnelCheckDetail> list = patrolTunnelCheckDetailService.list(qw);
        return getDataTable(list);
    }

    /**
     * 查询隧道巡检查子表列表(不分页)
     */
    @ApiOperation("查询隧道巡检查子表列表(不分页)")
    //@RequiresPermissions("patrol:detail:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolTunnelCheckDetail> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolTunnelCheckDetail> list = patrolTunnelCheckDetailService.list(qw);
        return success(list);
    }

    /**
     * 根据id查询隧道巡检查子表数据
     */
    @ApiOperation("根据id查询隧道巡检查子表数据")
    //@RequiresPermissions("patrol:detail:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
            PatrolTunnelCheckDetail patrolTunnelCheckDetail = patrolTunnelCheckDetailService.getById(id);
        if (patrolTunnelCheckDetail == null) {
            return error("未查询到【隧道巡检查子表】记录");
        }
        return success(patrolTunnelCheckDetail);
    }

    /**
     * assertCheckId查询隧道巡检查子表数据
     */
    @ApiOperation("assertCheckId查询隧道巡检查子表数据")
    //@RequiresPermissions("patrol:bridgeCheckDetail:query")
    @GetMapping(value = "/getByCheckId")
    public AjaxResult getByCheckId(@RequestParam String assertCheckId) {
        QueryWrapper<PatrolTunnelCheckDetail> qw = new QueryWrapper<>();
        qw.eq("check_id", assertCheckId);
        List<PatrolTunnelCheckDetail> list = patrolTunnelCheckDetailService.list(qw);
        return success(list);
    }


    /**
     * assertCheckId查询桥梁巡检查子表数据
     */
    @ApiOperation("查询隧道巡检查子表数据")
    @ApiResponse(code = 200, message = "成功", response = PatrolTunnelCheckDetail.class)
    @GetMapping(value = "/getByCondition")
    public AjaxResult getByCondition(@ApiParam("隧道id") @RequestParam String tunnelId, @ApiParam("检查类型,'5': 隧道日常巡查，'6': 隧道经常检查") @RequestParam String type, @ApiParam("检查年份") @RequestParam() String year) {
        List<PatrolTunnelCheckDetail> list = new ArrayList<>();
        List<PatrolTunnelCheck> TunnelCheckList = patrolTunnelCheckService.list(new QueryWrapper<>() {{
            this.eq(tunnelId != null, "asset_id", tunnelId).
                    eq(type != null, "type", type).
                    le(year != null, "DATE_FORMAT(check_time,'%Y')", year).orderByDesc("check_time");
        }});

        if (TunnelCheckList.size() >= 1) {
            for (PatrolTunnelCheck patrolTunnelCheck : TunnelCheckList) {
                QueryWrapper<PatrolTunnelCheckDetail> qw = new QueryWrapper<>();
                qw.eq("check_id", patrolTunnelCheck.getId());
                list = patrolTunnelCheckDetailService.list(qw);
                if (list != null && !list.isEmpty()) {
                    break;  // 找到数据就退出循环
                }
            }
        }

        return success(list);
    }

    /**
     * 新增隧道巡检查子表
     */
    @ApiOperation("新增隧道巡检查子表")
    //@RequiresPermissions("patrol:detail:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolTunnelCheckDetail patrolTunnelCheckDetail) {
        return toAjax(patrolTunnelCheckDetailService.save(patrolTunnelCheckDetail));
    }

    /**
     * 修改隧道巡检查子表
     */
    @ApiOperation("修改隧道巡检查子表")
    //@RequiresPermissions("patrol:detail:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolTunnelCheckDetail patrolTunnelCheckDetail) {
        return toAjax(patrolTunnelCheckDetailService.updateById(patrolTunnelCheckDetail));
    }

    /**
     * 删除隧道巡检查子表
     */
    @ApiOperation("删除隧道巡检查子表")
    //@RequiresPermissions("patrol:detail:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolTunnelCheckDetailService.removeById(id));
    }

    /**
     * 导出隧道巡检查子表列表
     */
    @ApiOperation("导出隧道巡检查子表列表")
    //@RequiresPermissions("patrol:detail:export")
    @Log(title = "隧道巡检查子表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolTunnelCheckDetail> list = patrolTunnelCheckDetailService.list();
        ExcelUtil<PatrolTunnelCheckDetail> util = new ExcelUtil<PatrolTunnelCheckDetail>(PatrolTunnelCheckDetail.class);
        util.exportExcel(response, list, "隧道巡检查子表数据");
    }


}
