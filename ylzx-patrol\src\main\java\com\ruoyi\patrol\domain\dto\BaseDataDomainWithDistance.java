package com.ruoyi.patrol.domain.dto;

import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.enums.InspectionType;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Description:
 * @author: QD
 * @date: 2024年08月15日 17:39
 */
@Data
public class BaseDataDomainWithDistance <T extends BaseDataCache>{
    private String inspectId;
    private Boolean isCheck;
    private Double distance;
    private T baseData;

    public BaseDataDomainWithDistance(T baseData, Double distance, Boolean isCheck, String inspectId, InspectionType type, LocalDateTime checkTime) {
        this.inspectId = inspectId;
        this.distance = distance;
        this.isCheck = isCheck;
        this.baseData = baseData;
        this.baseData.setIsCheck(isCheck);
        this.baseData.setIsCheckStr(isCheck == null ? null : (isCheck ? "已检查" : "未检查"));
        if (type != null) {
            this.baseData.setType(type.getCode());
            if (type.getFlag()) {
                // 日常巡查格式化为yyyy-MM-DD
                this.baseData.setDateStr(checkTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            } else {
                // 月度检查格式化为yyyy-MM
                this.baseData.setDateStr(checkTime.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            }
        }
    }
}
