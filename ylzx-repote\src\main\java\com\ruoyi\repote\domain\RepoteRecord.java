package com.ruoyi.repote.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 填报记录对象 repote_record
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="填报记录")
@TableName("repote_record")
@Data
public class RepoteRecord extends BaseTableEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 填报表格ID */
    @Excel(name = "填报表格ID")
    @ApiModelProperty(value = "填报表格ID")
    private String formId;

    /** 填报人ID */
    @Excel(name = "填报人ID")
    @ApiModelProperty(value = "填报人ID")
    private Long userId;

    /** 填报人名字 */
    @Excel(name = "填报人名字")
    @ApiModelProperty(value = "填报人名字")
    private String userName;

    /** 填报后表格地址 */
    @Excel(name = "填报后表格地址")
    @ApiModelProperty(value = "填报后表格地址")
    private String url;

    @Excel(name = "附件地址ids")
    @ApiModelProperty(value = "附件地址ids")
    private String annexUrls;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remake;

    /** 填报状态（1-待填写，2-待提交，3-已提交） */
    @Excel(name = "填报状态", readConverterExp = "1=-待填写，2-待提交，3-已提交")
    @ApiModelProperty(value = "填报状态（1-待填写，2-待提交，3-已提交）")
    private Integer status;

    @Excel(name = "填报表格名称")
    @ApiModelProperty(value = "填报表格名称")
    private String formName;

    @Excel(name = "填报表格模板地址")
    @ApiModelProperty(value = "填报表格模板地址")
    @TableField(exist = false)
    private String formUrl;

    @Excel(name = "填报表格要求")
    @ApiModelProperty(value = "填报表格要求")
    @TableField(exist = false)
    private String request;

    @Excel(name = "任务ID")
    @ApiModelProperty(value = "任务ID")
    private String missionId;

    @Excel(name = "任务名称")
    @ApiModelProperty(value = "任务名称")
    private String missionName;

    @Excel(name = "到期时间")
    @ApiModelProperty(value = "到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private LocalDateTime expiry;

    @Excel(name = "任务备注")
    @ApiModelProperty(value = "任务备注")
    @TableField(exist = false)
    private String missionRemark;

    @Excel(name = "任务要求文件")
    @ApiModelProperty(value = "任务要求文件")
    @TableField(exist = false)
    private String missionUrl;
}
