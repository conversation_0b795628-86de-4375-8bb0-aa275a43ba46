package com.ruoyi.patrol.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description:
 * @author: QD
 * @date: 2024年11月21日 17:11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TunnelDetailsVO {
    private String id;
    @Excel(name = "管理处",sort = 1)
    private String propertyUnitName;
    @Excel(name = "路段",sort = 2)
    private String maintenanceSectionName;
    @Excel(name = "月份",sort = 3,dateFormat = "yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private LocalDateTime expiry;
    @Excel(name = "隧道名称",sort = 4)
    private String assetName;
    @Excel(name = "隧道编码",sort = 5)
    private String assetCode;
    @Excel(name = "巡查机构",sort = 6)
    private String maintainUnitName;
}
