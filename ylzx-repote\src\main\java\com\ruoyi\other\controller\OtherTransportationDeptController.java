package com.ruoyi.other.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.other.domain.OtherTransportation;
import com.ruoyi.other.domain.OtherTransportationDept;
import com.ruoyi.other.domain.dto.OtherTransportationDTO;
import com.ruoyi.other.domain.dto.OtherTransportationDeptDTO;
import com.ruoyi.other.service.OtherTransportationDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "大件运输部门" )
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/transportationDept")
public class OtherTransportationDeptController extends BaseController {
    final private OtherTransportationDeptService otherTransportationDeptService;

    /**
     * 查询大件运输列表(分页)
     */
    @ApiOperation("查询大件运输管理处")
//    //@RequiresPermissions("other:transportationDept:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        QueryWrapper<OtherTransportationDept> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        startPage();
        List<OtherTransportationDept> list = otherTransportationDeptService.list(qw);
        return getDataTable(list);
    }

    /**
     * 新增大件运输部门
     */
    @ApiOperation("新增大件运输部门")
//    //@RequiresPermissions("other:transportation:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody OtherTransportationDept otherTransportationDept) {
        return toAjax(otherTransportationDeptService.save(otherTransportationDept));
    }

    /**
     * 修改大件运输部门
     */
    @ApiOperation("修改大件运输部门")
    //@RequiresPermissions("other:transportation:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody OtherTransportationDept otherTransportationDept) {
        boolean success = otherTransportationDeptService.updateDepartmentByTransportationId(
                otherTransportationDept.getTransportationId(),
                otherTransportationDept.getDeptId(),
                otherTransportationDept.getDeptName()
        );
        return success ? AjaxResult.success("修改成功") : AjaxResult.error("修改失败");
    }

    /**
     * 根据id查询管理处ID列表
     */
    @ApiOperation("根据id查询管理处ID列表")
    //@RequiresPermissions("other:transportation:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {

        // 查询管理处ID列表
        List<String> managementDepartmentIds = otherTransportationDeptService.getManagementDepartmentIdsByTransportationId(id);

        if (managementDepartmentIds == null) {
            return error("未查询到【大件运输部门】");
        }

        return success(managementDepartmentIds);
    }

    /**
     * 根据id查询管理处ID列表
     */
    @ApiOperation("根据用户管理处ID查询是否在管理处ID列表")
    @PostMapping(value = "/getTransportationDeptByList")
    public AjaxResult getTransportationDeptByList(@RequestBody List<String> userDeptIdList) {

        if (userDeptIdList == null || userDeptIdList.isEmpty()) {
            return error("未提供有效的【大件运输部门】ID列表");
        }

        // 根据传入的ID列表查询对应的管理处ID列表
        List<String> transportationList = otherTransportationDeptService.getTransportationList(userDeptIdList);  // 假设服务层有对应处理多个ID的方法

        if (transportationList == null || transportationList.isEmpty()) {
            return error("未查询到任何与提供的ID匹配的【大件运输部门】");
        }

        return success(transportationList);
    }


//    @ApiOperation("删除大件运输路段")
//    @DeleteMapping("/delete/{id}")
//    public AjaxResult remove(@PathVariable String id) {
//        return toAjax(otherTransportationDeptService.removeById(id));
//    }

    @ApiOperation("删除大件运输部门")
    @PostMapping("/delete")
    public AjaxResult removeAll(@RequestBody String transportationId) {
        // 使用 transportationId 和 roadId 执行删除操作
        boolean result = otherTransportationDeptService.removeByTransportationId(transportationId);
        return toAjax(result);
    }




//    /**
//     * 修改大件运输多个部门
//     */
//    @ApiOperation("修改大件运输多个部门")
//    //@RequiresPermissions("other:transportation:edit")
//    @PutMapping("/edit")
//    public AjaxResult editList(@Validated @RequestBody OtherTransportationDept otherTransportationDept) {
//        return toAjax(otherTransportationDeptService.updateByIds(otherTransportationDept));
//    }


//    /**
//     * 修改大件运输部门
//     */
//    @ApiOperation("修改大件运输部门")
//    //@RequiresPermissions("other:transportation:edit")
//    @PutMapping("/edit")
//    public AjaxResult edit(@Validated @RequestBody OtherTransportationDept otherTransportationDept) {
//        return toAjax(otherTransportationDeptService.updateById(otherTransportationDept));
//    }


}
