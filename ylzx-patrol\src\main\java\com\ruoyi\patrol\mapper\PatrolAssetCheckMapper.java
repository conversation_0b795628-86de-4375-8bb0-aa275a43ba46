package com.ruoyi.patrol.mapper;

import java.time.LocalDateTime;
import java.time.Month;
import java.time.YearMonth;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patrol.domain.BaseBridgeResponseCache;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolCheckBase;
import com.ruoyi.patrol.domain.dto.PatrolDiseaseDTO;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.request.PatrolAssetCheckRequest;
import com.ruoyi.patrol.domain.vo.AssetCheckStatisticsVO;
import com.ruoyi.patrol.enums.InspectionType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 资产巡检查主表Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Mapper
public interface PatrolAssetCheckMapper extends BaseMapper<PatrolAssetCheck> {

    /**
     * 批量更新病害数
     * @param
     * @return
     */
    void updateDiseaseNumbers(@Param("list") List<PatrolDiseaseDTO> list,@Param("tableName") String tableName);

    /**
     * 批量更新巡检记录到期时间
     *
     * @param list 需要更新的巡检记录列表
     * @param tableName 表名
     * @return 更新的记录数
     */
    int updateExpiryBatch(@Param("list") List<PatrolDiseaseDTO> list, @Param("tableName") String tableName);

    /**
     * 批量更新管养分处ID和名称
     *
     * @param list 需要更新的巡检记录列表
     * @param tableName 表名
     * @return 更新的记录数
     */
    int updatePropertyUnitBatch(@Param("list") List<PatrolAssetCheck> list, @Param("tableName") String tableName);
    
    /**
     * 根据ID列表批量更新状态
     *
     * @param checkIds 需要更新的ID列表
     * @param entity 包含要更新字段的实体
     * @param tableName 表名
     * @return 更新的记录数
     */
    int updateStatusByIds(@Param("checkIds") Set<String> checkIds, 
                          @Param("entity") PatrolAssetCheck entity, 
                          @Param("tableName") String tableName);

    List<PatrolAssetCheck> findListByParam(Map<String, Object> params);

    List<PatrolAssetCheck> selectPatrolAssetChecksByAssetIds(PatrolAssetCheckRequest patrolAssetCheckRequest);


    List<PatrolAssetCheck> selectPatrolAssetChecksByAssetIdsBatch(
            @Param("list") List<PatrolAssetCheckRequest> patrolAssetCheckRequest,
            @Param("nowCheckTime") LocalDate nowCheckTime,
            @Param("type") String type,
            @Param("tableName") String tableName);


    // 根据list查询最近一次巡查记录
    Map<String, LocalDateTime> selectLatestExpiry(@Param("list") List<String> assetIds,
                                                  @Param("tableName") String tableName,
                                                  @Param("type") InspectionType type);


    // 最近一次巡查记录
    LocalDateTime findLatestExpiry(@Param("assetId") String assetId,
                                   @Param("tableName") String tableName,
                                   @Param("type") InspectionType type);

    /**
     * 查询指定资产在指定日期前后的巡检记录
     *
     * @param assetId   资产ID
     * @param tableName 巡检记录表名
     * @param type      巡检类型(如日常巡检、经常巡检等)
     * @param date      指定日期
     * @return 返回长度为2的数组，包含:
     * - [0]: 指定日期之前最近的一次巡检记录
     * - [1]: 指定日期之后最近的一次巡检记录
     * <p>
     * 说明:
     * 1. 用于获取资产在特定时间点前后的巡检状态
     * 2. 可用于判断资产是否按计划完成巡检
     * 3. 如果某个方向没有记录，对应位置返回null
     * <p>
     * 示例:
     * date = 2024-03-15
     * 返回: [2024-03-10的记录, 2024-03-20的记录]
     */
    PatrolCheckBase[] findExpiryResults(@Param("assetId") String assetId,
                                        @Param("tableName") String tableName,
                                        @Param("type") InspectionType type,
                                        @Param("date") LocalDate date);


    /**
     * 批量查询多个资产在指定日期前后的巡检记录
     *
     * @param assetIds  资产ID列表
     * @param tableName 巡检记录表名
     * @param type      巡检类型(如日常巡检、经常巡检等)
     * @param date      指定日期
     * @return 巡检记录列表，每个资产包含:
     * - 指定日期之前最近的一次巡检记录
     * - 指定日期之后最近的一次巡检记录
     * <p>
     * 说明:
     * 1. 批量版本的findExpiryResults，用于提高查询效率
     * 2. 适用于需要同时查询多个资产巡检状态的场景
     * 3. 返回结果可能少于输入的assetIds数量(某些资产可能没有巡检记录)
     * <p>
     * 示例:
     * assetIds = ["asset1", "asset2"]
     * date = 2024-03-15
     * 返回: [
     * {assetId: "asset1", beforeRecord: 2024-03-10记录, afterRecord: 2024-03-20记录},
     * {assetId: "asset2", beforeRecord: 2024-03-12记录, afterRecord: 2024-03-18记录}
     * ]
     */
    List<PatrolCheckBase> findExpiryResultsBatch(
            @Param("assetIds") List<String> assetIds,
            @Param("tableName") String tableName,
            @Param("type") InspectionType type,
            @Param("date") LocalDate date
    );


    // 批量插入
    Boolean insertBatch(@Param("list") List<PatrolAssetCheck> patrolAssetCheckList,
                        @Param("tableName") String tableName);


    /**
     * 查询指定日期已完成巡查的资产ID列表
     *
     * @param assetIds  待查询的资产ID列表
     * @param now       查询日期
     * @param type      巡查类型(如日常巡查、经常巡查等)
     * @param tableName 巡查记录表名
     * @return 已完成巡查的资产ID列表
     * <p>
     * 说明: 用于日常巡查场景，查询指定日期当天是否有巡查记录
     */
    List<String> selectCheckedDaysAssetIds(@Param("list") List<String> assetIds,
                                           @Param("now") LocalDate now,
                                           @Param("status") Integer status,
                                           @Param("type") InspectionType type,
                                           @Param("tableName") String tableName);


    /**
     * 查询指定月份已完成巡查的资产ID列表
     *
     * @param assetIds  待查询的资产ID列表
     * @param yearMonth 查询年月(如: 2024-03)
     * @param type      巡查类型(如日常巡查、经常巡查等)
     * @param tableName 巡查记录表名
     * @return 已完成巡查的资产ID列表
     * <p>
     * 说明: 用于经常巡查场景，查询指定月份内是否有巡查记录
     */
    List<PatrolAssetCheck> selectCheckedMonthAssetIds(@Param("list") List<String> assetIds,
                                                      @Param("yearMonth") LocalDate yearMonth,
                                                      @Param("status") Integer status,
                                                      @Param("type") InspectionType type,
                                                      @Param("tableName") String tableName);

    /**
     * 查询指定月份已完成巡查的资产ID列表
     *
     * @param assetIds  待查询的资产ID列表
     * @param yearMonth 查询年月(如: 2024-03)
     * @param type      巡查类型(如日常巡查、经常巡查等)
     * @param tableName 巡查记录表名
     * @return 已完成巡查的资产ID列表
     * <p>
     * 说明: 用于经常巡查场景，查询指定月份内是否有巡查记录
     */
    List<String> selectCheckedMonthAssetIdsByJD(@Param("list") List<String> assetIds,
                                                @Param("yearMonth") LocalDate yearMonth,
                                                @Param("status") Integer status,
                                                @Param("type") InspectionType type,
                                                @Param("tableName") String tableName);

    List<PatrolAssetCheck> selectCheckedYear(@Param("list") List<String> assetIds,
                                             @Param("year") String year,
                                             @Param("type") InspectionType type,
                                             @Param("tableName") String tableName);

    // 日常巡查对应的月有记录的日期
    List<PatrolAssetCheck> findByAssetIdAndYearMonth(@Param("assetId") String assetId,
                                                     @Param("yearMonth") String yearMonth,
                                                     @Param("type") InspectionType type,
                                                     @Param("tableName") String tableName);

    // 经常巡查对应的月有记录的日期
    List<PatrolAssetCheck> findByAssetIdAndYear(@Param("assetId") String assetId,
                                                @Param("year") String year,
                                                @Param("type") InspectionType type,
                                                @Param("tableName") String tableName);

    /**
     * 获取request条件下的总数
     *
     * @param request 请求参数
     * @return 总数
     */
    int countAssetCheckData(@Param("request") AssetBaseDataRequest request,
                            @Param("tableName") String tableName);

    /**
     * 获取request条件下的数据
     *
     * @param request 请求参数
     * @return 数据
     */
    List<PatrolAssetCheck> selectAssetCheckData(@Param("request") AssetBaseDataRequest request,
                                                @Param("offset") Long offset,
                                                @Param("pageSize") Long pageSize,
                                                @Param("tableName") String tableName);

    List<PatrolAssetCheck> selectAssetCheckDataWithDetail(@Param("request") AssetBaseDataRequest request,
                                                          @Param("offset") Long offset,
                                                          @Param("pageSize") Long pageSize,
                                                          @Param("tableName") String tableName,
                                                          @Param("detailTableName") String detailTableName);

    List<PatrolCheckBase> selectMaxExpiryByCheckTimeGreaterThanExpiry(@Param("request") AssetBaseDataRequest request,
                                                                      @Param("tableName") String tableName);


    /**
     * 统计月度数据
     *
     * @param request    请求参数
     * @param formatTime 时间格式
     * @param month      月份
     * @param tableName  表名
     * @return 统计数据
     */
    List<AssetCheckStatisticsVO> selectMonthlyStatistics(@Param("request") AssetBaseDataRequest request,
                                                         @Param("formatTime") String formatTime,
                                                         @Param("month") Integer month,
                                                         @Param("tableName") String tableName);

    /**
     * 根据创建时间和表名查询病害数大于0的数据
     *
     * @param createTime 创建时间
     * @param tableName 表名
     * @return 巡检记录列表
     */
    List<PatrolAssetCheck> selectByCreateTimeAndDiseaseCount(@Param("createTime") LocalDateTime createTime,
            @Param("tableName") String tableName);

    /**
 * 批量更新审核信息
 * 
 * @param list 检查记录列表
 * @param tableName 表名
 * @return 更新的记录数
 */
int updateAuditInfoBatch(@Param("list") List<PatrolAssetCheck> list, @Param("tableName") String tableName);

}
