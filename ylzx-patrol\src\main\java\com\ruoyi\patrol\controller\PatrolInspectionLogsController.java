package com.ruoyi.patrol.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlRunner;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.context.SecurityContextHolder;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.map.Paramap;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.domain.CommonResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.manage.api.domain.RemoteRoadDiseaseResponse;
import com.ruoyi.manage.api.service.RemoteRoadDiseaseService;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolInspectionLogs;
import com.ruoyi.patrol.domain.PatrolInspectionUser;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.vo.*;
import com.ruoyi.patrol.enums.AssetType;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.service.BaseCacheService;
import com.ruoyi.patrol.service.PatrolInspectionLogsService;
import com.ruoyi.patrol.service.PatrolInspectionUserService;
import com.ruoyi.patrol.service.handler.impl.*;
import com.ruoyi.patrol.utils.AssetConvertUtils;
import com.ruoyi.patrol.utils.StackUtils;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.system.api.*;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.dto.BaseRouteDTO;
import com.ruoyi.system.api.model.LoginUser;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;


import org.json.simple.JSONObject;
import cn.idev.excel.EasyExcel;

import javax.servlet.ServletOutputStream;

/**
 * 巡查日志Controller
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Api(tags = "巡查日志")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/inspectionLogs")
public class PatrolInspectionLogsController extends BaseController {

    @Resource
    private PatrolInspectionLogsService patrolInspectionLogsService;

    @Resource
    private PatrolInspectionUserService patrolInspectionUserService;

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private RemoteRoadDiseaseService remoteRoadDiseaseService;

    @Resource
    private PatrolAssetCheckService patrolAssetCheckService;

    @Resource
    private BaseCacheService baseCacheService;

    @Resource
    private RemoteMaintenanceSectionService remoteMaintenanceSectionService;

    @Resource
    private RemoteDeptAuthService remoteDeptAuthService;

    @Resource
    private RemoteRouteService remoteRouteService;

    @Resource
    private RemoteFileService remoteFileService;

    @Resource
    private ThreadPoolTaskExecutor executor;

    /**
     * 查询巡查日志列表(分页)
     */
    @ApiOperation("查询巡查日志列表")
    //@RequiresPermissions("patrol:inspectionLogs:list")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam Map<String, Object> params,
                              @RequestParam(name = "ids", required = false) List<String> logsIds) throws ExecutionException, InterruptedException {
        params.put("ids", logsIds);
        long startTime = System.currentTimeMillis();

        Integer isEvent = MapUtil.getInt(params, "isEvent");
        if (null != isEvent) {
            List<String> ids = remoteRoadDiseaseService.getRoadDiseaseIdList().getData();
            if (isEvent == 1) params.put("idList", ids);
            else params.put("idListNot", ids);
        }
        R<List<String>> r = remoteMaintenanceSectionService.findUserMaintenanceIds(null);
        if (r.getCode() != 200) {
            return getDataTable(new ArrayList<>());
        }
        params.put("sectionIdList", r.getData());
        params.put("order", "l.collect_time desc");
        startPage();
        List<PatrolInspectionLogs> list = patrolInspectionLogsService.findListByParam(params);

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (PatrolInspectionLogs tempPatrolInspectionLog : list) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                List<RemoteRoadDiseaseResponse> data = remoteRoadDiseaseService.getRoadDiseaseList(tempPatrolInspectionLog.getId()).getData();
                tempPatrolInspectionLog.setDiseaseNum(0);
                if (CollectionUtil.isNotEmpty(data))
                    tempPatrolInspectionLog.setDiseaseNum(data.size());

            }, executor);
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();

        long endTime = System.currentTimeMillis();
        log.info("接口耗时: {}ms", (endTime - startTime));
        return getDataTable(list);
    }

    @ApiOperation("积木报表条件查询列表")
    @GetMapping("/listByJm")
    public JSONObject listByJm(
            @RequestParam(name = "pageNo", defaultValue = "1") Long pageNo,
            @RequestParam(name = "pageSize", defaultValue = "100000") Long pageSize,
            @RequestParam(name = "ids", required = false) String ids,
            @RequestParam(name = "type", required = false) String type,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "isEvent", required = false) Integer isEvent,
            @RequestParam(name = "sectionId", required = false) String sectionId,
            @RequestParam(name = "mUnitId", required = false) String mUnitId,
            @RequestParam(name = "pUnitId", required = false) String pUnitId,
            @RequestParam(name = "times", required = false) String times,
            @RequestParam(name = "timee", required = false) String timee,
            @RequestParam(name = "sign", defaultValue = "1") String sign,
            HttpServletRequest request) {
        LoginUser loginUser = (LoginUser) SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginUser.class);
        Long userId = null;
        if (loginUser == null) {
            String userIdHeader = request.getHeader("userid");
            if (StringUtils.isNotBlank(userIdHeader)) {
                try {
                    userId = Long.valueOf(userIdHeader);
                    SysUser sysUser = remoteUserService.findByUserId(userId).getData();
                    if (sysUser != null) {
                        loginUser = remoteUserService.getUserInfo(sysUser.getUserName(), SecurityConstants.INNER).getData();
                        SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的用户ID头部值: {}", userIdHeader);
                }
            }
        } else {
            userId = loginUser.getUserid();
        }

        // 如果还是没有获取到用户ID，使用默认值
        if (userId == null) {
            userId = 1L;
        }

        // 创建参数Map
        Map<String, Object> params = new HashMap<>();

        // 处理ID列表参数
        Set<String> idSet = new HashSet<>();
        if (StringUtils.isNotBlank(ids)) {
            idSet.addAll(Arrays.asList(ids.split(",")));
        }

        if (isEvent != null) {
            List<String> diseaseIds = remoteRoadDiseaseService.getRoadDiseaseIdList().getData();
            if (diseaseIds != null) {
                if (isEvent == 1) {
                    // 添加事件ID到idList (合并并去重)
                    idSet.addAll(diseaseIds);
                    params.put("idList", new ArrayList<>(idSet));
                } else {
                    // 从已有idList中移除这些ID，然后添加到idListNot
                    if (!idSet.isEmpty()) {
                        // 如果已经有ID列表，则从中移除事件ID
                        idSet.removeAll(diseaseIds);
                        params.put("idList", new ArrayList<>(idSet));
                    }
                    // 添加到idListNot
                    params.put("idListNot", diseaseIds);
                }
            }
            params.put("isEvent", isEvent);
        } else if (!idSet.isEmpty()) {
            // 如果没有isEvent参数但有ids参数
            params.put("idList", new ArrayList<>(idSet));
        }

        // 添加参数到map中
        if (StringUtils.isNotBlank(type)) {
            params.put("patrolType", type);
        }

        if (StringUtils.isNotBlank(name)) {
            params.put("userNameLike", name);
        }


        if (StringUtils.isNotBlank(sectionId)) {
            sectionId = sectionId.replaceAll("_left_", "[");
            sectionId = sectionId.replaceAll("_right_", "]");
            params.put("maintenanceSectionId", sectionId);
        }

        if (StringUtils.isNotBlank(mUnitId)) {
            params.put("maintenanceUnitId", mUnitId);
        }

        if (StringUtils.isNotBlank(pUnitId)) {
            params.put("patrolUnitId", pUnitId);
        }

        if (StringUtils.isNotBlank(times)) {
            params.put("collectTimes", times);
        }

        if (StringUtils.isNotBlank(timee)) {
            params.put("collectTimee", timee);
        }

        R<List<String>> r = remoteMaintenanceSectionService.findUserMaintenanceIds(userId);
        if (r.getCode() != 200) {
            return new JSONObject();
        }
        params.put("sectionIdList", r.getData());
        params.put("order", "l.collect_time desc");

        // 使用PageHelper进行分页，直接设置pageNo和pageSize
        PageHelper.startPage(pageNo.intValue(), pageSize.intValue(), "l.collect_time desc");

        // 查询数据
        List<PatrolInspectionLogs> list = patrolInspectionLogsService.findListByParam(params);
        if (list == null || list.isEmpty()) {
            return new JSONObject();
        }

        // 调用service层方法设置用户信息和签名URL
        if ("1".equals(sign)) {
            patrolInspectionLogsService.setUserInfoAndSignUrl(list);
        }

        // 获取分页信息
        PageInfo<PatrolInspectionLogs> pageInfo = new PageInfo<>(list);

        // 构建返回结果
        JSONObject object = new JSONObject();
        object.put("data", list);
        object.put("total", pageInfo.getPages());  // 总页数
        object.put("count", list.size());          // 当前页记录数
        object.put("totalCount", pageInfo.getTotal()); // 总记录数
        return object;
    }

    /**
     * 迁移巡查人员关联表
     */
    @PostMapping("/testSaveUser")
    public int testSaveUser() {
        List<PatrolInspectionLogs> logsList = patrolInspectionLogsService.list();
        Set<String> deptIdset = logsList.stream().map(PatrolInspectionLogs::getMaintenanceUnitId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<Long> deptIds = deptIdset.stream().map(Long::parseLong).collect(Collectors.toList());
        List<SysUser> userList = remoteUserService.findListByDeptId(deptIds).getData();
        List<PatrolInspectionUser> patrolInspectionUserList = new ArrayList<>();
        for (PatrolInspectionLogs logs : logsList) {
            String personStr = logs.getPersonStr();
            if (personStr != null && !personStr.isEmpty()) {
                personStr = personStr.replaceAll("[\\pP\\p{S}\\s&&[^-]]+", "");
                for (int i = 2, j = 0; i < personStr.length(); i++) {
                    String substring = personStr.substring(j, i);
                    for (SysUser sysUser : userList) {
                        if (substring.equals(sysUser.getNickName()) && (logs.getMaintenanceUnitId() == null || sysUser.getDeptId() == null || logs.getMaintenanceUnitId().equals(sysUser.getDeptId().toString()))) {
                            PatrolInspectionUser user = new PatrolInspectionUser();
                            user.setPatrolId(logs.getId());
                            user.setUserId(sysUser.getUserId());
                            user.setNickName(sysUser.getNickName());
                            user.setSignId(sysUser.getSignId());
                            patrolInspectionUserList.add(user);
                            j = i;
                            break;
                        }
                    }

                }
            }
        }
        if (!patrolInspectionUserList.isEmpty())
            return patrolInspectionUserService.batchInsert(patrolInspectionUserList);

        return 0;
    }

    /**
     * 查询巡查日志列表(不分页)
     */
    @ApiOperation("查询巡查日志列表(不分页)")
    //@RequiresPermissions("patrol:inspectionLogs:listAll")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        QueryWrapper<PatrolInspectionLogs> qw = new QueryWrapper<>();
        qw.orderByDesc("create_time");
        List<PatrolInspectionLogs> list = patrolInspectionLogsService.list(qw);
        QueryWrapper<PatrolInspectionUser> qw2 = new QueryWrapper<>();
        qw2.in("patrol_id", list.stream().map(PatrolInspectionLogs::getId).collect(Collectors.toList()));
        List<PatrolInspectionUser> userList = patrolInspectionUserService.list(qw2);
        Map<String, List<PatrolInspectionUser>> userMap = userList.stream().collect(Collectors.groupingBy(PatrolInspectionUser::getPatrolId));
        for (PatrolInspectionLogs logs : list) {
            List<PatrolInspectionUser> patrolInspectionUsers = userMap.get(logs.getId());
            logs.setUserIds(patrolInspectionUsers.stream().map(PatrolInspectionUser::getUserId).collect(Collectors.toList()));
            logs.setUserNameList(patrolInspectionUsers.stream().map(PatrolInspectionUser::getNickName).collect(Collectors.toList()));
            logs.setSignIdList(patrolInspectionUsers.stream().map(PatrolInspectionUser::getSignId).collect(Collectors.toList()));
        }
        return success(list);
    }

    /**
     * 根据id查询巡查日志数据
     */
    @ApiOperation("根据id查询巡查日志数据")
    //@RequiresPermissions("patrol:inspectionLogs:query")
    @GetMapping(value = "/get/{id}")
    public AjaxResult get(@PathVariable String id) {
        PatrolInspectionLogs patrolInspectionLogs = patrolInspectionLogsService.getById(id);
        if (patrolInspectionLogs == null) {
            return error("未查询到【巡查日志】记录");
        }
        if (patrolInspectionLogs.getRouteCode() == null || patrolInspectionLogs.getRouteCode().isEmpty()) {
            R<List<BaseRouteDTO>> info =
                    remoteRouteService.listByMaintenanceSectionId(patrolInspectionLogs.getMaintenanceSectionId());
            if (info.getData() != null && !info.getData().isEmpty()) {
                patrolInspectionLogs.setRouteCode(info.getData().get(0).getRouteCode());
            }
        }
        List<PatrolInspectionUser> list = patrolInspectionUserService.listByMap(Paramap.create().put("patrol_id", id));
        if (!list.isEmpty()) {
            patrolInspectionLogs.setUserIds(list.stream().map(PatrolInspectionUser::getUserId).toList());
            patrolInspectionLogs.setUserNameList(list.stream().map(PatrolInspectionUser::getNickName).toList());
            patrolInspectionLogs.setNickNameList(list.stream().map(PatrolInspectionUser::getNickName).toList());
            patrolInspectionLogs.setSignIdList(list.stream().map(PatrolInspectionUser::getSignId).toList());
            String ownerId = list.get(0).getSignId();
            R<SysFile> file = remoteFileService.getFile(ownerId);
            if (file.getData() != null) {
                patrolInspectionLogs.setOwnerAvatar(file.getData().getUrl());
            }
        }
        return success(patrolInspectionLogs);
    }

    @ApiOperation("根据id查询巡查事件")
    //@RequiresPermissions("patrol:inspectionLogs:query")
    @GetMapping(value = "/getRoadDiseaseList/{id}")
    public AjaxResult getRoadDiseaseList(@PathVariable String id) {
        R<List<RemoteRoadDiseaseResponse>> remoteRoadDiseaseResponseR = remoteRoadDiseaseService.getRoadDiseaseList(id);
        if (remoteRoadDiseaseResponseR.getCode() != 200) {
            return error("未查询到【巡查事件】记录" + remoteRoadDiseaseResponseR.getMsg());
        } else {
            List<RemoteRoadDiseaseResponse> list = remoteRoadDiseaseResponseR.getData();
            List<DiseaseDetailsVO> diseaseDetailsVOList = new ArrayList<>();
            for (RemoteRoadDiseaseResponse remoteRoadDiseaseResponse : list) {
                DiseaseDetailsVO.DiseaseDetailsVOBuilder builder = DiseaseDetailsVO.builder();
                String direction = remoteRoadDiseaseResponse.getDirection();
                direction = direction == "0" ? "上行" : "下行";
                String stackNo = String.format("%s至%s",
                        StackUtils.formatStack(BigDecimal.valueOf(remoteRoadDiseaseResponse.getBeginMile())),
                        StackUtils.formatStack(BigDecimal.valueOf(remoteRoadDiseaseResponse.getEndMile())));
                String defectItem = remoteRoadDiseaseResponse.getAssetMainTypeName();
                String eventName = remoteRoadDiseaseResponse.getDiseaseName();
                String unit = null;
                String quantity = null;
                String description = remoteRoadDiseaseResponse.getDiseaseDesc();
                DiseaseDetailsVO diseaseDetailsVO = builder.direction(direction)
                        .stakeNo(stackNo)
                        .defectItem(defectItem)
                        .eventName(eventName)
                        .unit(unit)
                        .quantity(quantity)
                        .description(description)
                        .build();
                diseaseDetailsVOList.add(diseaseDetailsVO);
            }
            // 如果记录少于20条,用空记录补充到20条
            if (diseaseDetailsVOList.size() < 20) {
                int needAdd = 20 - diseaseDetailsVOList.size();
                for (int i = 0; i < needAdd; i++) {
                    DiseaseDetailsVO emptyVO = DiseaseDetailsVO.builder()
                            .direction("")
                            .stakeNo("")
                            .defectItem("")
                            .eventName("")
                            .unit("")
                            .quantity("")
                            .description("")
                            .build();
                    diseaseDetailsVOList.add(emptyVO);
                }
            }
            return success(diseaseDetailsVOList);
        }
    }


    /**
     * 新增巡查日志
     */
    @ApiOperation("新增巡查日志")
    //@RequiresPermissions("patrol:inspectionLogs:add")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody PatrolInspectionLogs patrolInspectionLogs) {
        try {
            PatrolInspectionLogs result = patrolInspectionLogsService.savePatrolInspectionLogs(patrolInspectionLogs);
            return success(result);
        } catch (Exception e) {
            log.error("新增巡查日志失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 针对较差网络环境下的巡查日志新增
     * 通过车牌号精确查询已有记录，根据时间条件优先返回已存在的记录
     */
    @ApiOperation("较差网络环境下新增巡查日志")
    @PostMapping("/addInPoorNetwork")
    public AjaxResult addInPoorNetwork(@RequestBody PatrolInspectionLogs patrolInspectionLogs) {
        try {
            PatrolInspectionLogs result = patrolInspectionLogsService.addPatrolInspectionLogsInPoorNetwork(patrolInspectionLogs);
            return success(result);
        } catch (Exception e) {
            log.error("较差网络环境下新增巡查日志失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 修改巡查日志
     */
    @ApiOperation("修改巡查日志")
    //@RequiresPermissions("patrol:inspectionLogs:edit")
    @PutMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody PatrolInspectionLogs patrolInspectionLogs) {
        try {
            boolean result = patrolInspectionLogsService.updatePatrolInspectionLogs(patrolInspectionLogs);
            return toAjax(result);
        } catch (Exception e) {
            log.error("修改巡查日志失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 批量新增
     *
     * @param patrolInspectionLogsList
     */
    @ApiOperation("批量新增")
    //@RequiresPermissions("patrol:inspectionLogs:addBatch")
    @PostMapping("/addBatch")
    public AjaxResult addBatch(@Validated @RequestBody List<PatrolInspectionLogs> patrolInspectionLogsList) {
        return success(patrolInspectionLogsService.insertBatch(patrolInspectionLogsList));
    }

    /**
     * 删除巡查日志
     */
    @ApiOperation("删除巡查日志")
    //@RequiresPermissions("patrol:inspectionLogs:remove")
    @DeleteMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(patrolInspectionLogsService.removeById(id));
    }

    /**
     * 导出巡查日志列表
     */
    @ApiOperation("导出巡查日志列表")
    //@RequiresPermissions("patrol:inspectionLogs:export")
    @Log(title = "巡查日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<PatrolInspectionLogs> list = patrolInspectionLogsService.list();
        ExcelUtil<PatrolInspectionLogs> util = new ExcelUtil<>(PatrolInspectionLogs.class);
        util.exportExcel(response, list, "巡查日志数据");
    }

    /**
     * 根据条件批量导出巡查日志列表
     */
    @ApiOperation("根据条件批量导出巡查日志列表")
    //@RequiresPermissions("patrol:inspectionLogs:export")
    @Log(title = "巡查日志", businessType = BusinessType.EXPORT)
    @PostMapping("/exportByCondition")
    public void exportByCondition(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        long startTime = System.currentTimeMillis();

        Integer isEvent = MapUtil.getInt(params, "isEvent");
        if (null != isEvent) {
            List<String> ids = remoteRoadDiseaseService.getRoadDiseaseIdList().getData();
            if (isEvent == 1) params.put("idList", ids);
            else params.put("idListNot", ids);
        }

        R<List<String>> r = remoteMaintenanceSectionService.findUserMaintenanceIds(null);
        if (r.getCode() != 200) {
            log.error("获取用户养护路段失败: {}", r.getMsg());
            return;
        }

        params.put("sectionIdList", r.getData());
        params.put("order", "l.collect_time desc");

        // 不使用分页，获取所有符合条件的数据
        List<PatrolInspectionLogs> list = patrolInspectionLogsService.findListByParam(params);
        ExcelUtil<PatrolInspectionLogs> util = new ExcelUtil<>(PatrolInspectionLogs.class);
        util.exportExcel(response, list, "巡查日志数据");


        // try {
        //     // 分批处理设置事件数量
        //     int maxConcurrent = 20; // 最大并发数
        //     for (int i = 0; i < list.size(); i += maxConcurrent) {
        //         int endIndex = Math.min(i + maxConcurrent, list.size());
        //         List<PatrolInspectionLogs> batch = list.subList(i, endIndex);

        //         List<CompletableFuture<Void>> batchFutures = new ArrayList<>();
        //         for (PatrolInspectionLogs tempPatrolInspectionLog : batch) {
        //             CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
        //                 List<RemoteRoadDiseaseResponse> data = remoteRoadDiseaseService
        //                         .getRoadDiseaseList(tempPatrolInspectionLog.getId()).getData();
        //                 tempPatrolInspectionLog.setDiseaseNum(0);
        //                 if (CollectionUtil.isNotEmpty(data))
        //                     tempPatrolInspectionLog.setDiseaseNum(data.size());
        //             }, executor);
        //             batchFutures.add(future);
        //         }

        //         // 等待当前批次完成
        //         CompletableFuture.allOf(batchFutures.toArray(new CompletableFuture[0])).get();
        //     }

        //     // 导出Excel
        //     ExcelUtil<PatrolInspectionLogs> util = new ExcelUtil<>(PatrolInspectionLogs.class);
        //     util.exportExcel(response, list, "巡查日志数据");

        //     long endTime = System.currentTimeMillis();
        //     log.info("导出接口耗时: {}ms，共导出{}条数据", (endTime - startTime), list.size());
        // } catch (Exception e) {
        //     log.error("导出巡查日志数据失败", e);
        // }
    }

    @ApiOperation("当前用户的巡查详情")
    @GetMapping("/patrolDetail")
    public AjaxResult patrolDetail() {
        LocalDateTime startTime = LocalDateTime.now().toLocalDate().atTime(0, 0, 0);
        LocalDateTime endTime = LocalDateTime.now().toLocalDate().atTime(23, 59, 59);
        InspectionStatsVO inspectionStatsVO = patrolInspectionLogsService.patrolDetail(startTime, endTime);
        return success(inspectionStatsVO);
    }

    @ApiOperation("当前用户的巡查、检查完成情况")
    @GetMapping("/patrolMonthlyDetail")
    public CommonResult<InspectionStatsVO> patrolMonthlyDetail() {
        InspectionStatsVO inspectionStatsVO = patrolInspectionLogsService.patrolDetailNew();
        return CommonResult.success(inspectionStatsVO);
    }

    @ApiOperation("根据管理处ID获取巡查、检查完成情况")
    @GetMapping("/patrolDeptMonthlyDetail")
    public CommonResult<InspectionStatsVO> patrolDeptMonthlyDetail(@RequestParam("managementMaintenanceId") String managementMaintenanceId, @RequestParam("year") Integer year, @RequestParam("month") Integer month) {
        // 路面巡查列表和这里统计不一致原因可能是：列表是根据管养单位直接查，这里是根据管养单位下面的路段来查询，导致结果不一致
        InspectionStatsVO inspectionStatsVO = patrolInspectionLogsService.patrolDetailNew(managementMaintenanceId, year, month);
        return CommonResult.success(inspectionStatsVO);
    }

    @ApiOperation("当前用户按部门统计巡查详情")
    @GetMapping("/patrolDetailByDepartments")
    public AjaxResult patrolDetailByDepartments() {
        LocalDateTime startTime = LocalDateTime.now().toLocalDate().atTime(0, 0, 0);
        LocalDateTime endTime = LocalDateTime.now().toLocalDate().atTime(23, 59, 59);
        List<InspectionStatsVO> inspectionStatsVOList = patrolInspectionLogsService.patrolDetailByDepartments(startTime, endTime);
        return success(inspectionStatsVOList);
    }

    @ApiOperation("获取当前用户的前后两个月的巡查详情")
    @GetMapping("/findByDoubleTime")
    public AjaxResult findByDoubleTime() {
        CompletableFuture<Map<String, String>> mainIdToDeptName = CompletableFuture.supplyAsync(() -> {
            R<Map<String, String>> mainIdToDeptNameMapR = remoteMaintenanceSectionService.getMaintenanceSectionDeptNameMap();
            if (mainIdToDeptNameMapR.getCode() != 200) {
                return new HashMap<>();
            }
            return mainIdToDeptNameMapR.getData();
        });
        // preTime和nowDate分别是上个月的第一天和这个月的最后一天
        InspectionType type = InspectionType.TUNNEL_REGULAR_INSPECTION;
        LocalDateTime lastTime = LocalDateTime.now().minusMonths(1).withDayOfMonth(1);
        LocalDateTime nextTime = LocalDateTime.now().withDayOfMonth(LocalDateTime.now().toLocalDate().lengthOfMonth());

        // 构建请求对象
        AssetBaseDataRequest nextRequest = AssetBaseDataRequest.builder()
                .type(type)
                .checkTime(nextTime)
                .dataRule(true)
                .build();
        baseCacheService.setDeptIds(nextRequest);

        AssetBaseDataRequest lastRequest = new AssetBaseDataRequest();
        BeanUtils.copyProperties(nextRequest, lastRequest);
        lastRequest.setCheckTime(lastTime);

        Map<String, String> mainIdToDeptNameMap = mainIdToDeptName.join();
        // 并行获取数据
        CompletableFuture<Map<String, List<PatrolAssetCheck>>> nextFuture = CompletableFuture.supplyAsync(
                () -> patrolAssetCheckService.selectPatrolAssetCheck(
                                nextRequest, null, null, new AtomicInteger())
                        .stream()
                        .filter(p -> p.getMaintenanceSectionId() != null &&
                                mainIdToDeptNameMap.containsKey(p.getMaintenanceSectionId()))
                        .collect(Collectors.groupingBy(
                                p -> mainIdToDeptNameMap.get(p.getMaintenanceSectionId())
                        ))
        );

        CompletableFuture<Map<String, List<PatrolAssetCheck>>> lastFuture = CompletableFuture.supplyAsync(
                () -> patrolAssetCheckService.selectPatrolAssetCheck(
                                lastRequest, null, null, new AtomicInteger())
                        .stream()
                        .filter(p -> p.getMaintenanceSectionId() != null &&
                                mainIdToDeptNameMap.containsKey(p.getMaintenanceSectionId()))
                        .collect(Collectors.groupingBy(
                                p -> mainIdToDeptNameMap.get(p.getMaintenanceSectionId())
                        ))
        );
        Map<String, List<PatrolAssetCheck>> nextMap = nextFuture.join();
        Map<String, List<PatrolAssetCheck>> lastMap = lastFuture.join();

        List<InspectionStatsVO> inspectionStatsVOList = Stream.concat(lastMap.keySet().stream(), nextMap.keySet().stream())
                .distinct()
                .parallel()
                .map(key -> {
                    InspectionStatsVO stats = new InspectionStatsVO();
//                    stats.setManagementMaintenanceId(key);
                    stats.setManagementMaintenanceName(key);
                    stats.setLastInspectedCount(lastMap.containsKey(key) ? lastMap.get(key).size() : 0);
                    stats.setNextInspectedCount(nextMap.containsKey(key) ? nextMap.get(key).size() : 0);
                    return stats;
                })
                .sorted(Comparator
                        .comparing(InspectionStatsVO::getNextInspectedCount).reversed()
                        .thenComparing(InspectionStatsVO::getLastInspectedCount).reversed()
                )
                .collect(Collectors.toList());

        // 合并两个Map的keys并使用并行流处理
        return success(inspectionStatsVOList);
    }

    @ApiOperation("数据驾驶舱 隧道养护专题 隧道基础信息")
    @GetMapping("/tunnelBaseInfo")
    public AjaxResult tunnelBaseInfo(@RequestParam String assetId) {
        // 获取当前月份的第一天
        LocalDateTime startTime = LocalDateTime.now().
                toLocalDate().withDayOfMonth(1).atTime(0, 0, 0);
        // 下个月的第一天
        LocalDateTime endTime = LocalDateTime.now().
                toLocalDate().plusMonths(1).
                withDayOfMonth(1).atTime(0, 0, 0);
        AssetBaseDataRequest request = AssetBaseDataRequest.builder()
                .assetId(assetId)
                .assetType(AssetType.TUNNEL)
                .checkStartTime(startTime)
                .checkEndTime(endTime)
                .dataRule(true)
                .build();
        return success(patrolAssetCheckService.assetCheckBaseInfo(request));
    }

    @ApiOperation("数据驾驶舱 隧道养护专题 隧道基础信息")
    @GetMapping("/bridgeBaseInfo")
    public AjaxResult bridgeBaseInfo(@RequestParam String assetId) {
        // 获取当前月份的第一天
        LocalDateTime startTime = LocalDateTime.now().
                toLocalDate().withDayOfMonth(1).atTime(0, 0, 0);
        // 下个月的第一天
        LocalDateTime endTime = LocalDateTime.now().
                toLocalDate().plusMonths(1).
                withDayOfMonth(1).atTime(0, 0, 0);
        AssetBaseDataRequest request = AssetBaseDataRequest.builder()
                .assetId(assetId)
                .assetType(AssetType.BRIDGE)
                .checkStartTime(startTime)
                .checkEndTime(endTime)
                .dataRule(true)
                .build();
        return success(patrolAssetCheckService.assetCheckBaseInfo(request));
    }

    @ApiOperation("年度巡检查询(单线程版)")
    @GetMapping("/patrolYearDetail/{year}")
    public AjaxResult patrolYearDetailSingle(@PathVariable("year") Integer year) {
        try {
            LocalDateTime startTime = LocalDateTime.of(year, 1, 1, 0, 0, 0);
            LocalDateTime endTime = LocalDateTime.of(year + 1, 1, 1, 0, 0, 0);

            // 构建请求对象
            AssetBaseDataRequest routeRequest = AssetBaseDataRequest.builder()
                    .checkStartTime(startTime)
                    .checkEndTime(endTime)
                    .build();
            patrolInspectionLogsService.setDeptIds(routeRequest);

            AssetBaseDataRequest bridgeRequest = AssetBaseDataRequest.builder()
                    .type(InspectionType.BRIDGE_REGULAR_INSPECTION)
                    .dataRule(true)
                    .isCheck(true)
                    .checkStartTime(startTime)
                    .checkEndTime(endTime)
                    .build();
            baseCacheService.setDeptIds(bridgeRequest);

            AssetBaseDataRequest culvertRequest = AssetBaseDataRequest.builder()
                    .type(InspectionType.CULVERT_REGULAR_INSPECTION)
                    .dataRule(true)
                    .isCheck(true)
                    .checkStartTime(startTime)
                    .checkEndTime(endTime)
                    .build();
            baseCacheService.setDeptIds(culvertRequest);

            AssetBaseDataRequest tunnelRequest = AssetBaseDataRequest.builder()
                    .type(InspectionType.TUNNEL_REGULAR_INSPECTION)
                    .dataRule(true)
                    .isCheck(true)
                    .checkStartTime(startTime)
                    .checkEndTime(endTime)
                    .build();
            baseCacheService.setDeptIds(tunnelRequest);

            // 顺序执行查询
            Integer bridgeCount = patrolAssetCheckService.countAssetCheckData(bridgeRequest);
            Integer culvertCount = patrolAssetCheckService.countAssetCheckData(culvertRequest);
            Integer tunnelCount = patrolAssetCheckService.countAssetCheckData(tunnelRequest);
            BigDecimal roadbedMileage = patrolInspectionLogsService.getTotalPatrolMileage(routeRequest);
            BigDecimal roadbedKilometer = roadbedMileage.divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_UP);
            // 构建返回结果
            InspectionCountVO inspectionCountVO = InspectionCountVO.builder()
                    .bridgeCount(bridgeCount)
                    .culvertCount(culvertCount)
                    .tunnelCount(tunnelCount)
                    .roadbedMileage(roadbedMileage.toString())
                    .build();

            return success(inspectionCountVO);

        } catch (Exception e) {
            log.error("年度巡检查询异常", e);
            return error("系统异常，请稍后重试");
        }
    }


    @ApiOperation("管理处分组查询年度巡检里程")
    @GetMapping("/patrolDeptGroupYear/{year}")
    public R<List<InspectionGroupCountVO>> patrolGroupByDeptYear(@PathVariable("year") Integer year) {
        try {
            return R.ok(patrolInspectionLogsService.deptPatrolMileageCount(year, 1));
        } catch (Exception e) {
            log.error("管理处分组查询年度巡检查询异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 各养护路段巡查里程统计
     */
    @ApiOperation(value = "各养护路段巡查里程统计")
    @GetMapping(value = "/getMaintenanceSectionIdMileage")
    public R<List<MileageCountVO>> getMaintenanceSectionIdMileage(@RequestParam("year") Integer year, @RequestParam(required = false, name = "maintenanceSectionId") String maintenanceSectionId) {
        try {
            return R.ok(patrolInspectionLogsService.getMaintenanceSectionIdMileage(year, maintenanceSectionId));
        } catch (Exception e) {
            log.error("各管理处巡查里程统计查询异常", e);
            return R.fail("系统异常，请稍后重试");
        }
    }

    @ApiOperation("按管理处统计巡查详情")
    @GetMapping("/patrolStats")
    public AjaxResult patrolStats() {
        LocalDateTime startTime = LocalDateTime.now().toLocalDate().atTime(0, 0, 0);
        LocalDateTime endTime = LocalDateTime.now().toLocalDate().atTime(23, 59, 59);
        InspectionStatsVO inspectionStatsVO = patrolInspectionLogsService.patrolDetail(startTime, endTime);
        return success(inspectionStatsVO);
    }

    @ApiOperation("当前待巡查清单")
    @GetMapping("/patrolList")
    public AjaxResult patrolList() {
        AssetBaseDataRequest bridgeRequest = AssetBaseDataRequest.builder()
                .type(InspectionType.BRIDGE_REGULAR_INSPECTION)
                .dataRule(true)
                .isCheck(false)
                .build();
        baseCacheService.setDeptIds(bridgeRequest);

        // 使用CompletableFuture并行执行
        CompletableFuture<List<InspectionInfoVO>> bridgeFuture = CompletableFuture.supplyAsync(() ->
                AssetConvertUtils.convertList(patrolAssetCheckService.selectPatrolAssetCheck(bridgeRequest, null, null, new AtomicInteger()), InspectionInfoVO.class)
        );

        AssetBaseDataRequest culverRequest = AssetBaseDataRequest.builder()
                .type(InspectionType.CULVERT_REGULAR_INSPECTION)
                .dataRule(true)
                .isCheck(false)
                .build();
        baseCacheService.setDeptIds(culverRequest);

        CompletableFuture<List<InspectionInfoVO>> culverFuture = CompletableFuture.supplyAsync(() ->
                AssetConvertUtils.convertList(patrolAssetCheckService.selectPatrolAssetCheck(culverRequest, null, null, new AtomicInteger()), InspectionInfoVO.class)
        );

        AssetBaseDataRequest tunnelRequest = AssetBaseDataRequest.builder()
                .type(InspectionType.TUNNEL_REGULAR_INSPECTION)
                .dataRule(true)
                .isCheck(false)
                .build();
        baseCacheService.setDeptIds(tunnelRequest);

        CompletableFuture<List<InspectionInfoVO>> tunnelFuture = CompletableFuture.supplyAsync(() ->
                AssetConvertUtils.convertList(patrolAssetCheckService.selectPatrolAssetCheck(tunnelRequest, null, null, new AtomicInteger()), InspectionInfoVO.class)
        );

        // 等待所有任务完成并合并结果
        List<InspectionInfoVO> inspectionInfoVOList = new ArrayList<>();
        inspectionInfoVOList.addAll(bridgeFuture.join());
        inspectionInfoVOList.addAll(culverFuture.join());
        inspectionInfoVOList.addAll(tunnelFuture.join());

        // 按照PatrolAssetCheck中的expireTime字段从小到大排序
        inspectionInfoVOList.sort(Comparator.comparing(
                InspectionInfoVO::getExpireTime,
                Comparator.nullsLast(Comparator.naturalOrder())
        ));

        // 最多返回10条数据
        if (inspectionInfoVOList.size() > 10) {
            inspectionInfoVOList = inspectionInfoVOList.subList(0, 10);
        }

        return success(inspectionInfoVOList);
    }

    /**
     * 获取指定养护路段和月份内有数据的日期列表
     *
     * @param maintenanceSectionId 养护路段ID
     * @param yearMonth            年月，格式为yyyy-MM
     * @return 日期列表
     */
    @GetMapping("/daysWithData")
    public AjaxResult getDaysWithData(@RequestParam(value = "maintenanceSectionId") String maintenanceSectionId,
                                      @RequestParam(value = "yearMonth") String yearMonth) {
        return success(patrolInspectionLogsService.getDaysWithData(maintenanceSectionId, yearMonth));
    }

    /**
     * 批量删除巡查记录
     *
     * @param ids 巡查记录id列表
     * @return 结果
     */
    @ApiOperation("批量删除巡查记录")
    //@RequiresPermissions("patrol:inspectionLogs:remove")
    @PostMapping("/deleteByIds")
    public AjaxResult deleteByIds(@RequestBody List<String> ids) {
        return success(patrolInspectionLogsService.deleteByIds(ids));
    }


    @ApiOperation("巡查图表统计信息(带权限)")
    @GetMapping("/statistics")
    public AjaxResult statistics(@RequestParam(name = "year", defaultValue = "2024") @ApiParam(name = "年份") String year) {

        R<List<SysDept>> deptListData = remoteDeptAuthService.getDeptList();

        R<List<String>> userMaintenanceIdsData = remoteMaintenanceSectionService.findUserMaintenanceIds(null);

        List<String> userMaintenanceIds = userMaintenanceIdsData.getData();

        List<Map<String, Object>> mapList = SqlRunner.db().selectList("SELECT\n" +
                "curdate() 'date',\n" +
                "t1.maintenance_unit_name maintenanceUnitName,\n" +
                "IFNULL(sum(t1.patrol_mileage)/1000,0)  patrolMileage\n" +
                "FROM\n" +
                "`patrol_inspection_logs` t1 \n" +
                "WHERE t1.start_time like CONCAT({0},'%')\n" +
                "and t1.maintenance_unit_name is not null \n" +
                "and t1.start_time like  CONCAT( curdate(), '%')\t\n" +
                "and t1.maintenance_section_id in {1} \n" +
                "GROUP BY t1.maintenance_unit_name\n" +
                "union all\n" +
                "SELECT\n" +
                "date_sub(curdate(),interval 1 day) 'date',\n" +
                "t1.maintenance_unit_name maintenanceUnitName,\n" +
                "IFNULL(sum(t1.patrol_mileage)/1000,0)  patrolMileage\n" +
                "FROM\n" +
                "`patrol_inspection_logs` t1 \n" +
                "WHERE t1.start_time like CONCAT({0},'%')\n" +
                "and t1.maintenance_unit_name is not null \n" +
                "and t1.start_time like  CONCAT( date_sub(curdate(),interval 1 day),'%')\n" +
                "and t1.maintenance_section_id in {1} \n" +
                "GROUP BY t1.maintenance_unit_name", year, userMaintenanceIds);


        List<SysDept> sysDeptList = deptListData.getData();

//        sysDeptList.stream().filter(item -> item.getDeptType())

        HashSet<String> dateSet = new HashSet<>();
        HashSet<String> maintenanceUnitNameSet = new HashSet<>();


        for (Map<String, Object> tempMap : mapList) {
            String maintenanceUnitName = MapUtil.getStr(tempMap, "maintenanceUnitName");
            if (StringUtils.isEmpty(maintenanceUnitName))
                continue;
            String date = MapUtil.getStr(tempMap, "date");
            dateSet.add(date);
            List<SysDept> collect = sysDeptList.stream().filter(i -> i.getDeptName().equals(maintenanceUnitName)).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                SysDept sysDept = collect.get(0);
                boolean equals = sysDept.getDeptType().equals(4);
                if (equals) {
                    List<SysDept> collect1 = sysDeptList.stream().filter(i -> i.getDeptId().equals(sysDept.getParentId())).collect(Collectors.toList());
                    if (!collect1.isEmpty()) {
                        String deptName = collect1.get(0).getDeptName();
                        tempMap.put("maintenanceUnitName", deptName);
                    }
                }
                maintenanceUnitNameSet.add((String) tempMap.get("maintenanceUnitName"));
            }
        }

        ArrayList<HashMap<String, Object>> hashMaps = new ArrayList<>();

        for (String tempDate : dateSet) {
            for (String tempMaintenance : maintenanceUnitNameSet) {

                Float reduce = mapList.stream().filter(i -> (MapUtil.getStr(i, "maintenanceUnitName").equals(tempMaintenance) && (MapUtil.getStr(i, "date").equals(tempDate)))).map(item1 -> MapUtil.getFloat(item1, "patrolMileage")).reduce(0f, (result, item) -> result + item);

                hashMaps.add(new HashMap<>() {{
                    this.put("date", tempDate);
                    this.put("maintenanceUnitName", tempMaintenance);
                    this.put("patrolMileage", reduce.floatValue());
                }});

            }

        }


//        return success(mapList);
        return success(hashMaps);
    }

    /**
     * 统计巡查日志数量
     */
    @ApiOperation("统计巡查日志数量")
    @PostMapping("/count")
    public AjaxResult countListByUser(@RequestBody Map<String, Object> params) {
        R<List<String>> r = remoteMaintenanceSectionService.findUserMaintenanceIds(null);
        if (r.getCode() != 200) {
            return AjaxResult.success(0);
        }
        params.put("sectionIdList", r.getData());
        Integer count = patrolInspectionLogsService.countListByUser(params);
        return AjaxResult.success(count);
    }

    /**
     * 导出日常巡查记录表
     */
    @ApiOperation("导出日常巡查记录表")
    @Log(title = "导出日常巡查记录表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportDailyPatrolReport")
    public void exportDailyPatrolReport(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        try {
            Map<String, String> signUrlMap = new HashMap<>();

            // 获取巡查日志记录数据
            List<PatrolInspectionLogs> reportData = patrolInspectionLogsService.exportReportCard(params, signUrlMap);
            if (reportData == null || reportData.isEmpty()) {
                throw new RuntimeException("未找到符合条件的巡查记录");
            }

            // 生成文件名
            String fileName = null;
            if (params.get("exportFileName") != null) {
                fileName = URLEncoder.encode(params.get("exportFileName").toString(), StandardCharsets.UTF_8);
            } else {
                StringBuilder sb = new StringBuilder();
                sb.append("日常巡查记录表");

                if (reportData.size() == 1) {
                    PatrolInspectionLogs record = reportData.get(0);

                    // 拼接文件名：路线编号_路段名称_巡查时间
                    if (record.getRouteCode() != null && !record.getRouteCode().isEmpty()) {
                        sb.append("_");
                        sb.append(record.getRouteCode());
                    }

                    if (record.getMaintenanceSectionName() != null && !record.getMaintenanceSectionName().isEmpty()) {
                        sb.append("_");
                        sb.append(record.getMaintenanceSectionName());
                    }

                    if (record.getCollectTime() != null) {
                        sb.append("_");
                        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMddHHmmss");
                        sb.append(sdf.format(record.getCollectTime()));
                    }
                } else {
                    sb.append("(");
                    sb.append(reportData.size());
                    sb.append("条_");
                    sb.append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    sb.append(")");
                }

                fileName = URLEncoder.encode(sb.toString(), StandardCharsets.UTF_8);
            }

            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            // 使用EasyExcel导出
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                // 创建ExcelWriter
                EasyExcel.write(outputStream)
                        .registerWriteHandler(new DailyPatrolExcelReportHandler(reportData, signUrlMap))
                        .sheet("日常巡查记录表")
                        .doWrite(new ArrayList<>()); // 空列表，因为DailyPatrolExcelReportHandler会处理数据绘制
            } catch (Exception e) {
                log.error("导出日常巡查记录表失败", e);
                throw new RuntimeException("导出失败: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("导出日常巡查记录表失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    @ApiOperation("统计巡查日志数量")
    @PostMapping("/exportCount")
    public void ExportCount(@RequestBody Map<String, Object> params, @RequestParam String type, HttpServletResponse response) throws ExecutionException, InterruptedException {

        List<PatrolInspectionLogsTotalVO> list = null;
        ExcelUtil<PatrolInspectionLogsTotalVO> util = null;
        if (type.equals("3")) {
            list = patrolInspectionLogsService.countByPatrolUnit(params);
            util = new ExcelUtil<>(PatrolInspectionLogsTotalVO.class);
        }

        if (type.equals("2")) {
            list = patrolInspectionLogsService.countByMaintenanceSubUnit(params);
            util = new ExcelUtil<>(PatrolInspectionLogsTotalVO.class);
            util.hideColumn("patrolUnitName");
        }

        if (type.equals("1")) {
            list = patrolInspectionLogsService.countByMaintenanceUnit(params);
            patrolInspectionLogsService.fillOtherFields(list);
            util = new ExcelUtil<>(PatrolInspectionLogsTotalVO.class);
            util.hideColumn("patrolUnitName", "maintenanceSubUnitName");
        }


        if (util != null && list != null) {
            util.exportExcel(response, list, "巡维统计数据");
        }

    }

    @ApiOperation("统计巡查日志数量")
    @PostMapping("/exportCountCard")
    public void ExportCountCard(@RequestBody Map<String, Object> params, @RequestParam String type, HttpServletResponse response) throws ExecutionException, InterruptedException {
//            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
//            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        if (type.equals("0")) {
//            List<PatrolInspectionLogs> logList = patrolInspectionLogsService.findListByParam(new HashMap<>() {{
//                this.put("ids", Arrays.asList(MapUtil.getStr(params, "ids").split(",")));
//            }});
            Map<String, String> signUrlMap = new HashMap<>();
            List<PatrolInspectionLogs> logList  = patrolInspectionLogsService.exportReportCard(new HashMap<>() {{
                this.put("ids", Arrays.asList(MapUtil.getStr(params, "ids").split(",")));
            }}, signUrlMap);
            List<RemoteRoadDiseaseResponse> eventList = remoteRoadDiseaseService.getRoadDiseaseListByIdList(Arrays.asList(MapUtil.getStr(params, "ids").split(","))).getData();
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                // 创建ExcelWriter
                EasyExcel.write(outputStream)
                        .registerWriteHandler(new ComprehensiveStatisticsReportHandler(logList, eventList, signUrlMap))
                        .sheet("日常巡查记录表")
                        .doWrite(new ArrayList<>()); // 空列表，因为DailyPatrolExcelReportHandler会处理数据绘制
            } catch (Exception e) {
                log.error("导出日常巡查记录表失败", e);
                throw new RuntimeException("导出失败: " + e.getMessage());
            }

        }
        if (type.equals("1")) {
            AssetBaseDataRequest request = new AssetBaseDataRequest();
            request.setType(InspectionType.BRIDGE_DAILY_INSPECTION);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            request.setCheckStartTime(LocalDateTime.parse(params.get("startTime") + " 00:00:00", formatter));
            request.setCheckEndTime(LocalDateTime.parse(params.get("startTime") + " 23:59:59", formatter));
            request.setCustomSqlCondition("pac.maintenance_section_name = '" + params.get("maintenanceSectionName") + "'");
//                baseCacheService.setDeptIds(request);
            AtomicInteger total = new AtomicInteger();
            Map<String, String> signUrlMap = new HashMap<>();
            List<PatrolAssetCheck> reportData = patrolAssetCheckService.exportReportCard(request, signUrlMap, null, null);
//            List<PatrolAssetCheck> patrolAssetChecks = patrolAssetCheckService.selectPatrolAssetCheck(request, null, null, total);
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                // 创建ExcelWriter
                EasyExcel.write(outputStream)
                        .registerWriteHandler(new BridgeDailyStatisticsReportHandler(reportData, signUrlMap))
                        .sheet("日常巡查记录表")
                        .doWrite(new ArrayList<>()); // 空列表，因为DailyPatrolExcelReportHandler会处理数据绘制
            } catch (Exception e) {
                log.error("导出日常巡查记录表失败", e);
                throw new RuntimeException("导出失败: " + e.getMessage());
            }

        }

        if (type.equals("2")) {
            AssetBaseDataRequest request = new AssetBaseDataRequest();
            request.setType(InspectionType.TUNNEL_DAILY_INSPECTION);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            request.setCheckStartTime(LocalDateTime.parse(params.get("startTime") + " 00:00:00", formatter));
            request.setCheckEndTime(LocalDateTime.parse(params.get("startTime") + " 23:59:59", formatter));
            request.setCustomSqlCondition("pac.maintenance_section_name = '" + params.get("maintenanceSectionName") + "'");
//                baseCacheService.setDeptIds(request);
            AtomicInteger total = new AtomicInteger();
            Map<String, String> signUrlMap = new HashMap<>();
            List<PatrolAssetCheck> reportData = patrolAssetCheckService.exportReportCard(request, signUrlMap, null, null);
//            List<PatrolAssetCheck> patrolAssetChecks = patrolAssetCheckService.selectPatrolAssetCheck(request, null, null, total);
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                // 创建ExcelWriter
                EasyExcel.write(outputStream)
                        .registerWriteHandler(new TunnelDailyStatisticsReportHandler(reportData, signUrlMap))
                        .sheet("日常巡查记录表")
                        .doWrite(new ArrayList<>()); // 空列表，因为DailyPatrolExcelReportHandler会处理数据绘制
            } catch (Exception e) {
                log.error("导出日常巡查记录表失败", e);
                throw new RuntimeException("导出失败: " + e.getMessage());
            }
        }
    }

    @ApiOperation("统计巡查日志数量")
    @PostMapping("/countByPatrolUnit")
    public TableDataInfo countByPatrolUnit(@RequestBody Map<String, Object> params) throws ExecutionException, InterruptedException {
        startPage();
        List<PatrolInspectionLogsTotalVO> list = patrolInspectionLogsService.countByPatrolUnit(params);

        return getDataTable(list);
    }

    @ApiOperation("统计巡查日志数量")
    @PostMapping("/countByMaintenanceSubUnit")
    public TableDataInfo countByMaintenanceSubUnit(@RequestBody Map<String, Object> params) throws ExecutionException, InterruptedException {
        startPage();
        List<PatrolInspectionLogsTotalVO> list = patrolInspectionLogsService.countByMaintenanceSubUnit(params);

        return getDataTable(list);
    }


    @ApiOperation("统计巡查日志数量")
    @PostMapping("/countByMaintenanceUnit")
    public TableDataInfo countByMaintenanceUnit(@RequestBody Map<String, Object> params) throws ExecutionException, InterruptedException {
//        startPage();
        List<PatrolInspectionLogsTotalVO> list = patrolInspectionLogsService.countByMaintenanceUnit(params);

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(200);
//        rspData.setRows(list.subList(((int)params.get("pageNum")-1) *(int)params.get("pageSize"), (int)params.get("pageNum")*(int)params.get("pageSize")));
        rspData.setRows(patrolInspectionLogsService.fillOtherFields(list.subList(((int) params.get("pageNum") - 1) * (int) params.get("pageSize"), Math.min((int) params.get("pageNum") * (int) params.get("pageSize"), list.size()))));
        rspData.setMsg("查询成功");
        rspData.setTotal(list.size());
        rspData.setPageNum(((Integer) params.get("pageNum")).longValue());
        rspData.setPageSize(((Integer) params.get("pageSize")).longValue());

        return rspData;
    }


}
