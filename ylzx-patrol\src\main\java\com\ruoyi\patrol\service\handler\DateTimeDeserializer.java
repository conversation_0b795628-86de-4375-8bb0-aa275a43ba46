package com.ruoyi.patrol.service.handler;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * 日期时间反序列化工具类
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
public class DateTimeDeserializer {

    /**
     * 将日期字符串反序列化为当天开始时间 (00:00:00)
     */
    public static class StartOfDayDeserializer extends JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String dateStr = p.getText();
            if (dateStr == null || dateStr.isEmpty()) {
                return null;
            }
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
            return date.atStartOfDay(); // 00:00:00
        }
    }

    /**
     * 将日期字符串反序列化为当天结束时间 (23:59:59)
     */
    public static class EndOfDayDeserializer extends JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String dateStr = p.getText();
            if (dateStr == null || dateStr.isEmpty()) {
                return null;
            }
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
            return date.atTime(LocalTime.MAX); // 23:59:59.999999999
        }
    }
    
    /**
     * 将日期字符串反序列化为指定日期，但保留当前时间（时分秒）
     */
    public static class CurrentTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String dateStr = p.getText();
            if (dateStr == null || dateStr.isEmpty()) {
                return null;
            }
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
            return date.atTime(LocalTime.now()); // 使用当前时间的时分秒
        }
    }
    
    /**
     * 将日期字符串反序列化为指定日期，并生成工作时间（9:00-17:00）之间的随机时间
     */
    public static class WorkHoursRandomDeserializer extends JsonDeserializer<LocalDateTime> {
        private static final Random random = new Random();
        private static final int START_HOUR = 9;  // 工作开始时间：9点
        private static final int END_HOUR = 17;   // 工作结束时间：17点
        
        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String dateStr = p.getText();
            if (dateStr == null || dateStr.isEmpty()) {
                return null;
            }
            
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
            
            // 生成9:00到17:00之间的随机时间
            int hour = START_HOUR + random.nextInt(END_HOUR - START_HOUR + 1);
            int minute = random.nextInt(60);
            int second = random.nextInt(60);
            
            return date.atTime(hour, minute, second);
        }
    }
} 