<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repote.mapper.RepoteAnnexMapper">

    <resultMap type="com.ruoyi.repote.domain.RepoteAnnex" id="RepoteAnnexResult">
            <result property="missionId" column="mission_id"/>
            <result property="addressId" column="address_id"/>
    </resultMap>

    <sql id="base_column">
 mission_id, address_id    </sql>

    <sql id="where_column">
        <if test="missionId != null and missionId != ''">
            AND mission_id = #{missionId}
        </if>
        <if test="missionIdLike != null and missionIdLike != ''">
            AND mission_id like CONCAT('%', #{missionIdLike}, '%')
        </if>
        <if test="addressId != null and addressId != ''">
            AND address_id = #{addressId}
        </if>
    </sql>

    <sql id="set_column">
            <if test="missionId != null">
                mission_id = #{missionId},
            </if>
            <if test="addressId != null">
                address_id = #{addressId},
            </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="RepoteAnnexResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_annex
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="RepoteAnnexResult">
        SELECT
        <include refid="base_column"/>
        FROM repote_annex
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

</mapper>