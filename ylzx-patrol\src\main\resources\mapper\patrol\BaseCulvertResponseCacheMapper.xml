<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.BaseCulvertResponseCacheMapper">
    <resultMap type="com.ruoyi.patrol.domain.BaseCulvertResponseCache" id="BaseCulvertResponseCacheMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="assetId" column="asset_id" jdbcType="VARCHAR"/>
        <result property="assetName" column="asset_name" jdbcType="VARCHAR"/>
        <result property="assetCode" column="asset_code" jdbcType="VARCHAR"/>
        <result property="routeId" column="route_id" jdbcType="VARCHAR"/>
        <result property="routeName" column="route_name" jdbcType="VARCHAR"/>
        <result property="routeCode" column="route_code" jdbcType="VARCHAR"/>
        <result property="maintenanceSectionName" column="maintenance_section_name" jdbcType="VARCHAR"/>
        <result property="managementMaintenanceName" column="management_maintenance_name" jdbcType="VARCHAR"/>
        <result property="managementMaintenanceBranchName" column="management_maintenance_branch_name" jdbcType="VARCHAR"/>
        <result property="centerStake" column="center_stake" jdbcType="NUMERIC"/>
        <result property="constructionStake" column="construction_stake" jdbcType="NUMERIC"/>
        <result property="unifiedMileageStake" column="unified_mileage_stake" jdbcType="NUMERIC"/>
        <result property="nationalNetworkStake" column="national_network_stake" jdbcType="NUMERIC"/>
        <result property="operationState" column="operation_state" jdbcType="VARCHAR"/>
        <result property="operationStateName" column="operation_state_name" jdbcType="VARCHAR"/>
        <result property="dailyInspectionStatus" column="daily_inspection_status" jdbcType="VARCHAR"/>
        <result property="regularInspectionStatus" column="regular_inspection_status" jdbcType="VARCHAR"/>
        <result property="longitude" column="longitude" jdbcType="NUMERIC"/>
        <result property="latitude" column="latitude" jdbcType="NUMERIC"/>
        <result property="ks" column="ks" jdbcType="VARCHAR"/>
        <result property="dayFrequency" column="day_frequency" jdbcType="INTEGER"/>
        <result property="monthFrequency" column="month_frequency" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="managementMaintenanceId" column="management_maintenance_id" jdbcType="VARCHAR"/>
        <result property="managementMaintenanceBranchId" column="management_maintenance_branch_id" jdbcType="VARCHAR"/>
        <result property="maintenanceSectionId" column="maintenance_section_id" jdbcType="VARCHAR"/>
        <result property="ifDataRule" column="if_data_rule" jdbcType="INTEGER"/>
        <result property="otherSelectWhereSql" column="other_select_where_sql" jdbcType="VARCHAR"/>
        <result property="pageNum" column="page_num" jdbcType="INTEGER"/>
        <result property="pageSize" column="page_size" jdbcType="INTEGER"/>
        <result property="orderByColumn" column="order_by_column" jdbcType="VARCHAR"/>
        <result property="isAsc" column="is_asc" jdbcType="VARCHAR"/>
        <result property="reasonable" column="reasonable" jdbcType="INTEGER"/>
        <result property="culvertCode" column="culvert_code" jdbcType="VARCHAR"/>
        <result property="culvertSpan" column="culvert_span" jdbcType="NUMERIC"/>
        <result property="culvertLength" column="culvert_length" jdbcType="NUMERIC"/>
        <result property="culvertHeight" column="culvert_height" jdbcType="NUMERIC"/>
        <result property="culvertType" column="culvert_type" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 定义基础列列表 -->
    <sql id="Base_Column_List">
        base_culvert_response_cache.id,
        base_culvert_response_cache.asset_id,
        base_culvert_response_cache.asset_name,
        base_culvert_response_cache.asset_code,
        base_culvert_response_cache.route_id,
        base_culvert_response_cache.route_name,
        base_culvert_response_cache.route_code,
        base_culvert_response_cache.maintenance_section_name,
        base_culvert_response_cache.management_maintenance_name,
        base_culvert_response_cache.management_maintenance_branch_name,
        base_culvert_response_cache.center_stake,
        base_culvert_response_cache.construction_stake,
        base_culvert_response_cache.unified_mileage_stake,
        base_culvert_response_cache.national_network_stake,
        base_culvert_response_cache.operation_state,
        base_culvert_response_cache.operation_state_name,
        base_culvert_response_cache.daily_inspection_status,
        base_culvert_response_cache.regular_inspection_status,
        base_culvert_response_cache.longitude,
        base_culvert_response_cache.latitude,
        base_culvert_response_cache.ks,
        base_culvert_response_cache.day_frequency,
        base_culvert_response_cache.month_frequency,
        base_culvert_response_cache.update_time,
        base_culvert_response_cache.management_maintenance_id,
        base_culvert_response_cache.management_maintenance_branch_id,
        base_culvert_response_cache.maintenance_section_id,
        base_culvert_response_cache.if_data_rule,
        base_culvert_response_cache.other_select_where_sql,
        base_culvert_response_cache.page_num,
        base_culvert_response_cache.page_size,
        base_culvert_response_cache.order_by_column,
        base_culvert_response_cache.is_asc,
        base_culvert_response_cache.reasonable,
        base_culvert_response_cache.culvert_code,
        base_culvert_response_cache.culvert_span,
        base_culvert_response_cache.culvert_length,
        base_culvert_response_cache.culvert_height,
        base_culvert_response_cache.culvert_type
    </sql>

    <!-- 定义公共的 WHERE 条件 -->
    <sql id="Common_Where_Clause">
        <where>
            <if test="request.assetId != null and request.assetId != ''">
                AND asset_id = #{request.assetId}
            </if>
            <if test="request.assetName != null and request.assetName != ''">
                AND asset_name LIKE CONCAT('%', #{request.assetName}, '%')
            </if>
            <if test="request.assetCode != null and request.assetCode != ''">
                AND asset_code LIKE CONCAT('%', #{request.assetCode}, '%')
            </if>
            <!-- 路线编码条件 -->
            <if test="request.routeCode != null and request.routeCode != ''">
                AND route_code = #{request.routeCode}
            </if>
            <if test="request.routeCodes != null and request.routeCodes.size() > 0">
                AND route_code IN
                <foreach collection="request.routeCodes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>

            <!-- 养护段条件 -->
            <if test="request.maintenanceSectionId != null and request.maintenanceSectionId != ''">
                AND maintenance_section_id = #{request.maintenanceSectionId}
            </if>
            <if test="request.maintenanceSectionIdList != null and request.maintenanceSectionIdList.size() > 0">
                AND maintenance_section_id IN
                <foreach collection="request.maintenanceSectionIdList" item="sectionId" open="(" separator="," close=")">
                    #{sectionId}
                </foreach>
            </if>

            <!-- 管理养护条件 -->
            <if test="request.managementMaintenanceId != null and request.managementMaintenanceId != ''">
                AND management_maintenance_id = #{request.managementMaintenanceId}
            </if>
            <if test="request.managementMaintenanceIds != null and request.managementMaintenanceIds.size() > 0">
                AND management_maintenance_id IN
                <foreach collection="request.managementMaintenanceIds" item="maintenanceId" open="(" separator="," close=")">
                    #{maintenanceId}
                </foreach>
            </if>

            <!-- ID条件 -->
            <if test="request.ids != null">
                <choose>
                    <when test="request.ids.size() > 0">
                        AND asset_id IN
                        <foreach collection="request.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1 = 0
                    </otherwise>
                </choose>
            </if>

            <!-- 排除ID条件 -->
            <if test="request.excludeIds != null and request.excludeIds.size() > 0">
                AND asset_id NOT IN
                <foreach collection="request.excludeIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 关键字搜索条件 -->
            <if test="request.ks != null and request.ks != ''">
                AND (asset_name LIKE CONCAT('%', #{request.ks}, '%')
                OR asset_code LIKE CONCAT('%', #{request.ks}, '%'))
            </if>
            <if test="request.isCheck != null and request.type != null and request.type.code in ('2', '4', '6')">
                <if test="request.isCheck == true">
                    AND stage = 1
                </if>
                <if test="request.isCheck == false">
                    AND stage = 0
                </if>
            </if>
            <if test="request.checkId != null and request.checkId != null">
                AND id = #{request.checkId}
            </if>
            <if test="request.checkIds != null and request.checkIds.size() > 0">
                AND id IN
                <foreach collection="request.checkIds" item="checkId" open="(" separator="," close=")">
                    #{checkId}
                </foreach>
            </if>
        </where>
    </sql>

    <!-- 查询总数 -->
    <select id="countAssetBaseData" resultType="int">
        SELECT COUNT(1)
        FROM base_culvert_response_cache
        <include refid="Common_Where_Clause"/>
    </select>

    <!-- 查询数据 -->
    <select id="selectAssetBaseData" resultMap="BaseCulvertResponseCacheMap">
        <choose>
            <!-- 当 offset 和 pageSize 不为空时，使用分页的 INNER JOIN 模式 -->
            <when test="offset != null and pageSize != null">
                SELECT 
                    <include refid="Base_Column_List"/>
                FROM (
                    SELECT id
                    FROM base_culvert_response_cache
                    <include refid="Common_Where_Clause"/>
                    ORDER BY 
                    <choose>
                        <when test="request.customSort != null and request.customSort != ''">
                            ${request.customSort}
                        </when>
                        <otherwise>
                            id ASC
                        </otherwise>
                    </choose>
                    LIMIT #{offset}, #{pageSize}
                ) AS t
                INNER JOIN base_culvert_response_cache ON base_culvert_response_cache.id = t.id
            </when>
            <!-- 当 offset 或 pageSize 为空时，使用普通的查询模式 -->
            <otherwise>
                SELECT 
                    <include refid="Base_Column_List"/>
                FROM base_culvert_response_cache
                <include refid="Common_Where_Clause"/>
                ORDER BY 
                <choose>
                    <when test="request.customSort != null and request.customSort != ''">
                        ${request.customSort}
                    </when>
                    <otherwise>
                        id ASC
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>

    <!-- 批量插入 -->
    <insert id="insertBatch">
        insert into base_culvert_response_cache(id,asset_id,asset_name,asset_code,route_id,route_name,route_code,maintenance_section_name,management_maintenance_name,management_maintenance_branch_name,center_stake,construction_stake,unified_mileage_stake,national_network_stake,operation_state,operation_state_name,daily_inspection_status,regular_inspection_status,longitude,latitude,ks,day_frequency,month_frequency,update_time,management_maintenance_id,management_maintenance_branch_id,maintenance_section_id,if_data_rule,other_select_where_sql,page_num,page_size,order_by_column,is_asc,reasonable,culvert_code,culvert_span,culvert_length,culvert_height,culvert_type)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.id},#{entity.assetId},#{entity.culvertType},#{entity.assetCode},#{entity.routeId},#{entity.routeName},#{entity.routeCode},#{entity.maintenanceSectionName},#{entity.managementMaintenanceName},#{entity.managementMaintenanceBranchName},#{entity.centerStake},#{entity.constructionStake},#{entity.unifiedMileageStake},#{entity.nationalNetworkStake},#{entity.operationState},#{entity.operationStateName},#{entity.dailyInspectionStatus},#{entity.regularInspectionStatus},#{entity.longitude},#{entity.latitude},#{entity.ks},#{entity.dayFrequency},#{entity.monthFrequency},#{entity.updateTime},#{entity.managementMaintenanceId},#{entity.managementMaintenanceBranchId},#{entity.maintenanceSectionId},#{entity.ifDataRule},#{entity.otherSelectWhereSql},#{entity.pageNum},#{entity.pageSize},#{entity.orderByColumn},#{entity.isAsc},#{entity.reasonable},#{entity.culvertCode},#{entity.culvertSpan},#{entity.culvertLength},#{entity.culvertHeight},#{entity.culvertType})
        </foreach>
    </insert>

    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch">
        insert into base_culvert_response_cache(id,asset_id,asset_name,asset_code,route_id,route_name,route_code,maintenance_section_name,management_maintenance_name,management_maintenance_branch_name,center_stake,construction_stake,unified_mileage_stake,national_network_stake,operation_state,operation_state_name,daily_inspection_status,regular_inspection_status,longitude,latitude,ks,day_frequency,month_frequency,update_time,management_maintenance_id,management_maintenance_branch_id,maintenance_section_id,if_data_rule,other_select_where_sql,page_num,page_size,order_by_column,is_asc,reasonable,culvert_code,culvert_span,culvert_length,culvert_height,culvert_type)
        values
        <foreach collection="entities" item="entity" separator=",">
           (#{entity.id},#{entity.assetId},#{entity.assetName},#{entity.assetCode},#{entity.routeId},#{entity.routeName},#{entity.routeCode},#{entity.maintenanceSectionName},#{entity.managementMaintenanceName},#{entity.managementMaintenanceBranchName},#{entity.centerStake},#{entity.constructionStake},#{entity.unifiedMileageStake},#{entity.nationalNetworkStake},#{entity.operationState},#{entity.operationStateName},#{entity.dailyInspectionStatus},#{entity.regularInspectionStatus},#{entity.longitude},#{entity.latitude},#{entity.ks},#{entity.dayFrequency},#{entity.monthFrequency},#{entity.updateTime},#{entity.managementMaintenanceId},#{entity.managementMaintenanceBranchId},#{entity.maintenanceSectionId},#{entity.ifDataRule},#{entity.otherSelectWhereSql},#{entity.pageNum},#{entity.pageSize},#{entity.orderByColumn},#{entity.isAsc},#{entity.reasonable},#{entity.culvertCode},#{entity.culvertSpan},#{entity.culvertLength},#{entity.culvertHeight},#{entity.culvertType})
        </foreach>
        on duplicate key update
        asset_id = values(asset_id),
        asset_name = values(asset_name),
        asset_code = values(asset_code),
        route_id = values(route_id),
        route_name = values(route_name),
        route_code = values(route_code),
        maintenance_section_name = values(maintenance_section_name),
        management_maintenance_name = values(management_maintenance_name),
        management_maintenance_branch_name = values(management_maintenance_branch_name),
        center_stake = values(center_stake),
        construction_stake = values(construction_stake),
        unified_mileage_stake = values(unified_mileage_stake),
        national_network_stake = values(national_network_stake),
        operation_state = values(operation_state),
        operation_state_name = values(operation_state_name),
        daily_inspection_status = values(daily_inspection_status),
        regular_inspection_status = values(regular_inspection_status),
        longitude = values(longitude),
        latitude = values(latitude),
        ks = values(ks),
        day_frequency = values(day_frequency),
        month_frequency = values(month_frequency),
        update_time = values(update_time),
        management_maintenance_id = values(management_maintenance_id),
        management_maintenance_branch_id = values(management_maintenance_branch_id),
        maintenance_section_id = values(maintenance_section_id),
        if_data_rule = values(if_data_rule),
        other_select_where_sql = values(other_select_where_sql),
        page_num = values(page_num),
        page_size = values(page_size),
        order_by_column = values(order_by_column),
        is_asc = values(is_asc),
        reasonable = values(reasonable),
        culvert_code = values(culvert_code),
        culvert_span = values(culvert_span),
        culvert_length = values(culvert_length),
        culvert_height = values(culvert_height),
        culvert_type = values(culvert_type)
    </insert>

</mapper>
