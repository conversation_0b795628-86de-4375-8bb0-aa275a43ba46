package com.ruoyi.repote.mapper;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.repote.domain.RepoteForm;
import org.apache.ibatis.annotations.Mapper;

/**
 * 填报格规范Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
@Mapper
public interface RepoteFormMapper extends BaseMapper<RepoteForm> {

    List<RepoteForm> findListByParam(Map<String, Object> params);


}
