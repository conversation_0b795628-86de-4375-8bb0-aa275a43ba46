<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patroltidb.mapper.PatrolDeviceCheckMapper">

    <resultMap type="com.ruoyi.patroltidb.domain.PatrolDeviceCheck" id="PatrolDeviceCheckResult">
        <result property="recordType" column="record_type"/>
        <result property="assetId" column="asset_id"/>
        <result property="assetName" column="asset_name"/>
        <result property="assetCode" column="asset_code"/>
        <result property="routeCode" column="route_code"/>
        <result property="routeName" column="route_name"/>
        <result property="domainId" column="domain_id"/>
        <result property="checkTime" column="check_time"/>
        <result property="weather" column="weather"/>
        <result property="checkerId" column="checker_id"/>
        <result property="checker" column="checker"/>
        <result property="recorderId" column="recorder_id"/>
        <result property="recorder" column="recorder"/>
        <result property="maker" column="maker"/>
        <result property="reviewer" column="reviewer"/>
        <result property="approver" column="approver"/>
        <result property="remark" column="remark"/>
        <result property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="expiry" column="expiry"/>
        <result property="frequency" column="frequency"/>
        <result property="stage" column="stage"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="base_column">id, create_by, create_time, update_by, update_time, expiry, frequency, stage, type, del_flag,
        record_type, asset_id, asset_name, asset_code, route_code, route_name, domain_id, check_time, weather, checker_id, checker, recorder_id, recorder, maker, reviewer, approver, remark    </sql>

    <sql id="base_column_c">c.id, c.create_by, c.create_time, c.update_by, c.update_time, c.expiry, c.frequency, c.stage, c.type, c.del_flag,
        c.record_type, c.asset_id, c.asset_name, c.asset_code, c.route_code, c.route_name, c.domain_id, c.check_time, c.weather, c.checker_id, c.checker, c.recorder_id, c.recorder, c.maker, c.reviewer, c.approver, c.remark    </sql>

    <sql id="where_column">
        <if test="recordType != null and recordType != ''">
            AND record_type = #{recordType}
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag = #{delFlag}
        </if>
        <if test="expiry != null and expiry != ''">
            AND expiry = #{expiry}
        </if>
        <if test="frequency != null and frequency != ''">
            AND frequency = #{frequency}
        </if>
        <if test="stage != null and stage != ''">
            AND stage = #{stage}
        </if>
        <if test="assetId != null and assetId != ''">
            AND asset_id = #{assetId}
        </if>
        <if test="assetIdLike != null and assetIdLike != ''">
            AND asset_id like CONCAT('%', #{assetIdLike}, '%')
        </if>
        <if test="assetName != null and assetName != ''">
            AND asset_name = #{assetName}
        </if>
        <if test="assetCode != null and assetCode != ''">
            AND asset_code = #{assetCode}
        </if>
        <if test="routeCode != null and routeCode != ''">
            AND route_code = #{routeCode}
        </if>
        <if test="routeName != null and routeName != ''">
            AND route_name = #{routeName}
        </if>
        <if test="domainId != null and domainId != ''">
            AND domain_id = #{domainId}
        </if>
        <if test="checkTime != null and checkTime != ''">
            AND check_time = #{checkTime}
        </if>
        <if test="oprTimes != null and oprTimes != ''">
            AND date_format(check_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(#{oprTimes}, '%Y-%m-%d')
        </if>
        <if test="oprTimee != null and oprTimee != ''">
            AND date_format(check_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(#{oprTimee}, '%Y-%m-%d')
        </if>
        <if test="weather != null and weather != ''">
            AND weather = #{weather}
        </if>
        <if test="checkerId != null and checkerId != ''">
            AND checker_id = #{checkerId}
        </if>
        <if test="checker != null and checker != ''">
            AND checker = #{checker}
        </if>
        <if test="recorderId != null and recorderId != ''">
            AND recorder_id = #{recorderId}
        </if>
        <if test="recorder != null and recorder != ''">
            AND recorder = #{recorder}
        </if>
        <if test="maker != null and maker != ''">
            AND maker = #{maker}
        </if>
        <if test="reviewer != null and reviewer != ''">
            AND reviewer = #{reviewer}
        </if>
        <if test="approver != null and approver != ''">
            AND approver = #{approver}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}
        </if>
    </sql>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="PatrolDeviceCheckResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_device_check
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="PatrolDeviceCheckResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_device_check
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
<!--        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">-->
<!--            limit #{pageNum},#{pageSize}-->
<!--        </if>-->
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findDetailListByParam" resultType="com.ruoyi.patroltidb.domain.PatrolDeviceCheckDetail">
        SELECT
            d.id, c.create_by, c.create_time, c.update_by, c.update_time,
            c.record_type, c.asset_id, c.asset_name, c.asset_code, c.route_code, c.route_name, c.domain_id, c.check_time, c.weather, c.checker_id, c.checker, c.recorder_id, c.recorder, c.maker, c.reviewer, c.approver, c.remark
            ,d.dev_name
            ,d.location
            ,d.content
            ,d.result
            ,d.describe
            ,d.measures
            ,d.pic_paths
            ,d.car_license
            ,d.nums
        FROM patrol_device_check_detail as d
        left join patrol_device_check AS c on c.id = d.check_id
        <where>
            c.id IS NOT NULL
            <include refid="where_column_c"/>
        </where>
        order by c.check_time desc
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findDetailListByParamCount" resultType="long">
        select
            count(1)
        from (
            SELECT
                d.id
            FROM patrol_device_check_detail as d
            left join patrol_device_check AS c on c.id = d.check_id
            <where>
                c.id IS NOT NULL
                <include refid="where_column_c"/>
            </where>
            order by c.check_time desc
        ) m
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findFaultListByParam" resultType="com.ruoyi.patroltidb.domain.PatrolDeviceFaultRecord">
        SELECT
            d.id, c.create_by, c.create_time, c.update_by, c.update_time,
            c.record_type, c.asset_id, c.asset_name, c.asset_code, c.route_code, c.route_name, c.domain_id, c.check_time, c.weather, c.checker_id, c.checker, c.recorder_id, c.recorder, c.maker, c.reviewer, c.approver
            ,d.check_id
            , d.dev_name
            , d.location
            , d.fault_location
            , d.`describe`
            , d.measures
            , d.code
            , d.report_time
            , d.repair_time
            , d.remark
            , d.repair_flag
        FROM patrol_device_fault_record as d
        left join patrol_device_check AS c on c.id = d.check_id
        <where>
            c.id IS NOT NULL
            <include refid="where_column_c"/>
        </where>
        order by c.check_time desc
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findFaultListByParamCount" resultType="long">
        select
            count(1)
        from (
            SELECT
                d.id
            FROM patrol_device_fault_record as d
            left join patrol_device_check AS c on c.id = d.check_id
            <where>
                c.id IS NOT NULL
                <include refid="where_column_c"/>
            </where>
            order by c.check_time desc
        ) m
    </select>

    <sql id="where_column_c">
        <if test="id != null and id != ''">
            AND d.id = #{id}
        </if>
        <if test="ids != null and ids.size > 0">
            AND d.id IN
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="recordType != null and recordType != ''">
            AND c.record_type = #{recordType}
        </if>
        <if test="assetId != null and assetId != ''">
            AND c.asset_id = #{assetId}
        </if>
        <if test="tunnelIdList != null and tunnelIdList.size() > 0">
            AND c.asset_id IN
            <foreach collection="tunnelIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
<!--        <if test="assetName != null and assetName != ''">-->
<!--            AND c.asset_name = #{assetName}-->
<!--        </if>-->
        <if test="assetNameLike != null and assetNameLike != ''">
            AND c.asset_name like CONCAT('%', #{assetNameLike}, '%')
        </if>
        <if test="assetCode != null and assetCode != ''">
            AND c.asset_code = #{assetCode}
        </if>
        <if test="checkTime != null and checkTime != ''">
            AND c.check_time = #{checkTime}
        </if>
        <if test="oprTimes != null and oprTimes != ''">
            AND date_format(c.check_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(#{oprTimes}, '%Y-%m-%d')
        </if>
        <if test="oprTimee != null and oprTimee != ''">
            AND date_format(c.check_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(#{oprTimee}, '%Y-%m-%d')
        </if>
        <if test="domainId != null and domainId != ''">
            AND c.domain_id = #{domainId}
        </if>
        <if test="domainIds != null and domainIds != ''">
            AND c.domain_id IN (${domainIds})
        </if>
        <if test="routeCodes != null and routeCodes != ''">
            AND c.route_code IN (${routeCodes})
        </if>
        <if test="routeName != null and routeName != ''">
            AND c.route_name = #{routeName}
        </if>
    </sql>

    <!-- 故障月报统计 -->
    <select id="listMonthlyReport" resultType="com.ruoyi.patroltidb.domain.excel.DeviceMonthlyReportExport">
        SELECT
            MAX(asset_id) asset_id,
            MAX(asset_name) asset_name,
            MAX(asset_code) asset_code,
            MAX(route_code) route_code,
            MAX(route_name) route_name,
            MAX(domain_id) domain_id,
            YEAR(check_time) AS year,
            MONTH(check_time) AS month,
            (SELECT COUNT(1) FROM patrol_device_fault_record WHERE check_id = patrol_device_check.id) faultNum
        FROM
            patrol_device_check
        WHERE
            record_type = 3
        <if test="year != null and year != ''">
            AND YEAR(check_time) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND MONTH(check_time) = #{month}
        </if>
        <if test="tunnelIdList != null and tunnelIdList.size() > 0">
            AND asset_id IN
            <foreach collection="tunnelIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="assetId != null and assetId != ''">
            AND asset_id = #{assetId}
        </if>
        <if test="assetName != null and assetName != ''">
            AND asset_name = #{assetName}
        </if>
        <if test="assetCode != null and assetCode != ''">
            AND asset_code = #{assetCode}
        </if>
        GROUP BY
            YEAR(check_time),MONTH(check_time),asset_code
        ORDER BY
            YEAR(check_time) DESC,MONTH(check_time) DESC,asset_id
    </select>

    <!-- 故障月报统计 -->
    <select id="getByAssetIdFault" resultType="com.ruoyi.patroltidb.domain.PatrolDeviceFaultRecord">
        SELECT
            dc.asset_id,
            dc.asset_name,
            dc.asset_code,
            dc.route_code,
            dc.route_name,
            dc.domain_id,
            YEAR(dc.check_time) AS YEAR,
            MONTH(dc.check_time) AS MONTH,
            dfr.dev_name,
            dfr.location,
            dfr.fault_location,
            dfr.`describe`,
            dfr.measures,
            dfr.`code`,
            dfr.report_time,
            dfr.repair_time,
            dfr.repair_flag
        FROM
            patrol_device_fault_record dfr
        LEFT JOIN patrol_device_check dc ON dfr.check_id = dc.id
        WHERE
            dc.record_type = 3
        <if test="year != null and year != ''">
            AND YEAR(check_time) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND MONTH(dc.check_time) = #{month}
        </if>
        <if test="tunnelIdList != null and tunnelIdList.size() > 0">
            AND dc.asset_id IN
            <foreach collection="tunnelIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="assetId != null and assetId != ''">
            AND dc.asset_id = #{assetId}
        </if>
        <if test="assetName != null and assetName != ''">
            AND dc.asset_name = #{assetName}
        </if>
        <if test="assetCode != null and assetCode != ''">
            AND dc.asset_code = #{assetCode}
        </if>
    </select>

</mapper>