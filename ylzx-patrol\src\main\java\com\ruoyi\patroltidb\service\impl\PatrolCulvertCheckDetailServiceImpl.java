package com.ruoyi.patroltidb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.datasource.annotation.Slave;
import com.ruoyi.patroltidb.domain.PatrolCulvertCheckDetail;
import com.ruoyi.patroltidb.mapper.PatrolCulvertCheckDetailMapper;
import com.ruoyi.patroltidb.service.PatrolCulvertCheckDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 涵洞巡检查子表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Service
@Slave
public class PatrolCulvertCheckDetailServiceImpl extends ServiceImpl<PatrolCulvertCheckDetailMapper, PatrolCulvertCheckDetail> implements PatrolCulvertCheckDetailService {

//    @Autowired
//    private PatrolCulvertCheckDetailMapper patrolCulvertCheckDetailMapper;

    @Override
    public List<PatrolCulvertCheckDetail> findListByParam(Map<String, Object> params) {
        return baseMapper.findListByParam(params);
    }


}
