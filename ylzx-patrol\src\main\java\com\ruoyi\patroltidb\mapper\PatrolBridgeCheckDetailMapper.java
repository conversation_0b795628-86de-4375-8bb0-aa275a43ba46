package com.ruoyi.patroltidb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.patroltidb.domain.PatrolBridgeCheckDetail;
import com.ruoyi.patroltidb.domain.TestFileImg;

import java.util.List;
import java.util.Map;

/**
 * 桥梁巡检查子Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface PatrolBridgeCheckDetailMapper extends BaseMapper<PatrolBridgeCheckDetail> {

    List<PatrolBridgeCheckDetail> findListByParam(Map<String, Object> params);

    List<TestFileImg> findFileImgList(Map<String, Object> params);

    Long batchUpdate(Map<String, Object> params);


}
