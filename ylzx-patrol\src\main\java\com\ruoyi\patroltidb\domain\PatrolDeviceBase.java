package com.ruoyi.patroltidb.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: sfc
 * @date: 2024年11月06日 16:48
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="资产巡检查基础表")
@Data
@NoArgsConstructor
public class PatrolDeviceBase extends BaseTableEntity {

    /** 检查ID */
//    @Excel(name = "检查ID")
    @ApiModelProperty(value = "检查ID")
    private String checkId;

    /** 设备名称 */
    @Excel(name = "设备名称", sort = 10)
    @ApiModelProperty(value = "设备名称")
    private String devName;

    /** 位置 */
    @Excel(name = "检查位置", sort = 11)
    @ApiModelProperty(value = "位置")
    private String location;

    /** 描述 */
    @Excel(name = "故障的原因及内容", sort = 13)
    @ApiModelProperty(value = "描述")
    @TableField("`describe`")
    private String describe;

    /** 措施 */
    @Excel(name = "应急措施", sort = 14)
    @ApiModelProperty(value = "措施")
    private String measures;


    /** 记录类型（1经常检查，2日常巡查，3故障记录，4故障月报） */
    @TableField(exist = false)
    private Long recordType;

    /** 资产id */
    @TableField(exist = false)
    private String assetId;

    /** 资产名称 */
    @Excel(name = "隧道名称", type = Excel.Type.EXPORT, sort = 4)
    @TableField(exist = false)
    private String assetName;

    /** 资产编码 */
    @Excel(name = "隧道编码", type = Excel.Type.EXPORT, sort = 3)
    @TableField(exist = false)
    private String assetCode;

    /** 路线编码 */
    @Excel(name = "路线编码", type = Excel.Type.EXPORT, sort = 1)
    @TableField(exist = false)
    private String routeCode;

    /** 路线名称 */
    @Excel(name = "路线名称", type = Excel.Type.EXPORT, sort = 2)
    @TableField(exist = false)
    private String routeName;

    /** 管养单位id */
    @TableField(exist = false)
    private Long domainId;

    /** 管养单位名称 */
    @Excel(name = "管养单位", type = Excel.Type.EXPORT, sort = 5)
    @TableField(exist = false)
    private String deptName;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date checkTime;

    /** 天气 */
    @Excel(name = "天气", sort = 7)
    @TableField(exist = false)
    private String weather;

    /** 检查人Id */
    @TableField(exist = false)
    private Long checkerId;

    /** 检查人 */
    @Excel(name = "检查人", sort = 8)
    @TableField(exist = false)
    private String checker;

    /** 记录人Id */
    @TableField(exist = false)
    private Long recorderId;

    /** 记录人 */
    @Excel(name = "记录人", sort = 9)
    @TableField(exist = false)
    private String recorder;

    /** 检查人签名 */
    @TableField(exist = false)
    private String checkerUrl;

    /** 记录人签名 */
    @TableField(exist = false)
    private String recorderUrl;

    /** 检查人签名List */
    @TableField(exist = false)
    private List<String> kahunaSignList;

    /** 记录人签名List */
    @TableField(exist = false)
    private List<String> oprUserSignList;

    /** 制表 */
    @TableField(exist = false)
    private String maker;

    /** 复核 */
    @TableField(exist = false)
    private String reviewer;

    /** 审定 */
    @TableField(exist = false)
    private String approver;
}
